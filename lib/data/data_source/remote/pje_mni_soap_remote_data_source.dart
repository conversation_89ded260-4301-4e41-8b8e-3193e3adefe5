import 'dart:async';

// import 'package:buffer/buffer.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:retrofit/retrofit.dart';
import 'package:tjcemobile/app/flavor.dart';
import 'package:tjcemobile/app/shared/utils/pje_parser/pje_parser_utils.dart';
import 'package:tjcemobile/data/model/pje_general_response_model.dart';
import 'package:tjcemobile/data/model/pje_process_complete_model.dart';
import 'package:tjcemobile/data/network/dio_factory.dart';
import 'package:tjcemobile/data/network/pje_mni_soap_service/pje_mni_soap_service.dart';
import 'package:tjcemobile/data/response_parser/responses_parser.dart';
import 'package:tjcemobile/domain/viewmodel/pje_process_mni_viewmodel.dart';

//coverage:ignore-file
abstract class PjeMniSoapRemoteDataSource {
  Future<PjeProcessCompleteModel> postProcessMNI(
    String process, {
    PjeLoginViewModel? pjeLoginViewModel,
    bool useBaseUri2G = false,
    bool includeHeader = false,
    bool includeMovements = false,
    bool includeDocuments = false,
    String? documentId,
  });

  Future<PjeGeneralResponseModel> postCourt({
    bool useBaseUri2G = false,
  });

  Future<PjeGeneralResponseModel> postCourtClass(
    String arg0, {
    bool useBaseUri2G = false,
  });

  Future<PjeGeneralResponseModel> postCourtMatter(
    String arg0,
    String arg1, {
    bool useBaseUri2G = false,
  });

  Future<PjeGeneralResponseModel> postCompetence(
    String arg0,
    String arg1,
    String arg2, {
    bool useBaseUri2G = false,
  });
}

class PjeMniSoapRemoteDataSourceImpl implements PjeMniSoapRemoteDataSource {
  @override
  Future<PjeProcessCompleteModel> postProcessMNI(
    String process, {
    PjeLoginViewModel? pjeLoginViewModel,
    bool useBaseUri2G = false,
    bool includeHeader = false,
    bool includeMovements = false,
    bool includeDocuments = false,
    String? documentId,
  }) async {
    HttpResponse<dynamic> content;

    var flavorValues = FlavorConfig.instance.values;

    var username = flavorValues.pjeConsultantUser;
    var password = flavorValues.pjeConsultantPassword;

    Map<String, dynamic> data = {
      "name": "ser:consultarProcesso",
      "attributes": {},
      "children": [
        {
          "name": "tip:idConsultante",
          "attributes": {},
          "children": [pjeLoginViewModel?.consultantId ?? username],
        },
        {
          "name": "tip:senhaConsultante",
          "attributes": {},
          "children": [pjeLoginViewModel?.consultantPassword ?? password],
        },
        {
          "name": "tip:numeroProcesso",
          "attributes": {},
          "children": [process],
        },
      ],
    };

    if (includeHeader) {
      data["children"]?.add({
        "name": "tip:incluirCabecalho",
        "attributes": {},
        "children": ["true"],
      });
    } else {
      data["children"]?.add({
        "name": "tip:incluirCabecalho",
        "attributes": {},
        "children": ["false"],
      });
    }

    if (includeMovements) {
      data["children"]?.add(
        {
          "name": "tip:movimentos",
          "attributes": {},
          "children": ["true"],
        },
      );
    }

    if (includeDocuments) {
      data["children"]?.add(
        {
          "name": "tip:incluirDocumentos",
          "attributes": {},
          "children": ["true"],
        },
      );
    }

    if (documentId != null) {
      data["children"]?.add(
        {
          "name": "tip:documento",
          "attributes": {},
          "children": [documentId],
        },
      );
    }

    var raw = PjeParserUtils.mountPrimaryXml(data);

    if (useBaseUri2G) {
      content = await this.service2G.postProcessMNI(raw);
    } else {
      content = await this.service1G.postProcessMNI(raw);
    }

    if (content.response.data == null) {
      throw ErrorDescription("Não foi possível localizar o processo.");
    }

    var map = await PjeParserUtils.parseMultipartRelated(
      content.response,
      file: documentId != null,
    );

    if (!map.containsKey('soap') || !map.containsKey('file')) {
      throw ErrorDescription("Não foi possível obter o conteúdo.");
    }

    var xmlParsed = PjeParserUtils.parsePrimaryXml(map['soap']['body']);

    if (xmlParsed.containsKey("mensagem") &&
        xmlParsed["mensagem"] ==
            "Erro ao realizar login via MNI. exception invoking: loginFailed") {
      throw ErrorDescription("Erro ao realizar Login.");
    }

    if (xmlParsed.containsKey("sucesso") && xmlParsed["sucesso"] == false) {
      throw ErrorDescription("Falha ao obter o processo  solicitado.");
    }

    if (documentId != null) {
      xmlParsed['processo']['documento'][0]['conteudo']['include'] =
          map['file']['body'];
    }

    data = {
      "status_code": content.response.statusCode,
      "data": xmlParsed,
    };

    ResponsesParser parser = ResponsesParser(data);
    return parser.parsePjeProcessCompleteModelInBackground();
  }

  @override
  Future<PjeGeneralResponseModel> postCourt({
    bool useBaseUri2G = false,
  }) async {
    HttpResponse<dynamic> content;

    var raw = PjeParserUtils.mountSecondaryXml({
      "name": "ws:consultarJurisdicoes",
      "attributes": {},
      "children": [],
    });

    if (useBaseUri2G) {
      content = await this.service2G.postConsultPJe(raw);
    } else {
      content = await this.service1G.postConsultPJe(raw);
    }

    var xml = PjeParserUtils.parseSecondaryXml(content.response.data);

    Map<String, dynamic> data = {
      "status_code": content.response.statusCode,
      "data": xml,
    };

    ResponsesParser parser = ResponsesParser(data);
    return parser.parsePjeGeneralResponseModelInBackground();
  }

  @override
  Future<PjeGeneralResponseModel> postCourtClass(
    String arg0, {
    bool useBaseUri2G = false,
  }) async {
    HttpResponse<dynamic> content;

    List<Map<String, dynamic>> args = [
      {
        "name": "id",
        "value": arg0,
      }
    ];

    var raw = PjeParserUtils.mountSecondaryXml(
      {
        "name": "ws:consultarClassesJudiciais",
        "attributes": {},
        "children": null,
      },
      args: args,
    );

    if (useBaseUri2G) {
      content = await this.service2G.postConsultPJe(raw);
    } else {
      content = await this.service1G.postConsultPJe(raw);
    }

    var xml = PjeParserUtils.parseSecondaryXml(content.response.data);

    Map<String, dynamic> data = {
      "status_code": content.response.statusCode,
      "data": xml,
    };

    ResponsesParser parser = ResponsesParser(data);
    return parser.parsePjeGeneralResponseModelInBackground();
  }

  @override
  Future<PjeGeneralResponseModel> postCourtMatter(
    String arg0,
    String arg1, {
    bool useBaseUri2G = false,
  }) async {
    HttpResponse<dynamic> content;

    List<Map<String, dynamic>> args = [
      {
        "name": "id",
        "value": arg0,
      },
      {
        "name": "codigo",
        "value": arg1,
      }
    ];

    var raw = PjeParserUtils.mountSecondaryXml(
      {
        "name": "ws:consultarAssuntosJudiciais",
        "attributes": {},
        "children": null,
      },
      args: args,
    );

    if (useBaseUri2G) {
      content = await this.service2G.postConsultPJe(raw);
    } else {
      content = await this.service1G.postConsultPJe(raw);
    }

    var xml = PjeParserUtils.parseSecondaryXml(content.response.data);

    Map<String, dynamic> data = {
      "status_code": content.response.statusCode,
      "data": xml,
    };

    ResponsesParser parser = ResponsesParser(data);
    return parser.parsePjeGeneralResponseModelInBackground();
  }

  @override
  Future<PjeGeneralResponseModel> postCompetence(
    String arg0,
    String arg1,
    String arg2, {
    bool useBaseUri2G = false,
  }) async {
    HttpResponse<dynamic> content;

    List<Map<String, dynamic>> args = [
      {
        "name": "id",
        "value": arg0,
      },
      {
        "name": "codigo",
        "value": arg1,
      },
      {
        "name": "codigo",
        "value": arg2,
      },
    ];

    var raw = PjeParserUtils.mountSecondaryXml(
      {
        "name": "ws:consultarCompetencias",
        "attributes": {},
        "children": null,
      },
      args: args,
    );

    if (useBaseUri2G) {
      content = await this.service2G.postConsultPJe(raw);
    } else {
      content = await this.service1G.postConsultPJe(raw);
    }

    var xml = PjeParserUtils.parseSecondaryXml(content.response.data);

    Map<String, dynamic> data = {
      "status_code": content.response.statusCode,
      "data": xml,
    };

    ResponsesParser parser = ResponsesParser(data);
    return parser.parsePjeGeneralResponseModelInBackground();
  }

  PjeMniSoapService get service1G {
    return PjeMniSoapService(
      Get.find<DioFactory>().getDio(),
      baseUrl: FlavorConfig.instance.values.pjeMni1GBaseUrl,
    );
  }

  PjeMniSoapService get service2G {
    return PjeMniSoapService(
      Get.find<DioFactory>().getDio(),
      baseUrl: FlavorConfig.instance.values.pjeMni2GBaseUrl,
    );
  }
}
