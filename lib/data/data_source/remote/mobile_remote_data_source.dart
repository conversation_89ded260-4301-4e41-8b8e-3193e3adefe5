import 'package:retrofit/retrofit.dart';
import 'package:tjcemobile/data/model/auth_token_model.dart';
import 'package:tjcemobile/data/model/response_interceptor_model.dart';
import 'package:tjcemobile/data/network/mobile_service/mobile_service.dart';
import 'package:tjcemobile/data/response_parser/responses_parser.dart';
import 'package:tjcemobile/domain/viewmodel/auth_token_viewmodel.dart';
import 'package:tjcemobile/domain/viewmodel/logger_client_viewmodel.dart';

abstract class MobileRemoteDataSource {
  Future<AuthTokenModel> postAuthToken(AuthTokenViewModel viewModel);

  Future<ResponseInterceptorModel> postLoggerClientError(
      LoggerClientViewModel viewModel,
      );
}

class MobileRemoteDataSourceImpl implements MobileRemoteDataSource {
  MobileService _service;

  MobileRemoteDataSourceImpl(this._service);

  @override
  Future<AuthTokenModel> postAuthToken(AuthTokenViewModel viewModel) async {
    HttpResponse<dynamic> content = await this._service.postAuthToken(
          viewModel,
        );

    Map<String, dynamic> data = {
      'status_code': content.response.statusCode,
      'data': content.response.data,
    };

    ResponsesParser parser = ResponsesParser(data);
    return parser.parseAuthTokenModelInBackground();
  }

  @override
  Future<ResponseInterceptorModel> postLoggerClientError(
      LoggerClientViewModel viewModel,
      ) async {
    HttpResponse<dynamic> content =
    await this._service.postLoggerClientError(viewModel);

    Map<String, dynamic> data = {
      'status_code': content.response.statusCode,
      'data': content.response.data,
    };

    ResponsesParser parser = ResponsesParser(data);
    return parser.parseResponseInterceptorModelInBackground();
  }
}
