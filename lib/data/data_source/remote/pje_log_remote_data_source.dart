import 'package:retrofit/retrofit.dart';
import 'package:tjcemobile/data/model/pje_log_model.dart';
import 'package:tjcemobile/data/network/pje_log_service/pje_log_service.dart';
import 'package:tjcemobile/data/response_parser/responses_parser.dart';
import 'package:tjcemobile/domain/viewmodel/pje_log_viewmodel.dart';

abstract class PjeLogRemoteDataSource {
  Future<PjeLogModel> postPjeLog(
    PjeLogViewModel? viewModel,
  );
}

class PjeLogRemoteDataSourceImpl implements PjeLogRemoteDataSource {
  PjeLogService _service;

  PjeLogRemoteDataSourceImpl(this._service);

  @override
  Future<PjeLogModel> postPjeLog(PjeLogViewModel? viewModel) async {
    HttpResponse<dynamic> content = await this._service.postPjeLog(
          viewModel,
        );

    Map<String, dynamic> data = {
      'status_code': content.response.statusCode,
      'data': content.response.data,
    };

    ResponsesParser parser = ResponsesParser(data);
    return parser.parsePjeLogModelInBackground();
  }
}
