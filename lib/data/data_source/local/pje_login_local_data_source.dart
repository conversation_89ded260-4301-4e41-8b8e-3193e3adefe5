import 'package:get/get.dart';
import 'package:tjcemobile/app/app_preferences.dart';
import 'package:tjcemobile/data/network/error_handler.dart';
import 'package:tjcemobile/domain/viewmodel/pje_process_mni_viewmodel.dart';

const PJE_LOGIN_KEY = "PJE_LOGIN_KEY";

abstract class PjeLoginLocalDataSource {
  Future<void> setPjeLoginViewModel(
    PjeLoginViewModel value,
  );

  Future<List<PjeLoginViewModel>> getPjeLoginViewModelList();

  Future<void> removeFromDataSource();
}

class PjeLoginLocalDataSourceImpl implements PjeLoginLocalDataSource {
  AppPreferences _appPreferences = Get.find<AppPreferences>();

  @override
  Future<void> setPjeLoginViewModel(PjeLoginViewModel value) async {
    Map<String, dynamic> response = await this._getter();
    List<PjeLoginViewModel> list = [];

    if (response[PJE_LOGIN_KEY] != null &&
        response[PJE_LOGIN_KEY] is List<dynamic>) {
      List<dynamic> data = response[PJE_LOGIN_KEY];

      list = data.map((e) => PjeLoginViewModel.fromJson(e)).toList();

      bool hasDegree =
          list.any((PjeLoginViewModel e) => (e.degree == value.degree));

      if (hasDegree) {
        list.removeWhere((PjeLoginViewModel e) => e.degree == value.degree);
      }

      list.add(value);
    } else {
      list.add(value);
    }

    var json = list.map((PjeLoginViewModel e) => e.toJson()).toList();
    response[PJE_LOGIN_KEY] = json;

    await this._setter(response);
  }

  @override
  Future<List<PjeLoginViewModel>> getPjeLoginViewModelList() async {
    Map<String, dynamic> response = await this._getter();
    List<dynamic>? list = response[PJE_LOGIN_KEY];

    if (list == null) return [];

    if (list.length > 0) {
      return list.map((e) => PjeLoginViewModel.fromJson(e)).toList();
    }

    throw ErrorHandler.handle(DataSource.CACHE_ERROR);
  }

  @override
  Future<void> removeFromDataSource() async {
    Map<String, dynamic> response = await this._getter();

    response[PJE_LOGIN_KEY] = null;

    await this._setter(response);
  }

  Future<Map<String, dynamic>> _getter() async {
    try {
      return await this._appPreferences.getLocalData();
    } catch (e) {
      throw ErrorHandler.handle(DataSource.CACHE_ERROR);
    }
  }

  Future<bool> _setter(Map<String, dynamic> value) async {
    try {
      return await this._appPreferences.setLocalData(value);
    } catch (e) {
      throw ErrorHandler.handle(DataSource.CACHE_ERROR);
    }
  }
}
