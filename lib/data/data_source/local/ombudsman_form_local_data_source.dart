import 'package:get/get.dart';
import 'package:tjcemobile/app/app_preferences.dart';
import 'package:tjcemobile/data/network/error_handler.dart';
import 'package:tjcemobile/domain/viewmodel/actuation_form_viewmodel.dart';

const OMBUDSMAN_FORM_KEY = "OMBUDSMAN_FORM_KEY";

abstract class OmbudsmanFormLocalDataSource {
  Future<void> setActuationFormViewModelList(ActuationFormViewModel value);

  Future<List<ActuationFormViewModel>> getActuationFormViewModelList();

  Future<void> removeFromDataSource();
}

class OmbudsmanFormLocalDataSourceImpl implements OmbudsmanFormLocalDataSource {
  AppPreferences _appPreferences = Get.find<AppPreferences>();

  @override
  Future<void> setActuationFormViewModelList(
      ActuationFormViewModel value) async {
    Map<String, dynamic> response = await this._getter();
    List<ActuationFormViewModel> list = [];

    if (response[OMBUDSMAN_FORM_KEY] != null &&
        response[OMBUDSMAN_FORM_KEY] is List<dynamic>) {
      List<dynamic> data = response[OMBUDSMAN_FORM_KEY];

      list = data.map((e) => ActuationFormViewModel.fromJson(e)).toList();

      if (list.length > 0) list = [];

      list.add(value);
    } else {
      list.add(value);
    }

    List<Map<String, dynamic>> json =
        list.map((ActuationFormViewModel e) => e.toJson()).toList();
    response[OMBUDSMAN_FORM_KEY] = json;

    await this._setter(response);
  }

  @override
  Future<List<ActuationFormViewModel>> getActuationFormViewModelList() async {
    Map<String, dynamic> response = await this._getter();
    List<dynamic>? list = response[OMBUDSMAN_FORM_KEY];

    if (list == null || list.isEmpty) return [];

    if (list.length > 0) {
      return list.map((e) => ActuationFormViewModel.fromJson(e)).toList();
    }

    throw ErrorHandler.handle(DataSource.CACHE_ERROR);
  }

  @override
  Future<void> removeFromDataSource() async {
    Map<String, dynamic> response = await this._getter();
    response[OMBUDSMAN_FORM_KEY] = null;

    await this._setter(response);
  }

  Future<Map<String, dynamic>> _getter() async {
    try {
      return await this._appPreferences.getLocalData();
    } catch (e) {
      throw ErrorHandler.handle(DataSource.CACHE_ERROR);
    }
  }

  Future<bool> _setter(Map<String, dynamic> value) async {
    try {
      return await this._appPreferences.setLocalData(value);
    } catch (e) {
      throw ErrorHandler.handle(DataSource.CACHE_ERROR);
    }
  }
}
