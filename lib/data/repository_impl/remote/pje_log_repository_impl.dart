import 'package:dartz/dartz.dart';
import 'package:tjcemobile/data/data_source/remote/pje_log_remote_data_source.dart';
import 'package:tjcemobile/data/model/pje_log_model.dart';
import 'package:tjcemobile/data/network/error_handler.dart';
import 'package:tjcemobile/data/network/failure.dart';
import 'package:tjcemobile/domain/repository/remote/pje_log_repository.dart';
import 'package:tjcemobile/domain/viewmodel/pje_log_viewmodel.dart';

class PjeLogRepositoryImpl extends PjeLogRepository {
  PjeLogRemoteDataSource _remoteDataSource;

  PjeLogRepositoryImpl(this._remoteDataSource);

  @override
  Future<Either<Failure, PjeLogModel>> postPjeLog(
      PjeLogViewModel? viewModel) async {
    try {
      final PjeLogModel response =
          await this._remoteDataSource.postPjeLog(viewModel);

      if (response.statusCode == ResponseCode.CREATED) {
        return Right(response);
      } else {
        return Left(
          Failure(
            response.statusCode ?? ApiInternalStatus.FAILURE,
            response.message ?? ResponseMessage.DEFAULT,
          ),
        );
      }
    } catch (error) {
      return (Left(ErrorHandler.handle(error).failure));
    }
  }
}
