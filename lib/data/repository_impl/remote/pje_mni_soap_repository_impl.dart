import 'package:dartz/dartz.dart';
import 'package:flutter/widgets.dart';
import 'package:tjcemobile/data/data_source/remote/pje_mni_soap_remote_data_source.dart';
import 'package:tjcemobile/data/model/pje_general_response_model.dart';
import 'package:tjcemobile/data/model/pje_process_complete_model.dart';
import 'package:tjcemobile/data/network/error_handler.dart';
import 'package:tjcemobile/data/network/failure.dart';
import 'package:tjcemobile/domain/repository/remote/pje_mni_soap_repository.dart';
import 'package:tjcemobile/domain/viewmodel/pje_process_mni_viewmodel.dart';

class PjeMniSoapRepositoryImpl extends PjeMniSoapRepository {
  PjeMniSoapRemoteDataSource _remoteDataSource;

  PjeMniSoapRepositoryImpl(this._remoteDataSource);

  @override
  Future<Either<Failure, PjeProcessCompleteModel>> postProcessMNI(
    String process, {
    PjeLoginViewModel? pjeLoginViewModel,
    bool useBaseUri2G = false,
    bool includeHeader = false,
    bool includeMovements = false,
    bool includeDocuments = false,
    String? documentId,
  }) async {
    try {
      final PjeProcessCompleteModel response =
          await this._remoteDataSource.postProcessMNI(
                process,
                pjeLoginViewModel: pjeLoginViewModel,
                useBaseUri2G: useBaseUri2G,
                includeHeader: includeHeader,
                includeMovements: includeMovements,
                includeDocuments: includeDocuments,
                documentId: documentId,
              );

      return Right(response);
    } catch (error) {
      return (Left(ErrorHandler.handle(error).failure));
    }
  }

  @override
  Future<Either<Failure, PjeGeneralResponseModel>> postCourt({
    bool useBaseUri2G = false,
  }) async {
    try {
      final PjeGeneralResponseModel response =
          await this._remoteDataSource.postCourt(useBaseUri2G: useBaseUri2G);

      return Right(response);
    } catch (error) {
      return (Left(ErrorHandler.handle(error).failure));
    }
  }

  @override
  Future<Either<Failure, PjeGeneralResponseModel>> postCourtClass(
    String arg0, {
    bool useBaseUri2G = false,
  }) async {
    try {
      final PjeGeneralResponseModel response =
          await this._remoteDataSource.postCourtClass(
                arg0,
                useBaseUri2G: useBaseUri2G,
              );

      return Right(response);
    } catch (error) {
      return (Left(ErrorHandler.handle(error).failure));
    }
  }

  @override
  Future<Either<Failure, PjeGeneralResponseModel>> postCourtMatter(
    String arg0,
    String arg1, {
    bool useBaseUri2G = false,
  }) async {
    try {
      final PjeGeneralResponseModel response =
          await this._remoteDataSource.postCourtMatter(
                arg0,
                arg1,
                useBaseUri2G: useBaseUri2G,
              );

      return Right(response);
    } catch (error) {
      return (Left(ErrorHandler.handle(error).failure));
    }
  }

  @override
  Future<Either<Failure, PjeGeneralResponseModel>> postCompetence(
    String arg0,
    String arg1,
    String arg2, {
    bool useBaseUri2G = false,
  }) async {
    try {
      final PjeGeneralResponseModel response =
          await this._remoteDataSource.postCompetence(
                arg0,
                arg1,
                arg2,
                useBaseUri2G: useBaseUri2G,
              );

      return Right(response);
    } catch (error) {
      return (Left(ErrorHandler.handle(error).failure));
    }
  }
}
