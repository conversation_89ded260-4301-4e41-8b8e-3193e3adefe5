import 'package:dartz/dartz.dart';
import 'package:tjcemobile/data/data_source/remote/mobile_remote_data_source.dart';
import 'package:tjcemobile/data/model/auth_token_model.dart';
import 'package:tjcemobile/data/model/response_interceptor_model.dart';
import 'package:tjcemobile/data/network/error_handler.dart';
import 'package:tjcemobile/data/network/failure.dart';
import 'package:tjcemobile/domain/repository/remote/mobile_repository.dart';
import 'package:tjcemobile/domain/viewmodel/auth_token_viewmodel.dart';
import 'package:tjcemobile/domain/viewmodel/logger_client_viewmodel.dart';

class MobileRepositoryImpl extends MobileRepository {
  MobileRemoteDataSource _remoteDataSource;

  MobileRepositoryImpl(this._remoteDataSource);

  @override
  Future<Either<Failure, AuthTokenModel>> postAuthToken(
    AuthTokenViewModel viewModel,
  ) async {
    try {
      final AuthTokenModel response =
          await this._remoteDataSource.postAuthToken(viewModel);

      if (response.statusCode == ResponseCode.SUCCESS) {
        return Right(response);
      } else {
        return Left(
          Failure(
            response.statusCode ?? ApiInternalStatus.FAILURE,
            response.message ?? ResponseMessage.DEFAULT,
          ),
        );
      }
    } catch (error) {
      return (Left(ErrorHandler.handle(error).failure));
    }
  }

  @override
  Future<Either<Failure, ResponseInterceptorModel>> postLoggerClientError(
    LoggerClientViewModel viewModel,
  ) async {
    try {
      final ResponseInterceptorModel response =
          await this._remoteDataSource.postLoggerClientError(viewModel);

      if (response.statusCode == ResponseCode.SUCCESS) {
        return Right(response);
      } else {
        return Left(
          Failure(
            response.statusCode ?? ApiInternalStatus.FAILURE,
            response.message ?? ResponseMessage.DEFAULT,
          ),
        );
      }
    } catch (error) {
      return (Left(ErrorHandler.handle(error).failure));
    }
  }
}
