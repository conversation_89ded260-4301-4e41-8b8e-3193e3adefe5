import 'package:dartz/dartz.dart';
import 'package:tjcemobile/data/data_source/local/pje_login_local_data_source.dart';
import 'package:tjcemobile/data/network/error_handler.dart';
import 'package:tjcemobile/data/network/failure.dart';
import 'package:tjcemobile/domain/repository/local/pje_login_local_repository.dart';
import 'package:tjcemobile/domain/viewmodel/pje_process_mni_viewmodel.dart';

class PjeLoginLocalRepositoryImpl extends PjeLoginLocalRepository {
  PjeLoginLocalDataSource _localDataSource;

  PjeLoginLocalRepositoryImpl(this._localDataSource);

  @override
  Future<Either<Failure, void>> setPjeLoginViewModel(
    PjeLoginViewModel viewModel,
  ) async {
    try {
      return Right(
        await this._localDataSource.setPjeLoginViewModel(viewModel),
      );
    } catch (error) {
      return (Left(ErrorHandler.handle(error).failure));
    }
  }

  @override
  Future<Either<Failure, List<PjeLoginViewModel>>>
      getPjeLoginViewModelList() async {
    try {
      return Right(await this._localDataSource.getPjeLoginViewModelList());
    } catch (error) {
      return (Left(ErrorHandler.handle(error).failure));
    }
  }

  @override
  Future<Either<Failure, void>> removeFromDataSource() async {
    try {
      return Right(
        await this._localDataSource.removeFromDataSource(),
      );
    } catch (error) {
      return (Left(ErrorHandler.handle(error).failure));
    }
  }
}
