import 'package:dartz/dartz.dart';
import 'package:tjcemobile/data/data_source/local/ombudsman_form_local_data_source.dart';
import 'package:tjcemobile/data/network/error_handler.dart';
import 'package:tjcemobile/data/network/failure.dart';
import 'package:tjcemobile/domain/repository/local/ombudsman_form_local_repository.dart';
import 'package:tjcemobile/domain/viewmodel/actuation_form_viewmodel.dart';

class OmbudsmanFormLocalRepositoryImpl extends OmbudsmanFormLocalRepository {
  OmbudsmanFormLocalDataSource _localDataSource;

  OmbudsmanFormLocalRepositoryImpl(this._localDataSource);

  @override
  Future<Either<Failure, void>> setActuationFormViewModelList(
    ActuationFormViewModel viewModel,
  ) async {
    try {
      return Right(
        await this._localDataSource.setActuationFormViewModelList(viewModel),
      );
    } catch (error) {
      return (Left(ErrorHandler.handle(error).failure));
    }
  }

  @override
  Future<Either<Failure, List<ActuationFormViewModel>>>
      getActuationFormViewModelList() async {
    try {
      return Right(await this._localDataSource.getActuationFormViewModelList());
    } catch (error) {
      return (Left(ErrorHandler.handle(error).failure));
    }
  }

  @override
  Future<Either<Failure, void>> removeFromDataSource() async {
    try {
      return Right(
        await this._localDataSource.removeFromDataSource(),
      );
    } catch (error) {
      return (Left(ErrorHandler.handle(error).failure));
    }
  }
}
