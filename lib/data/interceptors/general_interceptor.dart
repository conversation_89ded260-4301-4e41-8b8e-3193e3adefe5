import 'package:dio/dio.dart';
import 'package:tjcemobile/app/shared/utils/snackbar_utils.dart';
import 'package:tjcemobile/data/network/error_handler.dart';

class GeneralInterceptor extends Interceptor {
  @override
  void onError(DioException error, ErrorInterceptorHandler handler) async {
    if (error.type.index == ResponseCode.NO_INTERNET_CONNECTION) {
      var failure = ErrorHandler.handle(error).failure;

      SnackbarUtils.error(failure.title, failure.message ?? "");

      return handler.reject(error);
    }

    return handler.next(error);
  }
}
