import 'package:dio/dio.dart';
import 'package:get/get.dart' as getX;
import 'package:jwt_decoder/jwt_decoder.dart';
import 'package:tjcemobile/app/app_preferences.dart';
import 'package:tjcemobile/app/flavor.dart';
import 'package:tjcemobile/app/shared/utils/snackbar_utils.dart';
import 'package:tjcemobile/data/model/auth_token_model.dart';
import 'package:tjcemobile/data/network/error_handler.dart';
import 'package:tjcemobile/domain/viewmodel/auth_token_viewmodel.dart';

class SeiInterceptor extends Interceptor {
  Future<Response<dynamic>?> _refreshToken(
    RequestOptions requestOptions,
  ) async {
    var authorization = requestOptions.headers['Authorization'];

    if (authorization == null || authorization.isEmpty) {
      final appPreferences = getX.Get.find<AppPreferences>();

      var flavorValues = FlavorConfig.instance.values;

      var viewModel = AuthTokenViewModel(
        user: flavorValues.userSei,
        password: flavorValues.passwordSei,
      );

      try {
        Response response = await Dio().post(
          flavorValues.seiBaseUrl + "/auth/token",
          data: viewModel.toJson(),
        );

        if (response.statusCode == ResponseCode.SUCCESS) {
          Map<String, dynamic> data = {
            'status_code': response.statusCode,
            'data': response.data,
          };

          AuthTokenModel model = AuthTokenModel.fromJson(data);

          await appPreferences.setAuthorizationSei(data: model.toJson());

          requestOptions.headers['Authorization'] = model.data?.token;

          Duration timeout = const Duration(minutes: 1);

          return await Dio(
            BaseOptions(connectTimeout: timeout, receiveTimeout: timeout),
          ).fetch(requestOptions);
        }
      } on DioException catch (e) {
        if (e.type.index == ResponseCode.NO_INTERNET_CONNECTION) {
          var failure = ErrorHandler.handle(e).failure;

          SnackbarUtils.error(failure.title, failure.message ?? "");
        }
      }
    }

    return null;
  }

  DioException _dioException(
    RequestOptions requestOptions,
    Response<dynamic>? response,
  ) {
    return DioException(
      requestOptions: requestOptions,
      response: response,
    );
  }

  @override
  Future<void> onRequest(
    RequestOptions requestOptions,
    RequestInterceptorHandler handler,
  ) async {
    var appPreferences = getX.Get.find<AppPreferences>();

    var flavorValues = FlavorConfig.instance.values;

    var baseUrl = requestOptions.baseUrl;

    if (flavorValues.seiBaseUrl.contains(baseUrl)) {
      var map = await appPreferences.getAuthorizationSei();
      var model = AuthTokenModel.fromJson(map);

      if (model.data?.token == null) {
        var response = await this._refreshToken(requestOptions);

        if (response != null) return handler.resolve(response);

        return handler.reject(this._dioException(requestOptions, response));
      }

      var expirationDate =
          JwtDecoder.getExpirationDate(model.data?.token ?? "");

      if (DateTime.now().isAfter(expirationDate)) {
        var response = await this._refreshToken(requestOptions);

        if (response != null) return handler.resolve(response);

        return handler.reject(this._dioException(requestOptions, response));
      }

      requestOptions.headers['Authorization'] = model.data?.token;
    }

    return handler.next(requestOptions);
  }

  @override
  Future<void> onResponse(
    Response<dynamic> response,
    ResponseInterceptorHandler handler,
  ) async {
    var flavorValues = FlavorConfig.instance.values;

    var baseUrl = response.requestOptions.baseUrl;

    if (flavorValues.seiBaseUrl.contains(baseUrl)) {
      if (response.data is Map && response.data["sucesso"] == false) {
        var message = "Não foi possível carregar o conteúdo do documento.";

        if (!response.data["mensagem"].contains(message)) {
          var res = await this._refreshToken(response.requestOptions);

          if (res != null) return handler.resolve(response);

          return handler.reject(
            this._dioException(response.requestOptions, res),
          );
        }
      }

      var htmlError = '"-//W3C//DTD XHTML 1.0 Transitional//EN"';

      if (response.data is String && response.data.contains(htmlError)) {
        var res = await this._refreshToken(response.requestOptions);

        if (res != null) return handler.resolve(response);

        return handler.reject(
          this._dioException(response.requestOptions, res),
        );
      }
    }

    return handler.next(response);
  }

  @override
  void onError(DioException error, ErrorInterceptorHandler handler) async {
    var appPreferences = getX.Get.find<AppPreferences>();

    var flavorValues = FlavorConfig.instance.values;

    var baseUrl = error.requestOptions.baseUrl;

    if (flavorValues.seiBaseUrl.contains(baseUrl)) {
      var map = await appPreferences.getAuthorizationSei();
      var model = AuthTokenModel.fromJson(map);

      if (model.data?.token == null) {
        var response = await this._refreshToken(error.requestOptions);

        if (response != null) return handler.resolve(response);

        return handler.reject(
          this._dioException(error.requestOptions, response),
        );
      }

      var expirationDate =
          JwtDecoder.getExpirationDate(model.data?.token ?? "");

      if (DateTime.now().isAfter(expirationDate)) {
        var response = await this._refreshToken(error.requestOptions);

        if (response != null) return handler.resolve(response);

        return handler.reject(
          this._dioException(error.requestOptions, response),
        );
      }

      error.requestOptions.headers['Authorization'] = model.data?.token;

      if (error.response?.statusCode == ResponseCode.UNAUTHORIZED) {
        var response = await this._refreshToken(error.requestOptions);

        if (response != null) return handler.resolve(response);

        return handler.reject(
          this._dioException(error.requestOptions, response),
        );
      }

      if (!FlavorConfig.isProd) throw ErrorHandler.format(error).failure;

      return handler.reject(error);
    }

    return handler.next(error);
  }
}
