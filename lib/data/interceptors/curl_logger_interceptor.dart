import 'dart:convert';
import 'dart:developer';

import 'package:dio/dio.dart';

class CurlLoggerDioInterceptor extends Interceptor {
  bool printError;
  bool printSuccess;

  CurlLoggerDioInterceptor({
    this.printError = false,
    this.printSuccess = false,
  });

  @override
  void onResponse(
    Response response,
    ResponseInterceptorHandler handler,
  ) {
    if (this.printSuccess) {
      this._renderCurlRepresentation(response.requestOptions);
    }

    return handler.next(response);
  }

  @override
  void onError(DioException error, ErrorInterceptorHandler handler) {
    if (this.printError) {
      this._renderCurlRepresentation(error.requestOptions);
    }

    return handler.next(error);
  }

  void _renderCurlRepresentation(RequestOptions requestOptions) {
    try {
      log(this._cURLRepresentation(requestOptions));
    } catch (err) {
      log('unable to create a CURL representation of the requestOptions');
    }
  }

  String _cURLRepresentation(RequestOptions options) {
    List<String> components = ['curl -i'];
    if (options.method.toUpperCase() != 'GET') {
      components.add('-X ${options.method}');
    }

    options.headers.forEach((k, v) {
      if (k != 'Cookie') {
        components.add('-H "$k: $v"');
      }
    });

    if (options.data != null) {
      if (options.data is FormData) {
        options.data = Map.fromEntries(options.data.fields);
      }

      final data = json.encode(options.data).replaceAll('"', '\\"');
      components.add('-d "$data"');
    }

    components.add('"${options.uri.toString()}"');

    return components.join(' \\\n\t');
  }
}
