import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart' as getX;
import 'package:jwt_decoder/jwt_decoder.dart';
import 'package:tjcemobile/app/app_preferences.dart';
import 'package:tjcemobile/app/flavor.dart';
import 'package:tjcemobile/app/shared/utils/snackbar_utils.dart';
import 'package:tjcemobile/data/data_source/local/citizen_local_data_source.dart';
import 'package:tjcemobile/data/model/auth_token_model.dart';
import 'package:tjcemobile/data/model/citizen_model.dart';
import 'package:tjcemobile/data/model/lawyer_model.dart';
import 'package:tjcemobile/data/network/error_handler.dart';
import 'package:tjcemobile/domain/viewmodel/client_interceptor/http_call_viewmodel.dart';
import 'package:tjcemobile/domain/viewmodel/client_interceptor/http_error_viewmodel.dart';
import 'package:tjcemobile/domain/viewmodel/client_interceptor/http_response_viewmodel.dart';
import 'package:tjcemobile/domain/viewmodel/logger_client_viewmodel.dart';

class ClientInterceptor extends Interceptor {
  late HttpCallViewModel _httpCall;

  ClientInterceptor() {
    this._httpCall = HttpCallViewModel(0);
  }

  Future<Response<dynamic>?> _postLoggerClientError(
    RequestOptions requestOptions,
    LoggerClientViewModel viewModel,
  ) async {
    var authorization = requestOptions.headers['Authorization'];

    if (authorization != null || authorization.isNotEmpty) {
      var flavorValues = FlavorConfig.instance.values;

      try {
        await Dio().post(
          flavorValues.mobileBaseUrl + "/mobile-log",
          options: Options(
            headers: {'Authorization': authorization},
          ),
          data: viewModel.toJson(),
        );

        Duration timeout = const Duration(minutes: 1);

        return await Dio(
          BaseOptions(connectTimeout: timeout, receiveTimeout: timeout),
        ).fetch(requestOptions);
      } on DioException catch (e) {
        if (e.type.index == ResponseCode.NO_INTERNET_CONNECTION) {
          var failure = ErrorHandler.handle(e).failure;

          SnackbarUtils.error(failure.title, failure.message ?? "");
        }
      }
    }

    return null;
  }

  @override
  Future<void> onError(
    DioException error,
    ErrorInterceptorHandler handler,
  ) async {
    final appPreferences = getX.Get.find<AppPreferences>();
    final deviceInfoPlugin = getX.Get.find<DeviceInfoPlugin>();

    await appPreferences
        .setCurrentUriError(error.requestOptions.uri.toString());

    var httpResponse = HttpResponseViewModel()
      ..headers = error.response?.headers.toString()
      ..statusCode = error.response?.statusCode
      ..statusMessage = error.response?.statusMessage
      ..body = error.response?.data;

    this._httpCall.setResponse(httpResponse);

    var httpError = HttpErrorViewModel()
      ..error = error.error
      ..message = error.message
      ..stackTrace = error.stackTrace.toString();

    this._httpCall.setError(httpError);

    Map<String, dynamic> authenticated = await this.authenticated;

    var loggerClientViewModel = LoggerClientViewModel(
      httpCall: this._httpCall,
      route: this.route,
      isAuthenticated: authenticated.isNotEmpty,
      authenticated: authenticated,
    );

    try {
      BaseDeviceInfo baseDeviceInfo = await deviceInfoPlugin.deviceInfo;
      loggerClientViewModel.device = baseDeviceInfo.data;
    } on PlatformException catch (platformException) {
      print(platformException.message);
    } catch (e) {
      print(e);
    }

    var map = await appPreferences.getAuthorizationMobile();
    var model = AuthTokenModel.fromJson(map);

    if (model.data?.token == null) {
      return handler.next(error);
    }

    var expirationDate = JwtDecoder.getExpirationDate(model.data?.token ?? "");

    if (DateTime.now().isAfter(expirationDate)) {
      return handler.next(error);
    }

    var accessToken = model.data?.token ?? "";
    accessToken = "Bearer $accessToken";

    error.requestOptions.headers['Authorization'] = accessToken;

    if (error.response?.statusCode != ResponseCode.UNAUTHORIZED) {
      var response = await this._postLoggerClientError(
        error.requestOptions,
        loggerClientViewModel,
      );

      if (response != null) return handler.resolve(response);
    }

    return handler.next(error);
  }

  Map<String, dynamic> get route {
    var routing = getX.Get.routing;

    return {
      'current': routing.current,
      'previous': routing.previous,
      'args': routing.args,
      'isBack': routing.isBack,
      'removed': routing.removed,
      'isBottomSheet': routing.isBottomSheet,
      'isDialog': routing.isDialog,
    };
  }

  Future<Map<String, dynamic>> get authenticated async {
    final appPreferences = getX.Get.find<AppPreferences>();

    Map<String, dynamic> lawyerMap =
        await appPreferences.getLawyerAuthenticate();

    LawyerModel lawyerModel = LawyerModel.fromJson(lawyerMap);

    if (lawyerModel.data?.advogado?.id != null &&
        lawyerModel.data?.dispositivo?.token != null) {
      return lawyerModel.data!.toJson();
    }

    Map<String, dynamic> localDataMap = await appPreferences.getLocalData();

    if (localDataMap.containsKey(CITIZEN_KEY) &&
        localDataMap[CITIZEN_KEY] != null) {
      CitizenDataModel citizenDataModel =
          CitizenDataModel.fromJson(localDataMap[CITIZEN_KEY]);

      if (citizenDataModel.id != null && citizenDataModel.deviceToken != null) {
        return citizenDataModel.toJson();
      }
    }

    return {};
  }
}
