import 'package:json_annotation/json_annotation.dart';
import 'package:tjcemobile/data/model/base_model.dart';

part 'pje_log_model.g.dart';

@JsonSerializable()
class PjeLogModel extends BaseModel {
  PjeLogDataModel? data;

  PjeLogModel({this.data});

  factory PjeLogModel.fromJson(Map<String, dynamic> json) =>
      _$PjeLogModelFromJson(json);

  Map<String, dynamic> toJson() => _$PjeLogModelToJson(this);
}

@JsonSerializable()
class PjeLogDataModel {
  String? sucess;

  PjeLogDataModel({this.sucess});

  factory PjeLogDataModel.fromJson(Map<String, dynamic> json) =>
      _$PjeLogDataModelFromJson(json);

  Map<String, dynamic> toJson() => _$PjeLogDataModelToJson(this);
}
