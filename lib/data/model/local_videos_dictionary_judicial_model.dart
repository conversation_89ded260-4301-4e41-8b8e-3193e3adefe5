import 'package:json_annotation/json_annotation.dart';
import 'package:tjcemobile/data/model/base_model.dart';

part 'local_videos_dictionary_judicial_model.g.dart';

@JsonSerializable()
class LocalVideosDictionaryJudicialModel extends BaseModel {
  List<LocalVideosDictionaryJudicialDataModel>? data;

  LocalVideosDictionaryJudicialModel({this.data});

  factory LocalVideosDictionaryJudicialModel.fromJson(
          Map<String, dynamic> json) =>
      _$LocalVideosDictionaryJudicialModelFromJson(json);

  Map<String, dynamic> toJson() =>
      _$LocalVideosDictionaryJudicialModelToJson(this);
}

@JsonSerializable()
class LocalVideosDictionaryJudicialDataModel {
  String? title;
  String? subTitle;
  String? match;
  String? url;

  LocalVideosDictionaryJudicialDataModel({
    this.title,
    this.subTitle,
    this.match,
    this.url,
  });

  factory LocalVideosDictionaryJudicialDataModel.fromJson(
          Map<String, dynamic> json) =>
      _$LocalVideosDictionaryJudicialDataModelFromJson(json);

  Map<String, dynamic> toJson() =>
      _$LocalVideosDictionaryJudicialDataModelToJson(this);
}
