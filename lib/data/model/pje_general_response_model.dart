import 'package:json_annotation/json_annotation.dart';
import 'package:tjcemobile/data/model/base_model.dart';

part 'pje_general_response_model.g.dart';

@JsonSerializable()
class PjeGeneralResponseModel extends BaseModel {
  List<PjeGeneralResponseDataModel>? data;

  PjeGeneralResponseModel({this.data});

  factory PjeGeneralResponseModel.fromJson(Map<String, dynamic> json) =>
      _$PjeGeneralResponseModelFromJson(json);

  Map<String, dynamic> toJson() => _$PjeGeneralResponseModelToJson(this);
}

@JsonSerializable(includeIfNull: false)
class PjeGeneralResponseDataModel {
  String? id;
  String? codigo;
  String? descricao;

  PjeGeneralResponseDataModel({
    this.id,
    this.codigo,
    this.descricao,
  });

  factory PjeGeneralResponseDataModel.fromJson(Map<String, dynamic> json) =>
      _$PjeGeneralResponseDataModelFromJson(json);

  Map<String, dynamic> toJson() => _$PjeGeneralResponseDataModelToJson(this);
}
