import 'package:json_annotation/json_annotation.dart';
import 'package:tjcemobile/data/model/base_model.dart';

part 'limit_deal_mobile_court_model.g.dart';

@JsonSerializable()
class LimitDealMobileCourtModel extends BaseModel {
  LimitDealMobileCourtDataModel? data;

  LimitDealMobileCourtModel({
    this.data,
  });

  factory LimitDealMobileCourtModel.fromJson(Map<String, dynamic> json) =>
      _$LimitDealMobileCourtModelFromJson(json);

  Map<String, dynamic> toJson() => _$LimitDealMobileCourtModelToJson(this);
}

@JsonSerializable()
class LimitDealMobileCourtDataModel {
  int? status;
  String? mensagem;

  LimitDealMobileCourtDataModel({
    this.status,
    this.mensagem,
  });

  factory LimitDealMobileCourtDataModel.fromJson(Map<String, dynamic> json) =>
      _$LimitDealMobileCourtDataModelFromJson(json);

  Map<String, dynamic> toJson() => _$LimitDealMobileCourtDataModelToJson(this);
}
