import 'package:json_annotation/json_annotation.dart';
import 'package:tjcemobile/data/model/base_model.dart';

part 'history_actuation_model.g.dart';

@JsonSerializable()
class HistoryActuationModel extends BaseModel {
  List<HistoryActuationDataModel>? data;

  HistoryActuationModel({this.data});

  factory HistoryActuationModel.fromJson(Map<String, dynamic> json) =>
      _$HistoryActuationModelFromJson(json);

  Map<String, dynamic> toJson() => _$HistoryActuationModelToJson(this);
}

@JsonSerializable()
class HistoryActuationDataModel {
  String? movimentacao;
  String? resposta;
  String? dataAtualizacao;

  HistoryActuationDataModel(
      {this.movimentacao, this.resposta, this.dataAtualizacao});

  factory HistoryActuationDataModel.fromJson(Map<String, dynamic> json) =>
      _$HistoryActuationDataModelFromJson(json);

  Map<String, dynamic> toJson() => _$HistoryActuationDataModelToJson(this);
}
