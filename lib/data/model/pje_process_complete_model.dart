import 'package:json_annotation/json_annotation.dart';
import 'package:tjcemobile/data/model/base_model.dart';

part 'pje_process_complete_model.g.dart';

@JsonSerializable()
class PjeProcessCompleteModel extends BaseModel {
  PjeProcessCompleteDataModel? data;

  PjeProcessCompleteModel({this.data});

  factory PjeProcessCompleteModel.fromJson(Map<String, dynamic> json) =>
      _$PjeProcessCompleteModelFromJson(json);

  Map<String, dynamic> toJson() => _$PjeProcessCompleteModelToJson(this);
}

@JsonSerializable(
  includeIfNull: false,
  anyMap: true,
)
class PjeProcessCompleteDataModel {
  String? idProcesso;
  String? grau;
  Processo? processo;

  PjeProcessCompleteDataModel({
    this.idProcesso,
    this.grau,
    this.processo,
  });

  factory PjeProcessCompleteDataModel.fromJson(Map<String, dynamic> json) =>
      _$PjeProcessCompleteDataModelFromJson(json);

  Map<String, dynamic> toJson() => _$PjeProcessCompleteDataModelToJson(this);
}

@JsonSerializable(
  includeIfNull: false,
  anyMap: true,
)
class Processo {
  DadosBasicos? dadosBasicos;
  List<Movimento>? movimento;
  List<DocumentoDataModel>? documento;

  Processo({this.dadosBasicos, this.movimento, this.documento});

  factory Processo.fromJson(Map<String, dynamic> json) =>
      _$ProcessoFromJson(json);

  Map<String, dynamic> toJson() => _$ProcessoToJson(this);
}

@JsonSerializable(
  includeIfNull: false,
  anyMap: true,
)
class DadosBasicos {
  List<Polo>? polo;
  List<Assunto>? assunto;
  List<Value>? magistradoAtuante;
  int? valorCausa;
  OrgaoJulgador? orgaoJulgador;
  Value? numero;
  int? competencia;
  String? competenciaDescricao;
  int? classeProcessual;
  String? classeProcessualDescricao;
  String? codigoLocalidade;
  String? codigoLocalidadeDescricao;
  int? nivelSigilo;
  bool? intervencaoMP;
  int? tamanhoProcesso;
  Value? dataAjuizamento;

  DadosBasicos({
    this.polo,
    this.magistradoAtuante,
    this.valorCausa,
    this.orgaoJulgador,
    this.numero,
    this.competencia,
    this.competenciaDescricao,
    this.classeProcessual,
    this.classeProcessualDescricao,
    this.codigoLocalidade,
    this.codigoLocalidadeDescricao,
    this.nivelSigilo,
    this.intervencaoMP,
    this.tamanhoProcesso,
    this.dataAjuizamento,
  });

  factory DadosBasicos.fromJson(Map<String, dynamic> json) =>
      _$DadosBasicosFromJson(json);

  Map<String, dynamic> toJson() => _$DadosBasicosToJson(this);
}

@JsonSerializable(
  includeIfNull: false,
  anyMap: true,
)
class Polo {
  List<Parte>? parte;
  String? polo;

  Polo({this.parte, this.polo});

  factory Polo.fromJson(Map<String, dynamic> json) => _$PoloFromJson(json);

  Map<String, dynamic> toJson() => _$PoloToJson(this);
}

@JsonSerializable(
  includeIfNull: false,
  anyMap: true,
)
class Assunto {
  int? codigoNacional;
  String? codigoNacionalDescricao;
  bool? principal;

  Assunto({this.codigoNacional, this.codigoNacionalDescricao, this.principal});

  factory Assunto.fromJson(Map<String, dynamic> json) =>
      _$AssuntoFromJson(json);

  Map<String, dynamic> toJson() => _$AssuntoToJson(this);
}

@JsonSerializable(
  includeIfNull: false,
  anyMap: true,
)
class Parte {
  Pessoa? pessoa;
  List<Advogado>? advogado;

  Parte({this.pessoa, this.advogado});

  factory Parte.fromJson(Map<String, dynamic> json) => _$ParteFromJson(json);

  Map<String, dynamic> toJson() => _$ParteToJson(this);
}

@JsonSerializable(
  includeIfNull: false,
  anyMap: true,
)
class Pessoa {
  List<Documento>? documento;
  String? nome;
  Value? dataNascimento;
  String? tipoPessoa;

  Pessoa({
    this.documento,
    this.nome,
    this.dataNascimento,
    this.tipoPessoa,
  });

  factory Pessoa.fromJson(Map<String, dynamic> json) => _$PessoaFromJson(json);

  Map<String, dynamic> toJson() => _$PessoaToJson(this);
}

@JsonSerializable(
  includeIfNull: false,
  anyMap: true,
)
class Documento {
  String? codigoDocumento;
  String? emissorDocumento;
  String? tipoDocumento;
  String? nome;

  Documento({
    this.codigoDocumento,
    this.emissorDocumento,
    this.tipoDocumento,
    this.nome,
  });

  factory Documento.fromJson(Map<String, dynamic> json) =>
      _$DocumentoFromJson(json);

  Map<String, dynamic> toJson() => _$DocumentoToJson(this);
}

@JsonSerializable(
  includeIfNull: false,
  anyMap: true,
)
class Advogado {
  String? nome;
  bool? intimacao;
  Value? inscricao;
  String? tipoRepresentante;

  Advogado({
    this.nome,
    this.intimacao,
    this.inscricao,
    // this.numeroDocumentoPrincipal,
    this.tipoRepresentante,
  });

  factory Advogado.fromJson(Map<String, dynamic> json) =>
      _$AdvogadoFromJson(json);

  Map<String, dynamic> toJson() => _$AdvogadoToJson(this);
}

@JsonSerializable(
  includeIfNull: false,
  anyMap: true,
)
class OrgaoJulgador {
  String? codigoOrgao;
  String? nomeOrgao;
  String? instancia;
  int? codigoMunicipioIBGE;

  OrgaoJulgador({
    this.codigoOrgao,
    this.nomeOrgao,
    this.instancia,
    this.codigoMunicipioIBGE,
  });

  factory OrgaoJulgador.fromJson(Map<String, dynamic> json) =>
      _$OrgaoJulgadorFromJson(json);

  Map<String, dynamic> toJson() => _$OrgaoJulgadorToJson(this);
}

@JsonSerializable(
  includeIfNull: false,
  anyMap: true,
)
class Movimento {
  MovimentoNacional? movimentoNacional;
  String? idDocumentoVinculado;
  Value? dataHora;
  String? identificadorMovimento;

  Movimento({
    this.movimentoNacional,
    this.dataHora,
    this.idDocumentoVinculado,
    this.identificadorMovimento,
  });

  factory Movimento.fromJson(Map<String, dynamic> json) =>
      _$MovimentoFromJson(json);

  Map<String, dynamic> toJson() => _$MovimentoToJson(this);
}

@JsonSerializable(
  includeIfNull: false,
  anyMap: true,
)
class MovimentoNacional {
  List<Value>? complemento;
  int? codigoNacional;

  MovimentoNacional({
    this.complemento,
    this.codigoNacional,
  });

  factory MovimentoNacional.fromJson(Map<String, dynamic> json) =>
      _$MovimentoNacionalFromJson(json);

  Map<String, dynamic> toJson() => _$MovimentoNacionalToJson(this);
}

@JsonSerializable(
  includeIfNull: false,
  anyMap: true,
)
class Value {
  String? value;
  bool? setValue;

  Value({this.value, this.setValue});

  factory Value.fromJson(Map<String, dynamic> json) => _$ValueFromJson(json);

  Map<String, dynamic> toJson() => _$ValueToJson(this);
}

@JsonSerializable(includeIfNull: false)
class DocumentoDataModel {
  bool loadContent;
  String? numeroProcesso;
  String? grau;
  Conteudo? conteudo;
  // List<Assinatura>? assinatura;
  String? idDocumento;
  List<DocumentoVinculado>? documentoVinculado;
  String? tipoDocumento;
  Value? dataHora;
  String? mimetype;
  int? nivelSigilo;
  String? movimento;
  String? hash;
  String? descricao;

  DocumentoDataModel({
    this.loadContent = false,
    this.numeroProcesso,
    this.grau,
    this.conteudo,
    this.documentoVinculado,
    // this.assinatura,
    this.idDocumento,
    this.tipoDocumento,
    this.dataHora,
    this.mimetype,
    this.nivelSigilo,
    this.movimento,
    this.hash,
    this.descricao,
  });

  factory DocumentoDataModel.fromJson(Map<String, dynamic> json) =>
      _$DocumentoDataModelFromJson(json);

  Map<String, dynamic> toJson() => _$DocumentoDataModelToJson(this);
}

@JsonSerializable()
class Conteudo {
  String? include;

  Conteudo({this.include});

  factory Conteudo.fromJson(Map<String, dynamic> json) =>
      _$ConteudoFromJson(json);

  Map<String, dynamic> toJson() => _$ConteudoToJson(this);
}

@JsonSerializable()
class DocumentoVinculado {
  String? idDocumento;
  String? idDocumentoVinculado;
  String? tipoDocumento;
  String? descricao;
  String? mimetype;
  String? hash;
  bool loadContent;

  DocumentoVinculado({
    this.idDocumento,
    this.idDocumentoVinculado,
    this.tipoDocumento,
    this.descricao,
    this.mimetype,
    this.hash,
    this.loadContent = false,
  });

  factory DocumentoVinculado.fromJson(Map<String, dynamic> json) =>
      _$DocumentoVinculadoFromJson(json);

  Map<String, dynamic> toJson() => _$DocumentoVinculadoToJson(this);

  DocumentoDataModel get fromDocumentoDataModel {
    return DocumentoDataModel(
      idDocumento: this.idDocumento,
      tipoDocumento: this.tipoDocumento,
      descricao: this.descricao,
      mimetype: this.mimetype,
      hash: this.hash,
    );
  }
}
