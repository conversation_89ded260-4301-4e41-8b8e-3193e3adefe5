import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:tjcemobile/domain/viewmodel/auth_token_viewmodel.dart';
import 'package:tjcemobile/domain/viewmodel/logger_client_viewmodel.dart';

part 'mobile_service.g.dart';

@RestApi()
abstract class MobileService {
  factory MobileService(Dio dio, {String baseUrl}) = _MobileService;

  @POST("/auth/token")
  Future<HttpResponse<dynamic>> postAuthToken(
    @Body() AuthTokenViewModel viewModel,
  );

  @POST("/mobile-log")
  Future<HttpResponse<dynamic>> postLoggerClientError(
    @Body() LoggerClientViewModel viewModel,
  );
}
