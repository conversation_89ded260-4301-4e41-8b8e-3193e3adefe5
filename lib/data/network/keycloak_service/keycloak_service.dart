import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:tjcemobile/domain/viewmodel/keycloak_authorization_viewmodel.dart';

part 'keycloak_service.g.dart';

@RestApi()
abstract class KeycloakService {
  factory KeycloakService(Dio dio, {String baseUrl}) = _KeycloakService;

  @POST("/token")
  Future<HttpResponse<dynamic>> postToken(
    @Header("Content-Type") String contentType,
    @Body() KeycloakAuthorizationViewModel viewModel,
  );
}
