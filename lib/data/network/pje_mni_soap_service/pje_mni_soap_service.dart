import 'package:dio/dio.dart' hide Headers;
import 'package:retrofit/retrofit.dart';

part 'pje_mni_soap_service.g.dart';

//coverage:ignore-file
@RestApi()
abstract class PjeMniSoapService {
  factory PjeMniSoapService(Dio dio, {String baseUrl}) = _PjeMniSoapService;

  @POST("/intercomunicacao")
  @Headers({
    "Content-Type": "text/xml; charset=utf-8",
  })
  @DioResponseType(ResponseType.bytes)
  Future<HttpResponse<dynamic>> postProcessMNI(
    @Body() String raw,
  );

  @POST("/ConsultaPJe")
  @Headers({
    "Content-Type": "text/xml; charset=utf-8",
  })
  @DioResponseType(ResponseType.plain)
  Future<HttpResponse<dynamic>> postConsultPJe(
    @Body() String raw,
  );
}
