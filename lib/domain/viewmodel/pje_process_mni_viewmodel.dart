import 'package:json_annotation/json_annotation.dart';

part 'pje_process_mni_viewmodel.g.dart';

@JsonSerializable(includeIfNull: false)
class PjeProcessMniViewModel {
  // PjeLoginViewModel pjeLoginViewModel;
  String idConsultante;
  String senhaConsultante;
  NumeroProcesso numeroProcesso;
  bool incluirCabecalho;
  bool incluirDocumentos;
  bool movimentos;
  String? documento;

  PjeProcessMniViewModel({
    // required this.pjeLoginViewModel,
    required this.idConsultante,
    required this.senhaConsultante,
    required this.numeroProcesso,
    this.incluirCabecalho = true,
    this.incluirDocumentos = true,
    this.movimentos = true,
    this.documento,
  });

  factory PjeProcessMniViewModel.fromJson(Map<String, dynamic> json) =>
      _$PjeProcessMniViewModelFromJson(json);

  Map<String, dynamic> toJson() => _$PjeProcessMniViewModelToJson(this);
}

@JsonSerializable()
class NumeroProcesso {
  String value;
  bool setValue;

  NumeroProcesso({
    required this.value,
    this.setValue = true,
  });

  factory NumeroProcesso.fromJson(Map<String, dynamic> json) =>
      _$NumeroProcessoFromJson(json);

  Map<String, dynamic> toJson() => _$NumeroProcessoToJson(this);
}

@JsonSerializable()
class PjeLoginViewModel {
  String? consultantId;
  String? consultantPassword;
  String? degree;

  PjeLoginViewModel({
    this.consultantId,
    this.consultantPassword,
    this.degree,
  });

// from json
  factory PjeLoginViewModel.fromJson(Map<String, dynamic> json) =>
      _$PjeLoginViewModelFromJson(json);

// to json
  Map<String, dynamic> toJson() => _$PjeLoginViewModelToJson(this);
}
