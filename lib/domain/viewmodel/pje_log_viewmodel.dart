import 'package:json_annotation/json_annotation.dart';

part 'pje_log_viewmodel.g.dart';

@JsonSerializable()
class PjeLogViewModel {
  String? cpf;
  String? oab;
  String? processo;
  String operacao;

  PjeLogViewModel({this.processo, this.oab, this.cpf, this.operacao = "1"});

  factory PjeLogViewModel.fromJson(Map<String, dynamic> json) =>
      _$PjeLogViewModelFromJson(json);

  Map<String, dynamic> toJson() => _$PjeLogViewModelToJson(this);
}
