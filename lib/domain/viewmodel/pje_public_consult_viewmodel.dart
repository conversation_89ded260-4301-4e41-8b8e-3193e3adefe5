import 'package:json_annotation/json_annotation.dart';

part 'pje_public_consult_viewmodel.g.dart';

@JsonSerializable(
  fieldRename: FieldRename.snake,
)
class PjePublicConsultViewModel {
  String? numberProcess;
  String? typeReferenceProcess;
  String? numberReferenceProcess;
  String? namePart;
  String? typeRegistrationFederalRevenue;
  String? numberRegistrationFederalRevenue;
  String? numberOAB;
  String? letterOAB;
  String? stateOAB;

  PjePublicConsultViewModel({
    this.numberProcess,
    this.typeReferenceProcess,
    this.numberReferenceProcess,
    this.namePart,
    this.typeRegistrationFederalRevenue,
    this.numberRegistrationFederalRevenue,
    this.numberOAB,
    this.letterOAB,
    this.stateOAB,
  });

  factory PjePublicConsultViewModel.fromJson(Map<String, dynamic> json) =>
      _$PjePublicConsultViewModelFromJson(json);

  Map<String, dynamic> toJson() => _$PjePublicConsultViewModelToJson(this);
}
