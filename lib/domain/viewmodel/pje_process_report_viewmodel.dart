import 'package:json_annotation/json_annotation.dart';

part 'pje_process_report_viewmodel.g.dart';

@JsonSerializable(
  fieldRename: FieldRename.snake,
)
class PjeProcessReportViewModel {
  String? processo;
  String? grau;

  PjeProcessReportViewModel({
    this.processo,
    this.grau,
  });

  factory PjeProcessReportViewModel.fromJson(Map<String, dynamic> json) =>
      _$PjeProcessReportViewModelFromJson(json);

  Map<String, dynamic> toJson() => _$PjeProcessReportViewModelToJson(this);
}
