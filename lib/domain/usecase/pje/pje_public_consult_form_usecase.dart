import 'package:dartz/dartz.dart';
import 'package:tjcemobile/data/model/company_model.dart';
import 'package:tjcemobile/data/model/keycloak_authorization_model.dart';
import 'package:tjcemobile/data/model/process_model.dart';
import 'package:tjcemobile/data/network/failure.dart';
import 'package:tjcemobile/domain/repository/remote/federal_revenue_repository.dart';
import 'package:tjcemobile/domain/repository/remote/keycloack_repository.dart';
import 'package:tjcemobile/domain/repository/remote/scpu_repository.dart';
import 'package:tjcemobile/domain/viewmodel/cnpj_viewmodel.dart';
import 'package:tjcemobile/domain/viewmodel/keycloak_authorization_viewmodel.dart';
import 'package:tjcemobile/domain/viewmodel/process_consult_viewmodel.dart';

class PjePublicConsultFormUseCase {
  FederalRevenueRepository _federalRevenueRepository;
  KeycloakRepository _keycloakRepository;
  ScpuRepository _scpuRepository;

  PjePublicConsultFormUseCase(
    this._federalRevenueRepository,
    this._keycloakRepository,
    this._scpuRepository,
  );

  Future<Either<Failure, CompanyModel>> getCompanyByCNPJ(
    CNPJViewModel viewModel,
  ) async {
    return await this._federalRevenueRepository.getCompanyByCNPJ(viewModel);
  }

  Future<Either<Failure, KeycloakAuthorizationModel>> postToken(
    KeycloakAuthorizationViewModel viewModel, {
    required String baseUrl,
  }) async {
    return await this._keycloakRepository.postToken(
          viewModel,
          baseUrl: baseUrl,
        );
  }

  Future<Either<Failure, ProcessModel>> postProcessName(
    ProcessConsultViewModel viewModel,
  ) async {
    return await this._scpuRepository.postProcessName(viewModel);
  }
}
