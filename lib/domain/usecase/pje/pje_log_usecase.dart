import 'package:dartz/dartz.dart';
import 'package:tjcemobile/data/model/keycloak_authorization_model.dart';
import 'package:tjcemobile/data/model/pje_log_model.dart';
import 'package:tjcemobile/data/network/failure.dart';
import 'package:tjcemobile/domain/repository/remote/keycloack_repository.dart';
import 'package:tjcemobile/domain/repository/remote/pje_log_repository.dart';
import 'package:tjcemobile/domain/viewmodel/keycloak_authorization_viewmodel.dart';
import 'package:tjcemobile/domain/viewmodel/pje_log_viewmodel.dart';

class PjeLogUseCase {
  PjeLogRepository _pjeLogRepository;
  KeycloakRepository _keycloakRepository;

  PjeLogUseCase(
    this._pjeLogRepository,
    this._keycloakRepository,
  );

  Future<Either<Failure, KeycloakAuthorizationModel>> postToken(
    KeycloakAuthorizationViewModel viewModel, {
    required String baseUrl,
  }) async {
    return await this._keycloakRepository.postToken(
          viewModel,
          baseUrl: baseUrl,
        );
  }

  Future<Either<Failure, PjeLogModel>> postPjeLog(
    PjeLogViewModel viewModel,
  ) async {
    return await this._pjeLogRepository.postPjeLog(
          viewModel,
        );
  }
}
