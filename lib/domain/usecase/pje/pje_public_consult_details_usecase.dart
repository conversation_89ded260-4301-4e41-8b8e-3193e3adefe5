import 'package:dartz/dartz.dart';
import 'package:tjcemobile/data/model/audience_pdf_model.dart';
import 'package:tjcemobile/data/model/keycloak_authorization_model.dart';
import 'package:tjcemobile/data/model/pje_general_response_model.dart';
import 'package:tjcemobile/data/model/pje_log_model.dart';
import 'package:tjcemobile/data/model/pje_process_complete_model.dart';
import 'package:tjcemobile/data/network/failure.dart';
import 'package:tjcemobile/domain/repository/local/pje_login_local_repository.dart';
import 'package:tjcemobile/domain/repository/remote/keycloack_repository.dart';
import 'package:tjcemobile/domain/repository/remote/lawyer_repository.dart';
import 'package:tjcemobile/domain/repository/remote/pje_log_repository.dart';
import 'package:tjcemobile/domain/repository/remote/pje_mni_soap_repository.dart';
import 'package:tjcemobile/domain/viewmodel/keycloak_authorization_viewmodel.dart';
import 'package:tjcemobile/domain/viewmodel/pje_log_viewmodel.dart';
import 'package:tjcemobile/domain/viewmodel/pje_process_mni_viewmodel.dart';
import 'package:tjcemobile/domain/viewmodel/pje_process_report_viewmodel.dart';

class PjePublicConsultDetailsUseCase {
  KeycloakRepository _keycloakRepository;
  LawyerRepository _lawyerRepository;
  PjeMniSoapRepository _pjeMniSoapRepository;
  PjeLogRepository _pjeLogRepository;
  PjeLoginLocalRepository _pjeLoginLocalRepository;

  PjePublicConsultDetailsUseCase(
    this._keycloakRepository,
    this._lawyerRepository,
    this._pjeMniSoapRepository,
    this._pjeLogRepository,
    this._pjeLoginLocalRepository,
  );

  Future<Either<Failure, KeycloakAuthorizationModel>> postToken(
    KeycloakAuthorizationViewModel viewModel, {
    required String baseUrl,
  }) async {
    return await this._keycloakRepository.postToken(
          viewModel,
          baseUrl: baseUrl,
        );
  }

  Future<Either<Failure, AudiencePDFModel>> getReportProcessFromPJe(
    PjeProcessReportViewModel viewModel,
  ) async {
    return await this._lawyerRepository.getReportProcessFromPJe(
          viewModel,
        );
  }

  Future<Either<Failure, PjeProcessCompleteModel>> postProcessMNISoap(
    String process, {
    PjeLoginViewModel? pjeLoginViewModel,
    bool useBaseUri2G = false,
    bool includeHeader = false,
    bool includeMovements = false,
    bool includeDocuments = false,
    String? documentId,
  }) async {
    return await this._pjeMniSoapRepository.postProcessMNI(
          process,
          pjeLoginViewModel: pjeLoginViewModel,
          useBaseUri2G: useBaseUri2G,
          includeHeader: includeHeader,
          includeMovements: includeMovements,
          includeDocuments: includeDocuments,
          documentId: documentId,
        );
  }

  Future<Either<Failure, PjeGeneralResponseModel>> postCourt({
    bool useBaseUri2G = false,
  }) async {
    return await this._pjeMniSoapRepository.postCourt(
          useBaseUri2G: useBaseUri2G,
        );
  }

  Future<Either<Failure, PjeGeneralResponseModel>> postCourtClass(
    String arg0, {
    bool useBaseUri2G = false,
  }) async {
    return await this._pjeMniSoapRepository.postCourtClass(
          arg0,
          useBaseUri2G: useBaseUri2G,
        );
  }

  Future<Either<Failure, PjeGeneralResponseModel>> postCourtMatter(
    String arg0,
    String arg1, {
    bool useBaseUri2G = false,
  }) async {
    return await this._pjeMniSoapRepository.postCourtMatter(
          arg0,
          arg1,
          useBaseUri2G: useBaseUri2G,
        );
  }

  Future<Either<Failure, PjeGeneralResponseModel>> postCompetence(
    String arg0,
    String arg1,
    String arg2, {
    bool useBaseUri2G = false,
  }) async {
    return await this._pjeMniSoapRepository.postCompetence(
          arg0,
          arg1,
          arg2,
          useBaseUri2G: useBaseUri2G,
        );
  }

  Future<Either<Failure, PjeLogModel>> postPjeLog(
    PjeLogViewModel? viewModel,
  ) async {
    return await this._pjeLogRepository.postPjeLog(
          viewModel,
        );
  }

  Future<Either<Failure, List<PjeLoginViewModel>>>
      getPjeLoginViewModel() async {
    return await this._pjeLoginLocalRepository.getPjeLoginViewModelList();
  }
}
