import 'package:dartz/dartz.dart';
import 'package:tjcemobile/data/model/keycloak_authorization_model.dart';
import 'package:tjcemobile/data/model/pje_log_model.dart';
import 'package:tjcemobile/data/model/pje_process_complete_model.dart';
import 'package:tjcemobile/data/network/failure.dart';
import 'package:tjcemobile/domain/repository/local/pje_login_local_repository.dart';
import 'package:tjcemobile/domain/repository/remote/keycloack_repository.dart';
import 'package:tjcemobile/domain/repository/remote/pje_log_repository.dart';
import 'package:tjcemobile/domain/repository/remote/pje_mni_soap_repository.dart';
import 'package:tjcemobile/domain/viewmodel/keycloak_authorization_viewmodel.dart';
import 'package:tjcemobile/domain/viewmodel/pje_log_viewmodel.dart';
import 'package:tjcemobile/domain/viewmodel/pje_process_mni_viewmodel.dart';

class PjePublicConsultListUseCase {
  PjeMniSoapRepository _pjeMniSoapRepository;
  PjeLogRepository _pjeLogRepository;
  PjeLoginLocalRepository _localRepository;
  KeycloakRepository _keycloakRepository;

  PjePublicConsultListUseCase(
    this._pjeMniSoapRepository,
    this._pjeLogRepository,
    this._localRepository,
    this._keycloakRepository,
  );

  Future<Either<Failure, KeycloakAuthorizationModel>> postToken(
    KeycloakAuthorizationViewModel viewModel, {
    required String baseUrl,
  }) async {
    return await this._keycloakRepository.postToken(
          viewModel,
          baseUrl: baseUrl,
        );
  }

  Future<Either<Failure, PjeProcessCompleteModel>> postProcessMNISoap(
    String process, {
    PjeLoginViewModel? pjeLoginViewModel,
    bool useBaseUri2G = false,
    bool includeHeader = false,
    bool includeMovements = false,
    bool includeDocuments = false,
    String? documentId,
  }) async {
    return await this._pjeMniSoapRepository.postProcessMNI(
          process,
          pjeLoginViewModel: pjeLoginViewModel,
          useBaseUri2G: useBaseUri2G,
          includeHeader: includeHeader,
          includeMovements: includeMovements,
          includeDocuments: includeDocuments,
          documentId: documentId,
        );
  }

  Future<Either<Failure, PjeLogModel>> postPjeLog(
    PjeLogViewModel? viewModel,
  ) async {
    return await this._pjeLogRepository.postPjeLog(
          viewModel,
        );
  }

  Future<Either<Failure, void>> setPjeLoginViewModel(
    PjeLoginViewModel viewModel,
  ) async {
    return await this._localRepository.setPjeLoginViewModel(viewModel);
  }

  Future<Either<Failure, List<PjeLoginViewModel>>>
      getPjeLoginViewModel() async {
    return await this._localRepository.getPjeLoginViewModelList();
  }
}
