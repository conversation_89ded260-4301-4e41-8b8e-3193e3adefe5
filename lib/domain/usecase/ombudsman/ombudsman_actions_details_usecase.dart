import 'package:dartz/dartz.dart';
import 'package:tjcemobile/data/model/history_actuation_model.dart';
import 'package:tjcemobile/data/network/failure.dart';
import 'package:tjcemobile/domain/repository/local/actuation_local_repository.dart';
import 'package:tjcemobile/domain/repository/remote/sioge_repository.dart';
import 'package:tjcemobile/data/model/actuation_model.dart';
import 'package:tjcemobile/domain/viewmodel/actuation_consult_viewmodel.dart';

class OmbudsmanActionsDetailsUseCase {
  SiogeRepository _siogeRepository;
  ActuationLocalRepository _actuationLocalRepository;

  OmbudsmanActionsDetailsUseCase(
      this._siogeRepository, this._actuationLocalRepository);
  Future<Either<Failure, ActuationModel>> execute(
    ActuationConsultViewModel viewModel,
  ) async {
    return await this._siogeRepository.getActuation(viewModel);
  }

  Future<Either<Failure, HistoryActuationModel>> getHistory(
    ActuationConsultViewModel viewModel,
  ) async {
    return await this._siogeRepository.getHistoryActuation(viewModel);
  }

  Future<Either<Failure, ActuationModel?>> getActuationModelById(
      String id) async {
    return await this._actuationLocalRepository.getActuationModelById(id);
  }

  Future<Either<Failure, void>> setActuationModel(
    ActuationModel actuationModel,
  ) async {
    return await this
        ._actuationLocalRepository
        .setActuationModel(actuationModel);
  }

  Future<Either<Failure, void>> deleteProcessDataModelById(String id) async {
    return await this._actuationLocalRepository.deleteActuationModelById(id);
  }

  Future<Either<Failure, List<ActuationModel>>> getActuationModel() async {
    return await this._actuationLocalRepository.getActuationModel();
  }
}
