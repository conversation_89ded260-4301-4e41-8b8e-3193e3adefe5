import 'package:dartz/dartz.dart';
import 'package:tjcemobile/data/model/auth_token_model.dart';
import 'package:tjcemobile/data/model/response_interceptor_model.dart';
import 'package:tjcemobile/data/network/failure.dart';
import 'package:tjcemobile/domain/viewmodel/auth_token_viewmodel.dart';
import 'package:tjcemobile/domain/viewmodel/logger_client_viewmodel.dart';

abstract class MobileRepository {
  Future<Either<Failure, AuthTokenModel>> postAuthToken(
    AuthTokenViewModel viewModel,
  );

  Future<Either<Failure, ResponseInterceptorModel>> postLoggerClientError(
    LoggerClientViewModel viewModel,
  );
}
