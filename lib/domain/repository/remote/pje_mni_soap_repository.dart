import 'package:dartz/dartz.dart';
import 'package:tjcemobile/data/model/pje_general_response_model.dart';
import 'package:tjcemobile/data/model/pje_process_complete_model.dart';
import 'package:tjcemobile/data/network/failure.dart';
import 'package:tjcemobile/domain/viewmodel/pje_process_mni_viewmodel.dart';

abstract class PjeMniSoapRepository {
  Future<Either<Failure, PjeProcessCompleteModel>> postProcessMNI(
    String process, {
    PjeLoginViewModel? pjeLoginViewModel,
    bool useBaseUri2G = false,
    bool includeHeader = false,
    bool includeMovements = false,
    bool includeDocuments = false,
    String? documentId,
  });

  Future<Either<Failure, PjeGeneralResponseModel>> postCourt({
    bool useBaseUri2G = false,
  });

  Future<Either<Failure, PjeGeneralResponseModel>> postCourtClass(
    String arg0, {
    bool useBaseUri2G = false,
  });

  Future<Either<Failure, PjeGeneralResponseModel>> postCourtMatter(
    String arg0,
    String arg1, {
    bool useBaseUri2G = false,
  });

  Future<Either<Failure, PjeGeneralResponseModel>> postCompetence(
    String arg0,
    String arg1,
    String arg2, {
    bool useBaseUri2G = false,
  });
}
