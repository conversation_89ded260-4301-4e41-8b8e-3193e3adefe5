import 'package:dartz/dartz.dart';
import 'package:tjcemobile/data/network/failure.dart';
import 'package:tjcemobile/domain/viewmodel/pje_process_mni_viewmodel.dart';

abstract class PjeLoginLocalRepository {
  Future<Either<Failure, void>> setPjeLoginViewModel(
    PjeLoginViewModel viewModel,
  );

  Future<Either<Failure, List<PjeLoginViewModel>>> getPjeLoginViewModelList();

  Future<Either<Failure, void>> removeFromDataSource();
}
