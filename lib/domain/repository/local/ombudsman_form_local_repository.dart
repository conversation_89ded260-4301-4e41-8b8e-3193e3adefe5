import 'package:dartz/dartz.dart';
import 'package:tjcemobile/data/network/failure.dart';
import 'package:tjcemobile/domain/viewmodel/actuation_form_viewmodel.dart';

abstract class OmbudsmanFormLocalRepository {
  Future<Either<Failure, void>> setActuationFormViewModelList(
    ActuationFormViewModel viewModel,
  );

  Future<Either<Failure, List<ActuationFormViewModel>>>
      getActuationFormViewModelList();

  Future<Either<Failure, void>> removeFromDataSource();
}
