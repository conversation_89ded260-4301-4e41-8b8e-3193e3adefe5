class LocalAuthUtils {
  Future<void> authenticate() async {
    // final LocalAuthentication auth = LocalAuthentication();
    //
    // final bool canAuthenticateWithBiometrics = await auth.canCheckBiometrics;
    // final bool canAuthenticate =
    //     canAuthenticateWithBiometrics || await auth.isDeviceSupported();
    //
    // if (!canAuthenticate) {
    //   emit(state.copyWith(loginContentState: LoginContentState.ERROR));
    //
    //   SnackbarUtils.error(
    //     "",
    //     "Não foi possível localizar uma biometria",
    //   );
    //
    //   return;
    // }
    //
    // final List<BiometricType> availableBiometrics =
    // await auth.getAvailableBiometrics();
    //
    // if (availableBiometrics.isEmpty) {
    //   emit(state.copyWith(loginContentState: LoginContentState.ERROR));
    //
    //   SnackbarUtils.error(
    //     "",
    //     "Não foi possível conectar",
    //   );
    //
    //   return;
    // }
    //
    // try {
    //   final bool didAuthenticate = await auth.authenticate(
    //     localizedReason:
    //     'O TJCE Mobile precisa validar a sua identidade para continuar com o seu acesso.',
    //     authMessages: const <AuthMessages>[
    //       AndroidAuthMessages(
    //         signInTitle: 'Autenticação obrigatória',
    //         biometricHint: 'Verifique a identidade',
    //       ),
    //       IOSAuthMessages(
    //         localizedFallbackTitle: 'Autenticação obrigatória',
    //         lockOut: 'Verifique a identidade',
    //       ),
    //     ],
    //   );
    //
    //   if (didAuthenticate) {
    //     await OccurrenceHandler.verifyPermissions();
    //
    //     emit(state.copyWith(loginContentState: LoginContentState.SUCCESS));
    //
    //     await Get.offAllNamed(Routes.homeRoute);
    //
    //     return;
    //   }
    // } on PlatformException catch (e) {
    //   print("PlatformException: $e");
    //   //
    // } catch (e) {
    //   SnackbarUtils.error(
    //     "Problema in",
    //     "Não foi possível conectar",
    //   );
    //
    //   return;
    //   print("Erro ao autenticar: $e");
    // }
  }
}
