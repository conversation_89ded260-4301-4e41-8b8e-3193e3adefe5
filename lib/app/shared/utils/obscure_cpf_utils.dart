import 'package:tjcemobile/app/shared/utils/validator/cpf_validator.dart';

class ObscureCPFUtils {
  static String cpf(String value) {
    String rawCpf = value.replaceAll(RegExp(r'\D'), '');

    if (rawCpf.length != 11) {
      if (!RegExp(r'^\d{14}$').hasMatch(value) &&
          !CPFValidator.isValid(value)) {
        return 'Insira um CPF válido';
      }
    }

    String maskedCpf = '***' +
        '.' +
        rawCpf.substring(3, 6) +
        '.' +
        rawCpf.substring(6, 9) +
        '-' +
        '**';

    return maskedCpf;
  }
}
