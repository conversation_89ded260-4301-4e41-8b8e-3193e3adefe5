import 'package:xml/xml.dart';

class XmlBuilderUtils {
  static void mount(
    XmlBuilder builder,
    Map<String, dynamic> element, {
    List<Map<String, dynamic>> args = const [],
  }) {
    builder.element(element['name'], nest: () {
      element['attributes'].forEach((key, value) {
        builder.attribute(key, value);
      });

      var children = args.map((arg) {
        return {
          "name": "arg${args.indexOf(arg)}",
          "attributes": {},
          "children": [
            {
              "name": arg["name"],
              "attributes": {},
              "children": [arg["value"]]
            },
          ]
        };
      }).toList();

      if (args.isNotEmpty) {
        children.forEach((child) {
          mount(builder, child);
        });

        return;
      }

      if (element['children'] == null) {
        return;
      }

      for (var child in element['children']) {
        if (child is Map && child.containsKey('text')) {
          builder.text(child['text']);
          return;
        }

        if (child is String || child is num) {
          builder.text(child);
          return;
        }

        if (child is Map<String, dynamic>) {
          mount(builder, child);
        }
      }
    });
  }
}
