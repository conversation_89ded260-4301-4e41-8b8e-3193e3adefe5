import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter/widgets.dart';
import 'package:tjcemobile/app/shared/utils/pje_parser/helpers/pje_parser_helper.dart';
import 'package:tjcemobile/app/shared/utils/xml_builder_utils.dart';
import 'package:xml/xml.dart';
import 'package:xml2json/xml2json.dart';

class PjeParserUtils {
  static String mountPrimaryXml(
    Map<String, dynamic> body, {
    List<Map<String, dynamic>> args = const [],
  }) {
    final builder = XmlBuilder();

    builder.element("soapenv:Envelope", nest: () {
      builder.attribute(
        "xmlns:soapenv",
        "http://schemas.xmlsoap.org/soap/envelope/",
      );

      builder.attribute(
        "xmlns:ser",
        "http://www.cnj.jus.br/servico-intercomunicacao-2.2.2/",
      );

      builder.attribute(
        "xmlns:tip",
        "http://www.cnj.jus.br/tipos-servico-intercomunicacao-2.2.2",
      );

      builder.element("soapenv:Header", nest: () {});
      builder.element("soapenv:Body", nest: () {
        XmlBuilderUtils.mount(builder, body, args: args);
      });
    });

    return builder.buildDocument().toXmlString(pretty: true);
  }

  static Map<String, dynamic> parsePrimaryXml(String xmlString) {
    final xml2json = Xml2Json();
    xml2json.parse(xmlString);

    var jsonOpenRally = xml2json.toOpenRally();

    Map<String, dynamic> jsonMap = jsonDecode(jsonOpenRally);

    Map<String, dynamic> json =
        jsonMap['Envelope']['Body']['consultarProcessoResposta'];

    PjeParserHelper.stringToBool(json);
    PjeParserHelper.stringToNum(json, excludeKeys: [
      "numeroDocumentoPrincipal",
      "codigoDocumento",
      "emissorDocumento",
      "descricao",
      "numero",
      "cep",
      "nome",
      "movimento",
      "dataNascimento",
      "magistradoAtuante",
      "codigoOrgao",
      "codigoLocalidade",
      "dataAjuizamento",
      "dataHora",
      "identificadorMovimento",
      "idDocumentoVinculado",
      "idDocumento",
      "tipoDocumento",
    ]);

    PjeParserHelper.stringsToMap([
      "numero",
      "inscricao",
      "dataNascimento",
      "magistradoAtuante",
      "dataAjuizamento",
      "numeroDocumentoPrincipal",
      "complemento",
      "dataHora",
    ], json);

    PjeParserHelper.mapToArray([
      "assunto",
      "magistradoAtuante",
      "outroParametro",
      "parte",
      "endereco",
      "advogado",
      "complemento",
      "documento",
      "documentoVinculado"
    ], json);

    return json;
  }

  static String mountSecondaryXml(
    Map<String, dynamic> body, {
    List<Map<String, dynamic>> args = const [],
  }) {
    final builder = XmlBuilder();
    builder.processing("xml", "version='1.0' encoding='UTF-8'");

    builder.element("soapenv:Envelope", nest: () {
      builder.attribute(
        "xmlns:soapenv",
        "http://schemas.xmlsoap.org/soap/envelope/",
      );
      builder.attribute("xmlns:ws", "http://ws.pje.cnj.jus.br/");

      builder.element("soapenv:Header", nest: () {});
      builder.element("soapenv:Body", nest: () {
        XmlBuilderUtils.mount(builder, body, args: args);
      });
    });

    return builder.buildDocument().toXmlString(pretty: true);
  }

  static List<Map<String, dynamic>> parseSecondaryXml(String xmlString) {
    final document = XmlDocument.parse(xmlString);
    final response = document.findAllElements('return');

    return response.map((element) {
      var id = element.findElements('id').singleOrNull?.firstChild?.value;
      var code = element.findElements('codigo').singleOrNull?.firstChild?.value;
      var description =
          element.findElements('descricao').singleOrNull?.firstChild?.value;

      return {
        "id": id,
        "codigo": code,
        "descricao": description,
      };
    }).toList();
  }

  static Future<Map<String, dynamic>> parseMultipartRelated(
    Response<dynamic> response, {
    bool file = false,
  }) async {
    Map<String, dynamic> data = {
      "soap": null,
      "file": null,
    };

    String responseString = utf8.decode(response.data, allowMalformed: true);

    if (file) responseString = latin1.decode(response.data);

    final boundaryMatch = RegExp(r'boundary="(.+?)"').firstMatch(
      response.headers.value('Content-Type') ?? '',
    );

    if (boundaryMatch == null) {
      throw ErrorDescription("Boundary não encontrado na resposta.");
    }

    final boundary = '--${boundaryMatch.group(1)}';
    final parts = responseString.split(boundary);

    for (var part in parts) {
      if (part.trim().isEmpty || part.contains('--$boundary--')) continue;

      final contentTypeMatch = RegExp(r'Content-Type:\s*(.+)').firstMatch(part);
      if (contentTypeMatch == null) continue;

      final contentType = contentTypeMatch.group(1)?.trim() ?? "";

      final contentSplit = part.split('\r\n\r\n');
      if (contentSplit.length < 2) continue;

      // final headers = contentSplit[0];
      final body = contentSplit.sublist(1).join('\r\n\r\n').trim();

      if (contentType
          .contains('application/xop+xml; charset=UTF-8; type="text/xml"')) {
        data["soap"] = {
          "contentType": contentType,
          "body": body,
        };
        continue;
      }

      data["file"] = {
        "contentType": contentType,
        "body": body,
      };
    }

    return data;
  }
}
