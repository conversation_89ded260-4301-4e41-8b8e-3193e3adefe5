class PjeParserHelper {
  static void stringToNum(
    Map<String, dynamic> json, {
    List<String>? excludeKeys,
  }) {
    json.forEach((key, value) {
      if (value is String && num.tryParse(value) != null) {
        if (excludeKeys != null && !excludeKeys.contains(key)) {
          json[key] = num.parse(value);
          return;
        }
      }

      if (value is Map<String, dynamic>) {
        stringToNum(value, excludeKeys: excludeKeys);
        return;
      }

      if (value is List) {
        for (var v in value) {
          if (v is Map<String, dynamic>) {
            stringToNum(v, excludeKeys: excludeKeys);
          }
        }
      }
    });
  }

  static void stringToBool(Map<String, dynamic> json) {
    json.forEach((key, value) {
      if (value is String) {
        if (value == "true") {
          json[key] = true;
        } else if (value == "false") {
          json[key] = false;
        }
      }

      if (value is Map<String, dynamic>) {
        stringToBool(value);
        return;
      }

      if (value is List) {
        for (var v in value) {
          if (v is Map<String, dynamic>) {
            stringToBool(v);
          }
        }
      }
    });
  }

  static void stringsToMap(List<String> items, Map<String, dynamic> json) {
    json.forEach((key, value) {
      if (items.contains(key) && value is String) {
        json[key] = {
          "value": value,
          "setValue": true,
        };

        return;
      }
      if (value is Map<String, dynamic>) {
        stringsToMap(items, value);
        return;
      }

      if (value is List) {
        for (var v in value) {
          if (v is Map<String, dynamic>) {
            stringsToMap(items, v);
          }
        }
      }
    });
  }

  static void mapToArray(List<String> items, Map<String, dynamic> json) {
    items.forEach((item) {
      if (json.containsKey(item)) {
        if (json[item] is! List) {
          json[item] = [json[item]];
        }
      }
    });

    json.forEach((_, value) {
      if (value is Map<String, dynamic>) {
        mapToArray(items, value);
        return;
      }

      if (value is List) {
        for (var v in value) {
          if (v is Map<String, dynamic>) {
            mapToArray(items, v);
          }
        }
      }
    });
  }
}
