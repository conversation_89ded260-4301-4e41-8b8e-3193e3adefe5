import 'package:get/get.dart';
import 'package:in_app_review/in_app_review.dart';
import 'package:tjcemobile/app/app_preferences.dart';
import 'package:tjcemobile/app/shared/utils/snackbar_utils.dart';

class ReviewAppUtils {
  static Future<void> getDialogRatingApp() async {
    final AppPreferences _appPreferences = Get.find<AppPreferences>();
    await _appPreferences.incrementOpenCount();
    int openCount = await _appPreferences.getOpenCount();

    if (openCount == 3) {
      final InAppReview inAppReview = InAppReview.instance;
      if (await inAppReview.isAvailable()) {
        try {
          await inAppReview.requestReview();
        } catch (e) {
          print("Erro ao pedir avaliação: $e");
          SnackbarUtils.error("", "Erro ao pedir avaliação");
        }
      }
    }
  }
}
