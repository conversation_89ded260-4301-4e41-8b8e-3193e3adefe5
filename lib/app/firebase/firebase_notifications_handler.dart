import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:get/get.dart';
import 'package:tjcemobile/app/app_preferences.dart';
import 'package:tjcemobile/data/model/lawyer_model.dart';
import 'package:tjcemobile/presentation/resources/routes_manager.dart';

//coverage:ignore-file
class FirebaseNotificationsHandler {
  static Future<void> init() async {
    final FirebaseMessaging firebaseMessaging = Get.find<FirebaseMessaging>();

    FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
        await FirebaseNotificationsHandler._localNotification(
            firebaseMessaging);

    bool permissionGranted =
        await FirebaseNotificationsHandler._requestPermissionGranted(
            firebaseMessaging);

    if (permissionGranted) {
      await firebaseMessaging.setForegroundNotificationPresentationOptions(
        sound: true,
        badge: true,
        alert: true,
      );

      FirebaseNotificationsHandler._onMessage(
        firebaseMessaging,
        flutterLocalNotificationsPlugin,
      );
    }
  }

  static Future<FlutterLocalNotificationsPlugin> _localNotification(
      FirebaseMessaging firebaseMessaging) async {
    final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
        FlutterLocalNotificationsPlugin();

    var androidInitialize =
        const AndroidInitializationSettings('@mipmap/ic_launcher');
    var iOSInitialize = const DarwinInitializationSettings();
    var initializationsSettings = InitializationSettings(
      android: androidInitialize,
      iOS: iOSInitialize,
    );

    await flutterLocalNotificationsPlugin.initialize(initializationsSettings);

    return flutterLocalNotificationsPlugin;
  }

  static void _onMessage(
    FirebaseMessaging firebaseMessaging,
    FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin,
  ) {
    AppPreferences appPreferences = Get.find<AppPreferences>();

    AndroidNotificationDetails androidNotificationDetails =
        AndroidNotificationDetails(
      '2',
      'flutterEmbedding',
      importance: Importance.max,
      priority: Priority.max,
      playSound: true,
    );

    NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: androidNotificationDetails,
      // iOS: const DarwinNotificationDetails(),
    );

    FirebaseMessaging.onMessage.listen((RemoteMessage remoteMessage) async {
      print("onMessage: ${remoteMessage.notification}");

      await flutterLocalNotificationsPlugin.show(
        0,
        remoteMessage.notification?.title,
        remoteMessage.notification?.body,
        platformChannelSpecifics,
        payload: remoteMessage.data['title'],
      );
    });

    FirebaseMessaging.onMessageOpenedApp
        .listen((RemoteMessage remoteMessage) async {
      print("onMessageOpenedApp: ${remoteMessage.notification}");

      if (remoteMessage.data.containsKey("route")) {
        String fullRoute = remoteMessage.data['route'];
        String route = remoteMessage.data['route'];

        if (route.contains("?")) {
          List<String> nameList = route.split("?");
          route = nameList[0];
        }

        switch (route) {
          case Routes.lawyerRoute:
            try {
              Map<String, dynamic> lawyerMap =
                  await appPreferences.getLawyerAuthenticate();

              LawyerModel lawyerModel = LawyerModel.fromJson(lawyerMap);

              if (lawyerModel.data?.advogado?.id != null &&
                  lawyerModel.data?.advogado?.cpf != null) {
                await Get.toNamed(Routes.lawyerRoute);

                return;
              }

              await Get.toNamed(Routes.lawyerLoginRoute);
            } catch (e) {}
            break;

          case Routes.travelAuthorizationOnboardingRoute:
            await Get.toNamed(fullRoute);

            break;
        }
      }
    });

    FirebaseMessaging.onBackgroundMessage((RemoteMessage remoteMessage) async {
      print("onBackgroundMessage: ${remoteMessage.notification}");
    });
  }

  static Future<bool> _requestPermissionGranted(
    FirebaseMessaging firebaseMessaging,
  ) async {
    NotificationSettings settings = await firebaseMessaging.requestPermission(
      alert: true,
      announcement: true,
      badge: true,
      carPlay: false,
      criticalAlert: true,
      provisional: true,
      sound: true,
    );

    AuthorizationStatus authorizationStatus = settings.authorizationStatus;

    if (authorizationStatus == AuthorizationStatus.authorized) {
      print("Notification permission granted.");
      return true;
    } else if (authorizationStatus == AuthorizationStatus.provisional) {
      print("Notification permission granted for provisional.");
      return true;
    } else {
      print("Notification permission not granted.");
      return false;
    }
  }
}
