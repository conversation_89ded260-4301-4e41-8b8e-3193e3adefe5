import 'dart:convert';

import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:tjcemobile/app/flavor.dart';

//coverage:ignore-start
class FirebaseRemoteConfigPreferences {
  static Future<void> fetch() async {
    Duration intervalFetch = const Duration(hours: 1);

    if (!FlavorConfig.isProd) {
      intervalFetch = const Duration(seconds: 1);
    }

    final FirebaseRemoteConfig firebaseRemoteConfig =
        Get.find<FirebaseRemoteConfig>();

    await firebaseRemoteConfig.setConfigSettings(
      RemoteConfigSettings(
        fetchTimeout: Duration.zero,
        minimumFetchInterval: intervalFetch,
      ),
    );

    try {
      await firebaseRemoteConfig.fetchAndActivate();
    } catch (e) {
      final String response =
          await rootBundle.loadString('assets/data/remote_config_default.json');
      final data = await json.decode(response);

      firebaseRemoteConfig.setDefaults(data);
    }
  }

  static String get homeBannerUrl {
    final FirebaseRemoteConfig firebaseRemoteConfig =
        Get.find<FirebaseRemoteConfig>();

    return firebaseRemoteConfig.getString('homeBannerUrl');
  }

  static String get homeBannerLinkUrl {
    final FirebaseRemoteConfig firebaseRemoteConfig =
        Get.find<FirebaseRemoteConfig>();

    return firebaseRemoteConfig.getString('homeBannerLinkUrl');
  }

  static String get mediaCourtVideoId {
    final FirebaseRemoteConfig firebaseRemoteConfig =
        Get.find<FirebaseRemoteConfig>();

    return firebaseRemoteConfig.getString('mediaCourtVideoId');
  }

  static String get variableTestUrl {
    final FirebaseRemoteConfig firebaseRemoteConfig =
        Get.find<FirebaseRemoteConfig>();

    return firebaseRemoteConfig.getString('variableTestUrl');
  }

  static String get mediaDomesticViolenceVideoId {
    final FirebaseRemoteConfig firebaseRemoteConfig =
        Get.find<FirebaseRemoteConfig>();

    return firebaseRemoteConfig.getString('mediaDomesticViolenceVideoId');
  }

  static String get womanSinalsViolence {
    final FirebaseRemoteConfig firebaseRemoteConfig =
        Get.find<FirebaseRemoteConfig>();

    return firebaseRemoteConfig.getString('womanSinalsViolence');
  }

  static String get preventionViolenceWoman {
    final FirebaseRemoteConfig firebaseRemoteConfig =
        Get.find<FirebaseRemoteConfig>();

    return firebaseRemoteConfig.getString('preventionViolenceWoman');
  }

  static String get lawMariaDaPenha {
    final FirebaseRemoteConfig firebaseRemoteConfig =
        Get.find<FirebaseRemoteConfig>();

    return firebaseRemoteConfig.getString('lawMariaDaPenha');
  }

  static String get actionsAndProjectsWoman {
    final FirebaseRemoteConfig firebaseRemoteConfig =
        Get.find<FirebaseRemoteConfig>();

    return firebaseRemoteConfig.getString('actionsAndProjectsWoman');
  }

  static String get howToReportViolenceWoman {
    final FirebaseRemoteConfig firebaseRemoteConfig =
        Get.find<FirebaseRemoteConfig>();

    return firebaseRemoteConfig.getString('howToReportViolenceWoman');
  }

  static String get signatureGovBrBaseUrl {
    final FirebaseRemoteConfig firebaseRemoteConfig =
        Get.find<FirebaseRemoteConfig>();

    return firebaseRemoteConfig.getString('SIGNATURE_GOV_BR_BASE_URL');
  }

  static String get signerGovBrBaseUrl {
    final FirebaseRemoteConfig firebaseRemoteConfig =
        Get.find<FirebaseRemoteConfig>();

    return firebaseRemoteConfig.getString('SIGNER_GOV_BR_BASE_URL');
  }

  static bool get usePjeMniSoap {
    final FirebaseRemoteConfig firebaseRemoteConfig =
        Get.find<FirebaseRemoteConfig>();

    return firebaseRemoteConfig.getBool('usePjeMniSoap');
  }

  static String get keycloakPjeBaseUrl {
    final FirebaseRemoteConfig firebaseRemoteConfig =
        Get.find<FirebaseRemoteConfig>();

    return firebaseRemoteConfig.getString('KEYCLOAK_PJE_BASE_URL');
  }

  static String get keycloakPjeGrantType {
    final FirebaseRemoteConfig firebaseRemoteConfig =
        Get.find<FirebaseRemoteConfig>();

    return firebaseRemoteConfig.getString('KEYCLOAK_PJE_GRANT_TYPE');
  }

  static String get keycloakPjeUsername {
    final FirebaseRemoteConfig firebaseRemoteConfig =
        Get.find<FirebaseRemoteConfig>();

    return firebaseRemoteConfig.getString('KEYCLOAK_PJE_USERNAME');
  }

  static String get keycloakPjePassword {
    final FirebaseRemoteConfig firebaseRemoteConfig =
        Get.find<FirebaseRemoteConfig>();

    return firebaseRemoteConfig.getString('KEYCLOAK_PJE_PASSWORD');
  }

  static String get keycloakPje1GClientId {
    final FirebaseRemoteConfig firebaseRemoteConfig =
        Get.find<FirebaseRemoteConfig>();

    return firebaseRemoteConfig.getString('KEYCLOAK_PJE_1G_CLIENT_ID');
  }

  static String get keycloakPje1GClientSecret {
    final FirebaseRemoteConfig firebaseRemoteConfig =
        Get.find<FirebaseRemoteConfig>();

    return firebaseRemoteConfig.getString('KEYCLOAK_PJE_1G_CLIENT_SECRET');
  }

  static String get keycloakPje2GClientId {
    final FirebaseRemoteConfig firebaseRemoteConfig =
        Get.find<FirebaseRemoteConfig>();

    return firebaseRemoteConfig.getString('KEYCLOAK_PJE_2G_CLIENT_ID');
  }

  static String get keycloakPje2GClientSecret {
    final FirebaseRemoteConfig firebaseRemoteConfig =
        Get.find<FirebaseRemoteConfig>();

    return firebaseRemoteConfig.getString('KEYCLOAK_PJE_2G_CLIENT_SECRET');
  }

  static String get pjeConsultantUser {
    final FirebaseRemoteConfig firebaseRemoteConfig =
        Get.find<FirebaseRemoteConfig>();

    return firebaseRemoteConfig.getString('PJE_CONSULTANT_USER');
  }

  static String get pjeConsultantPassword {
    final FirebaseRemoteConfig firebaseRemoteConfig =
        Get.find<FirebaseRemoteConfig>();

    return firebaseRemoteConfig.getString('PJE_CONSULTANT_PASSWORD');
  }
}
//coverage:ignore-end
