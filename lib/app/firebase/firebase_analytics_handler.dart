import 'dart:convert';

import 'package:firebase_analytics/firebase_analytics.dart';

//coverage:ignore-file
class FirebaseAnalyticsHandler {
  static Future<void> click(
    String route,
    String button, {
    Map? arguments,
  }) async {
    Map? data = {
      'route': route,
      'button': button,
    };

    if (arguments != null) data.addAll(arguments);

    await FirebaseAnalytics.instance.logSelectContent(
      contentType: 'click',
      itemId: button,
      parameters: {'data': json.encode(data)},
    );
  }

  static Future<void> contentView(
    String route, {
    Map? arguments,
  }) async {
    Map? data = {'route': route};

    if (arguments != null) data.addAll(arguments);

    await FirebaseAnalytics.instance.logScreenView(
      screenClass: 'MainActivity',
      screenName: route,
      parameters: {'data': json.encode(data)},
    );
  }

  static Future<void> errorEvent(String message) async {
    await FirebaseAnalytics.instance.logEvent(name: 'error', parameters: {
      'message': message,
    });
  }
}
