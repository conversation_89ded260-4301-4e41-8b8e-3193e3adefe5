import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:tjcemobile/app/shared/utils/url_launcher_utils.dart';
import 'package:tjcemobile/presentation/resources/color_manager.dart';

class OmbudsmanUnavailableDialog extends StatelessWidget {
  OmbudsmanUnavailableDialog({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Container(
        child: Column(
          children: <Widget>[
            Icon(
              Icons.error_outline_rounded,
              size: 24.sp,
              color: ColorManager.errorColor,
            ),
            SizedBox(height: 8.h),
            Text(
              "Serviço temporariamente indisponível",
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
      content: Text.rich(
        TextSpan(
          children: <TextSpan>[
            TextSpan(
                text:
                    "Este serviço está temporariamente indisponível, mas os canais de atendimento da Ouvidoria continuam ativos!\nVocê pode entrar em contato pelos nossos canais oficiais.\n"),
            TextSpan(text: "\nAcesse:"),
            TextSpan(text: " "),
            TextSpan(
              text:
                  "https://www.tjce.jus.br/ouvidoria/registre-sua-manifestacao/",
              style: TextStyle(color: Colors.blue),
              recognizer: TapGestureRecognizer()
                ..onTap = () async {
                  await UrlLauncherUtils.launchUrl(
                      "https://www.tjce.jus.br/ouvidoria/registre-sua-manifestacao/");
                },
            ),
            TextSpan(text: " "),
            TextSpan(text: "para mais informações."),
          ],
        ),
        textAlign: TextAlign.justify,
      ),
      actions: <Widget>[
        TextButton(
            child: Text("OK"),
            // coverage:ignore-start
            onPressed: () => Get.back()
            // coverage:ignore-end
            ),
      ],
    );
  }
}
