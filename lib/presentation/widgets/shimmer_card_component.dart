import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shimmer/shimmer.dart';
import 'package:tjcemobile/presentation/resources/color_manager.dart';
import 'package:tjcemobile/presentation/resources/padding_manager.dart';

class ShimmerCardComponent extends StatelessWidget {
  final int _count;

  ShimmerCardComponent({int count = 5, Key? key})
      : this._count = count,
        super(key: key);

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      physics: NeverScrollableScrollPhysics(),
      child: Container(
        margin: PaddingManager.small,
        child: Column(
          children: List.generate(this._count, (int index) {
            return Column(
              mainAxisAlignment: MainAxisAlignment.start,
              children: <Widget>[
                SizedBox(height: 12.r),
                Shimmer.fromColors(
                  baseColor: ColorManager.borderColor,
                  highlightColor:
                      ColorManager.borderColor.withAlpha((0.25 * 255).toInt()),
                  child: Container(
                    width: context.width,
                    height: 160.r,
                    decoration: BoxDecoration(
                      color: ColorManager.whiteColor
                          .withAlpha((0.35 * 255).toInt()),
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                  ),
                ),
              ],
            );
          }),
        ),
      ),
    );
  }
}
