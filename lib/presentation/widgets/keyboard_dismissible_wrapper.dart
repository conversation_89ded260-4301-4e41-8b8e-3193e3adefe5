import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class KeyboardDismissibleWrapper extends StatefulWidget {
  final Widget child;

  const KeyboardDismissibleWrapper({
    Key? key,
    required this.child,
  }) : super(key: key);

  @override
  State<KeyboardDismissibleWrapper> createState() =>
      _KeyboardDismissibleWrapperState();
}

class _KeyboardDismissibleWrapperState
    extends State<KeyboardDismissibleWrapper> {
  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      SystemChannels.textInput.invokeMethod('TextInput.hide');
    });
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
      },
      child: widget.child,
    );
  }
}
