import 'package:flutter/material.dart';
import 'package:flutter/semantics.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';


enum ButtonType {
  icon,
  elevated,
  text,
  inkWell,
  gestureDetector,
  material,
}

class AccessibilityUtils {
  static Widget button({
    final VoidCallback? onPressed,
    final VoidCallback? ontap,
    required final Widget child,
    required final String semanticLabel,
    final String? semanticHint,
    final String? tooltipMessage,
    final Color? color,
    final double? padding,
    final double? iconSize,
    final bool excludeSemantics = false,
    final bool showTooltip = false,
    final bool isPopupItem = false,
    final bool isSegmentItem = false,
    final ButtonType type = ButtonType.icon,
    final double? borderRadius,
    final bool container = true,
    final bool isIconButton = false,
  }) {
    if (isPopupItem || isSegmentItem || isIconButton) {
      return InkWell(
        onTap: onPressed ?? ontap,
        borderRadius: BorderRadius.circular(borderRadius ?? 0),
        child: Semantics(
          //  button: true,
          label: semanticLabel,
          hint: semanticHint,
          explicitChildNodes: true,
          child: child,
        ),
      );
    }
    if (ontap != null) {
      return InkWell(
        onTap: ontap,
        child: _buildSemanticsWrapper(
          semanticLabel: semanticLabel,
          semanticHint: semanticHint,
          excludeSemantics: excludeSemantics,
          padding: padding,
          child: child,
        ),
      );
    }
    switch (type) {
      case ButtonType.elevated:
        return ElevatedButton(
          onPressed: onPressed,
          child: _buildSemanticsWrapper(
            semanticLabel: semanticLabel,
            semanticHint: semanticHint,
            excludeSemantics: excludeSemantics,
            padding: padding,
            child: child,
          ),
        );
      case ButtonType.text:
        return TextButton(
          onPressed: onPressed,
          child: _buildSemanticsWrapper(
            semanticLabel: semanticLabel,
            semanticHint: semanticHint,
            excludeSemantics: excludeSemantics,
            padding: padding,
            child: child,
          ),
        );
      case ButtonType.inkWell:
        return InkWell(
          onTap: ontap,
          child: _buildSemanticsWrapper(
            semanticLabel: semanticLabel,
            semanticHint: semanticHint,
            excludeSemantics: excludeSemantics,
            padding: padding,
            child: child,
          ),
        );
      case ButtonType.gestureDetector:
        return GestureDetector(
          onTap: ontap,
          child: _buildSemanticsWrapper(
            semanticLabel: semanticLabel,
            semanticHint: semanticHint,
            excludeSemantics: excludeSemantics,
            padding: padding,
            child: child,
          ),
        );
      case ButtonType.material:
        return MaterialButton(
          onPressed: onPressed,
          child: _buildSemanticsWrapper(
            semanticLabel: semanticLabel,
            semanticHint: semanticHint,
            excludeSemantics: excludeSemantics,
            padding: padding,
            child: child,
          ),
        );
      default:
        return IconButton(
          color: color ?? Get.theme.colorScheme.outline,
          onPressed: onPressed,
          iconSize: iconSize ?? 24.sp,
          icon: showTooltip
              ? Tooltip(
                  message: tooltipMessage ?? semanticLabel,
                  child: _buildSemanticsWrapper(
                    semanticLabel: semanticLabel,
                    semanticHint: semanticHint,
                    excludeSemantics: excludeSemantics,
                    padding: padding,
                    iconColor: color ?? Get.theme.iconTheme.color,
                    child: child,
                  ),
                )
              : _buildSemanticsWrapper(
                  semanticLabel: semanticLabel,
                  semanticHint: semanticHint,
                  excludeSemantics: excludeSemantics,
                  padding: padding,
                  iconColor: color ?? Get.theme.iconTheme.color,
                  child: child,
                ),
        );
    }
  }

  static Widget _buildSemanticsWrapper({
    required final String semanticLabel,
    required final Widget child,
    final String? semanticHint,
    final bool excludeSemantics = false,
    final double? padding,
    final Color? iconColor,
    final bool container = true,
    final AlignmentGeometry? alignment,
  }) {
    return MergeSemantics(
      child: Semantics(
        // button: true,
        explicitChildNodes: true,
        label: semanticLabel,
        container: container,
        hint: semanticHint,
        child: excludeSemantics
            ? Container(
                alignment: alignment,
                padding: EdgeInsets.all(padding ?? 0),
                child: ExcludeSemantics(
                  child: IconTheme(
                      data: IconThemeData(color: iconColor), child: child),
                ),
              )
            : IconTheme(data: IconThemeData(color: iconColor), child: child),
      ),
    );
  }

  static Future<void> announce(String message,
      {TextDirection direction = TextDirection.ltr,
      Duration delay = const Duration(milliseconds: 10)}) async {
    Get.focusScope?.unfocus();
    await Future.delayed(delay);
    SemanticsService.announce(message, direction);
  }

  static Widget dialog({
    required String semanticLabel,
    required Widget child,
  }) {
    return Semantics(
      container: true,
      explicitChildNodes: true,
      label: semanticLabel,
      child: ExcludeSemantics(
        child: child,
      ),
    );
  }
}
