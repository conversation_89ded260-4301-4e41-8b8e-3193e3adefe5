import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:tjcemobile/app/firebase/firebase_analytics_handler.dart';
import 'package:tjcemobile/presentation/resources/color_manager.dart';
import 'package:tjcemobile/presentation/resources/font_manager.dart';
import 'package:tjcemobile/presentation/resources/padding_manager.dart';
import 'package:tjcemobile/presentation/widgets/custom_base_card.dart';

class ListTileCardComponent extends StatelessWidget {
  final String? urlFile;
  final String? title;
  final String? subTitle;
  final Icon? icon;
  final double? sizeIcon;
  final double? height;
  final VoidCallback? onTap;
  final String? imageName;

  const ListTileCardComponent({
    Key? key,
    this.urlFile,
    this.onTap,
    this.title,
    this.subTitle,
    this.icon,
    this.sizeIcon,
    this.height,
    this.imageName,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return CustomBaseCard(
      child: Container(
        height: height != null ? height : null,
        padding: PaddingManager.medium,
        child: Row(
          children: <Widget>[
            if (this.imageName == null) ...[
              icon == null
                  ? Icon(
                      Icons.picture_as_pdf,
                      color: ColorManager.secondaryColor,
                      size: sizeIcon != null ? sizeIcon : null,
                    )
                  : icon!,
            ] else ...[
              Image.asset(
                'assets/media/${this.imageName}',
                height: context.height * 0.1.r,
                width: context.width * 0.1.r,
              ),
            ],
            SizedBox(width: 12.r),
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(
                    this.urlFile != null
                        ? this._splitOnUrl(urlFile)
                        : title ?? "-",
                    style: this.urlFile != null
                        ? TextStyle(
                            fontSize: FontSizeManager.s12,
                          )
                        : TextStyle(
                            fontSize: FontSizeManager.s14,
                            fontWeight: FontWeightManager.bold,
                          ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 3,
                  ),
                  if (subTitle != null) ...[
                    Text(
                      this.urlFile != null
                          ? this._splitOnUrl(urlFile)
                          : subTitle ?? "-",
                      style: TextStyle(
                        fontSize: FontSizeManager.s10,
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 3,
                    ),
                  ]
                ],
              ),
            ),
            Icon(
              Icons.keyboard_arrow_right,
              color: ColorManager.outlineColor,
            ),
          ],
        ),
      ),
      onTap: this.onTap != null
          ? () async {
              await FirebaseAnalyticsHandler.click(
                'list_tile_card_component',
                this.title ?? '-',
              );

              this.onTap?.call();
            }
          : null,
    );
  }

  String _splitOnUrl(String? url) {
    List<String>? fileNameDocProcesso = url?.split('/');
    String? finalName = fileNameDocProcesso?[6];

    Map<String, String> fileTypeMapping = {
      "doc_frente": "Frente do documento de identificação",
      "doc_verso": "Verso do documento de identificação",
      "endereco": "Comprovante de endereço",
      "certidao": "Certidão",
      "passaporte": "Passaporte",
      "passagem": "Passagem",
      "anexo1": "Anexo importante nº 1",
      "anexo2": "Anexo importante nº 2",
      "anexo3": "Anexo importante nº 3",
      "anexo4": "Anexo importante nº 4",
      "autorizacao_frente": "Autorização de viagem (Genitor 1)",
      "autorizacao_verso": "Autorização de viagem (Genitor 2)",
    };

    if (finalName != null) {
      for (var entry in fileTypeMapping.entries) {
        if (finalName.contains(entry.key)) {
          return entry.value;
        }
      }

      return finalName.capitalizeFirst ?? "-";
    }

    return "-";
  }
}
