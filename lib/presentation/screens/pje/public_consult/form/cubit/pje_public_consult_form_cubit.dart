import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';
import 'package:tjcemobile/data/model/company_model.dart';
import 'package:tjcemobile/data/model/keycloak_authorization_model.dart';
import 'package:tjcemobile/data/model/process_model.dart';
import 'package:tjcemobile/data/network/failure.dart';
import 'package:tjcemobile/domain/usecase/pje/pje_public_consult_form_usecase.dart';
import 'package:tjcemobile/domain/viewmodel/cnpj_viewmodel.dart';
import 'package:tjcemobile/domain/viewmodel/pje_public_consult_viewmodel.dart';
import 'package:tjcemobile/domain/viewmodel/process_consult_viewmodel.dart';
import 'package:tjcemobile/presentation/resources/routes_manager.dart';

part 'pje_public_consult_form_state.dart';

class PjePublicConsultFormCubit extends Cubit<PjePublicConsultFormState> {
  final PjePublicConsultFormUseCase _useCase;

  PjePublicConsultFormCubit(this._useCase)
      : super(
          PjePublicConsultFormState(viewModel: PjePublicConsultViewModel()),
        );

  List<String> _list = [];

  Future<void> initFormView() async {
    this._list = [];

    emit(state.copyWith(
      viewContentState: ViewContentState.LOADING,
    ));

    await Future.delayed(Duration(seconds: 1));

    emit(state.copyWith(
      viewContentState: ViewContentState.SUCCESS,
    ));
  }

  void changeState({PjePublicConsultViewModel? viewModel}) {
    emit(state.copyWith(viewModel: viewModel));

    Get.forceAppUpdate();
  }

  void changeUseNamePart() {
    emit(state.copyWith(useNamePart: !state.useNamePart));
  }

  set isCPF(bool isCPF) {
    emit(state.copyWith(isCPF: isCPF));
  }

  Future<void> validateCNJP(String value) async {
    CNPJViewModel viewModel = CNPJViewModel(value.numericOnly());

    emit(state.copyWith(
      validateCNPJContentState: ValidateCNPJContentState.LOADING,
      isValidCNPJ: null,
    ));

    (await this._useCase.getCompanyByCNPJ(viewModel)).fold(
      (Failure failure) {
        emit(state.copyWith(
          validateCNPJContentState: ValidateCNPJContentState.ERROR,
          isValidCNPJ: false,
          failure: failure,
        ));
      },
      (CompanyModel companyModel) {
        emit(state.copyWith(
          validateCNPJContentState: ValidateCNPJContentState.SUCCESS,
          isValidCNPJ: true,
        ));
      },
    );
  }

  Future<void> getProcessByFilter() async {
    this._list = [];

    emit(state.copyWith(
      searchProcessesContentState: SearchProcessesContentState.LOADING,
    ));

    String? numberProcess = state.viewModel.numberProcess;
    String? namePart = state.viewModel.namePart;

    if (numberProcess != null && numberProcess.isNotEmpty) {
      this._list.add(numberProcess);
    }

    if (namePart != null && namePart.isNotEmpty) {
      var list = await this._postProcessName(namePart);

      if (list.isEmpty) {
        emit(state.copyWith(
          searchProcessesContentState: SearchProcessesContentState.DEFAULT,
        ));

        return;
      }

      this._list.addAll(list);
    }

    emit(state.copyWith(
      searchProcessesContentState: SearchProcessesContentState.DEFAULT,
    ));

    await this._redirectToList(this._list);
  }

  Future<void> _redirectToList(List<String> list) async {
    await Get.toNamed(Routes.pjePublicConsultListRoute,
        arguments: {"processNumberList": list});
  }

  Future<List<String>> _postProcessName(String name) async {
    List<String> list = [];

    ProcessConsultViewModel viewModel = ProcessConsultViewModel(
      nomeParte: name,
      criterioConsulta: '3',
      primeiroRegistro: '0',
      quantidadeRegistros: '9999',
    );

    await (await this._useCase.postProcessName(viewModel)).fold(
      (Failure failure) => null,
      (ProcessModel processModel) async {
        processModel.data?.forEach((item) {
          if (item.numero?.length == 20) {
            if (["PJE1G", "PJE2G"].contains(item.sistema ?? "")) {
              var process =
                  "${item.numeroFormatado ?? ""}/${item.sistema ?? ""}";

              list.add(process);
            }
          }
        });
      },
    );

    return list;
  }
}
