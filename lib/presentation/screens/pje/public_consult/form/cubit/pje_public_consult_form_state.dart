part of 'pje_public_consult_form_cubit.dart';

enum ViewContentState { DEFAULT, LOADING, SUCCESS, ERROR }

enum ProcessContentState { DEFAULT, LOADING, SUCCESS, ERROR }

enum ValidateCNPJContentState { DEFAULT, LOADING, SUCCESS, ERROR }

enum SearchProcessesContentState { DEFAULT, LOADING, SUCCESS, ERROR }

class PjePublicConsultFormState extends Equatable {
  final ViewContentState viewContentState;
  final PjePublicConsultViewModel viewModel;
  final Failure? failure;
  final ProcessContentState processContentState;
  final bool isCPF;
  final ValidateCNPJContentState validateCNPJContentState;
  final bool isValidCNPJ;
  final SearchProcessesContentState searchProcessesContentState;
  final KeycloakAuthorizationModel? keycloakAuthorizationModel;
  final bool useNamePart;

  const PjePublicConsultFormState({
    this.viewContentState = ViewContentState.DEFAULT,
    required this.viewModel,
    this.failure,
    this.processContentState = ProcessContentState.DEFAULT,
    this.isCPF = false,
    this.validateCNPJContentState = ValidateCNPJContentState.DEFAULT,
    this.isValidCNPJ = false,
    this.searchProcessesContentState = SearchProcessesContentState.DEFAULT,
    this.keycloakAuthorizationModel,
    this.useNamePart = false,
  });

  PjePublicConsultFormState copyWith({
    ViewContentState? viewContentState,
    PjePublicConsultViewModel? viewModel,
    Failure? failure,
    ProcessContentState? processContentState,
    bool? isCPF,
    ValidateCNPJContentState? validateCNPJContentState,
    bool? isValidCNPJ,
    SearchProcessesContentState? searchProcessesContentState,
    KeycloakAuthorizationModel? keycloakAuthorizationModel,
    bool? useNamePart,
  }) {
    return PjePublicConsultFormState(
      viewContentState: viewContentState ?? this.viewContentState,
      viewModel: viewModel ?? this.viewModel,
      failure: failure ?? this.failure,
      processContentState: this.processContentState,
      isCPF: isCPF ?? this.isCPF,
      validateCNPJContentState:
          validateCNPJContentState ?? this.validateCNPJContentState,
      isValidCNPJ: isValidCNPJ ?? this.isValidCNPJ,
      searchProcessesContentState:
          searchProcessesContentState ?? this.searchProcessesContentState,
      keycloakAuthorizationModel:
          keycloakAuthorizationModel ?? this.keycloakAuthorizationModel,
      useNamePart: useNamePart ?? this.useNamePart,
    );
  }

  List<Object?> get props => [
        viewContentState,
        viewModel,
        failure,
        processContentState,
        isCPF,
        validateCNPJContentState,
        isValidCNPJ,
        searchProcessesContentState,
        keycloakAuthorizationModel,
        useNamePart,
      ];
}
