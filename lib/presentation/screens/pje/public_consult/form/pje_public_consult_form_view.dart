import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:tjcemobile/app/shared/enum/input_format_enum.dart';
import 'package:tjcemobile/app/shared/utils/snackbar_utils.dart';
import 'package:tjcemobile/app/shared/utils/validator_utils.dart';
import 'package:tjcemobile/presentation/resources/color_manager.dart';
import 'package:tjcemobile/presentation/resources/font_manager.dart';
import 'package:tjcemobile/presentation/resources/padding_manager.dart';
import 'package:tjcemobile/presentation/screens/pje/public_consult/form/cubit/pje_public_consult_form_cubit.dart';
import 'package:tjcemobile/presentation/widgets/arrow_back_component.dart';
import 'package:tjcemobile/presentation/widgets/custom_base_card.dart';
import 'package:tjcemobile/presentation/widgets/custom_base_scaffold.dart';
import 'package:tjcemobile/presentation/widgets/custom_text_form_field.dart';
import 'package:tjcemobile/presentation/widgets/error_component.dart';
import 'package:tjcemobile/presentation/widgets/primary_button.dart';

class PjePublicConsultFormView extends StatefulWidget {
  const PjePublicConsultFormView({super.key});

  @override
  State<PjePublicConsultFormView> createState() =>
      _PjePublicConsultFormViewState();
}

class _PjePublicConsultFormViewState extends State<PjePublicConsultFormView> {
  final PjePublicConsultFormCubit _cubit =
      Get.find<PjePublicConsultFormCubit>();

  final _formKey = GlobalKey<FormState>();
  final TextEditingController _processController = TextEditingController();

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await this._cubit.initFormView();
    });
  }

  @override
  void dispose() {
    _processController.dispose();
    Get.delete<PjePublicConsultFormCubit>(force: true);

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return CustomBaseScaffold(
      appBar: this._buildAppBar(),
      body: this._buildBody(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
          semanticsLabel:
              "Área da consulta pública, você pode pesquisar seu processo por nome da parte ou pelo número",
          "Consulta Pública"),
      leading: ArrowBackComponent(onPressed: () => Get.back()),
    );
  }

  Widget _buildBody() {
    return BlocBuilder<PjePublicConsultFormCubit, PjePublicConsultFormState>(
      bloc: Get.find<PjePublicConsultFormCubit>(),
      builder: (BuildContext context, PjePublicConsultFormState state) {
        if (state.viewContentState == ViewContentState.LOADING) {
          return Center(
            child: CircularProgressIndicator(color: context.theme.primaryColor),
          );
        }

        if (state.viewContentState == ViewContentState.ERROR) {
          return ErrorComponent(failure: state.failure);
        }

        if (state.viewContentState == ViewContentState.SUCCESS) {
          return Column(
            children: <Widget>[
              SizedBox(
                width: 240.r,
                child: Text(
                  "Você pode filtrar pelo número do processo ou nome da parte",
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: FontSizeManager.s14,
                    fontWeight: FontWeight.bold,
                    color: ColorManager.whiteColor,
                  ),
                ),
              ),
              SizedBox(height: 32.r),
              Expanded(
                child: CustomBaseCard(
                  child: Padding(
                    padding: PaddingManager.medium,
                    child: this._buildForm(state),
                  ),
                ),
              ),
              SizedBox(height: 8.r),
              if (state.searchProcessesContentState !=
                  SearchProcessesContentState.LOADING) ...[
                SizedBox(
                  width: context.width,
                  child: PrimaryButton(
                    "Pesquisar",
                    // coverage:ignore-start
                    onPressed: () async {
                      if (this._formKey.currentState!.validate()) {
                        if (state.viewModel.numberProcess == null &&
                            state.viewModel.namePart == null) {
                          SnackbarUtils.error(
                            "Não foi possível realizar a pesquisa",
                            "Você deve preencher pelo menos um campo.",
                          );

                          return;
                        }

                        await this._cubit.getProcessByFilter();
                      }
                    },
                    // coverage:ignore-end
                  ),
                ),
              ],
              if (state.searchProcessesContentState ==
                  SearchProcessesContentState.LOADING) ...[
                // coverage:ignore-start
                CircularProgressIndicator(color: context.theme.primaryColor),
                // coverage:ignore-end
              ],
              SizedBox(height: 8.r),
            ],
          );
        }

        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildForm(PjePublicConsultFormState state) {
    return Form(
      key: this._formKey,
      child: ListView(
        children: <Widget>[
          SizedBox(height: 4.r),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              Text(
                "Processo",
                style: TextStyle(fontSize: FontSizeManager.s14),
              ),
              Semantics(
                value: state.useNamePart
                    ? "Nome parte selecionado"
                    : "Processo selecionado",
                label: state.useNamePart
                    ? "Nome parte selecionado"
                    : "Processo selecionado",
                child: Switch(
                  inactiveThumbColor: context.theme.colorScheme.primary,
                  inactiveTrackColor: context.theme.colorScheme.inversePrimary,
                  activeColor: context.theme.colorScheme.secondary,
                  value: state.useNamePart,
                  // coverage:ignore-start
                  onChanged: (bool value) {
                    Get.focusScope?.unfocus();

                    _processController.text = '';
                    state.viewModel.numberProcess = null;
                    state.viewModel.namePart = null;

                    this._cubit.changeState(viewModel: state.viewModel);
                    this._cubit.changeUseNamePart();
                  },
                  trackOutlineColor: WidgetStateProperty.resolveWith(
                    (final Set<WidgetState> states) {
                      if (states.contains(WidgetState.selected)) {
                        return context.theme.colorScheme.secondary;
                      }

                      return context.theme.colorScheme.primary;
                    },
                  ),
                  // coverage:ignore-end
                ),
              ),
              Text(
                "Nome da parte",
                style: TextStyle(fontSize: FontSizeManager.s14),
              ),
            ],
          ),
          SizedBox(height: 8.r),
          if (!state.useNamePart) ...[
            this._processFormField(state),
          ] else ...[
            CustomTextFormField(
              semanticsHint:
                  "Preencha o nome da parte, depois clique em pesquisar para fazer a pesquisa",
              labelText: "Nome da parte",
              textCapitalization: TextCapitalization.words,
              inputFormat: InputFormatEnum.TEXT,
              keyboardType: TextInputType.text,
              // coverage:ignore-start
              validator: (String? value) {
                if (value == null || value.isEmpty) return null;

                return ValidatorUtils.validateTwoStrings(
                  value,
                  fieldName: "nome e sobrenome",
                );
              },
              onChanged: (String? value) {
                if (value != null && value.isNotEmpty) {
                  state.viewModel.numberProcess = null;
                }

                state.viewModel.namePart = value;

                this._cubit.changeState(viewModel: state.viewModel);
              },
              // coverage:ignore-end
            ),
          ],
          //ToDo:: Descomentar para aplicar os filtros da web do PJe
          // SizedBox(height: 16.r),
          // Text("Processo Referência (Numeração)"),
          // SizedBox(height: 4.r),
          // Row(
          //   children: <Widget>[
          //     Expanded(
          //       child: RadioListTile<String>(
          //         title: Text("Única"),
          //         value: "unique",
          //         contentPadding: EdgeInsets.zero,
          //         activeColor: ColorManager.secondaryColor,
          //         groupValue: state.viewModel.typeReferenceProcess,
          //         onChanged: (String? value) {
          //           if (value == null) return;
          //
          //           state.viewModel.typeReferenceProcess = value;
          //
          //           this._cubit.changeState(viewModel: state.viewModel);
          //         },
          //       ),
          //     ),
          //     Expanded(
          //       child: RadioListTile<String>(
          //         title: Text("Livre"),
          //         value: "free",
          //         contentPadding: EdgeInsets.zero,
          //         activeColor: ColorManager.secondaryColor,
          //         groupValue: state.viewModel.typeReferenceProcess,
          //         onChanged: (String? value) {
          //           if (value == null) return;
          //
          //           state.viewModel.typeReferenceProcess = value;
          //
          //           this._cubit.changeState(viewModel: state.viewModel);
          //         },
          //       ),
          //     ),
          //   ],
          // ),
          // this._numberReferenceProcessFormField(state),
          // SizedBox(height: 16.r),
          // CustomTextFormField(
          //   labelText: "Nome da Parte",
          //   // coverage:ignore-start
          //   onChanged: (String? value) {
          //     state.viewModel.namePart = value;
          //
          //     this._cubit.changeState(viewModel: state.viewModel);
          //   },
          //   // coverage:ignore-end
          // ),
          // SizedBox(height: 16.r),
          // Text("Inscrição na Receita Federal"),
          // SizedBox(height: 4.r),
          // this._documentFormField(state),
          // SizedBox(height: 16.r),
          // Text("OAB"),
          // SizedBox(height: 4.r),
          // CustomTextFormField(
          //   labelText: "Número",
          //   keyboardType: TextInputType.number,
          //   validator: (String? value) {
          //     if (value == null || value.isEmpty) return null;
          //
          //     return ValidatorUtils.validateString(
          //       value,
          //       fieldName: "número",
          //     );
          //   },
          //   maxLength: 7,
          //   onChanged: (String? value) async {
          //     state.viewModel.numberOAB = value;
          //
          //     this._cubit.changeState(viewModel: state.viewModel);
          //   },
          // ),
          // SizedBox(height: 8.r),
          // CustomTextFormField(
          //   labelText: "Letra",
          //   textCapitalization: TextCapitalization.characters,
          //   inputFormat: InputFormatEnum.LETTER,
          //   validator: (String? value) {
          //     if (value == null || value.isEmpty) return null;
          //
          //     return ValidatorUtils.validateLetter(value);
          //   }, // coverage:ignore-start
          //   onChanged: (String? value) {
          //     state.viewModel.letterOAB = value;
          //
          //     this._cubit.changeState(viewModel: state.viewModel);
          //   },
          //   // coverage:ignore-end
          // ),
          // SizedBox(height: 8.r),
          // CustomTextFormField(
          //   labelText: "UF",
          //   textCapitalization: TextCapitalization.characters,
          //   inputFormat: InputFormatEnum.UF,
          //   keyboardType: TextInputType.text,
          //   validator: (String? value) {
          //     if (value == null || value.isEmpty) return null;
          //
          //     return ValidatorUtils.validateState(value);
          //   }, // coverage:ignore-start
          //   onChanged: (String? value) {
          //     state.viewModel.stateOAB = value;
          //
          //     this._cubit.changeState(viewModel: state.viewModel);
          //   },
          //   // coverage:ignore-end
          // ),
        ],
      ),
    );
  }

  Widget _processFormField(PjePublicConsultFormState state) {
    String? helperText;
    Widget? suffix;

    if (state.processContentState == ProcessContentState.LOADING) {
      helperText = "Buscando processo...";
      // coverage:ignore-start
      suffix = Container(
        padding: const EdgeInsets.all(16.0),
        child: const CircularProgressIndicator(),
      );
      // coverage:ignore-end
    }

    if (state.processContentState == ProcessContentState.ERROR) {
      helperText = "O numero de processo não é válido";
      // coverage:ignore-start
      suffix = Icon(Icons.error_outline, size: 24.sp, color: Colors.red);
      // coverage:ignore-end
    }

    if (state.processContentState == ProcessContentState.SUCCESS) {
      helperText = "Processo encontrado";
      suffix = Icon(Icons.check, size: 24.sp, color: Colors.green);
    }

    return CustomTextFormField(
      controller: _processController,
      semanticsHint:
          "Preencha o número do processo, depois clique em pesquisar para fazer a pesquisa",
      labelText: "Processo",
      keyboardType: TextInputType.number,
      inputFormat: InputFormatEnum.PROCESS,
      suffix: suffix,
      helperText: helperText,
      validator: (String? value) {
        if (value == null || value.isEmpty || value == '-') return null;

        return ValidatorUtils.validateProcess(value);
      },
      // coverage:ignore-start
      onChanged: (String? value) async {
        if (value == null || value.isEmpty || value == '-') {
          _processController.text = '';
          state.viewModel.numberProcess = null;
          this._cubit.changeState(viewModel: state.viewModel);
          return;
        }

        if (value.length == 25) {
          Get.focusScope?.unfocus();

          state.viewModel.namePart = null;
          state.viewModel.numberProcess = value;

          this._cubit.changeState(viewModel: state.viewModel);
        }
      },
      // coverage:ignore-end
    );
  }

  Widget _numberReferenceProcessFormField(PjePublicConsultFormState state) {
    String? helperText;
    Widget? suffix;

    if (state.processContentState == ProcessContentState.LOADING) {
      helperText = "Buscando processo...";
      // coverage:ignore-start
      suffix = Container(
        padding: const EdgeInsets.all(16.0),
        child: const CircularProgressIndicator(),
      );
      // coverage:ignore-end
    }

    if (state.processContentState == ProcessContentState.ERROR) {
      helperText = "O numero de processo não é válido";
      // coverage:ignore-start
      suffix = Icon(Icons.error_outline, size: 24.sp, color: Colors.red);
      // coverage:ignore-end
    }

    if (state.processContentState == ProcessContentState.SUCCESS) {
      helperText = "Processo encontrado";
      suffix = Icon(Icons.check, size: 24.sp, color: Colors.green);
    }

    return CustomTextFormField(
      labelText: "Numeração",
      keyboardType: TextInputType.numberWithOptions(
        signed: true,
      ),
      inputFormat: InputFormatEnum.PROCESS,
      suffix: suffix,
      helperText: helperText,
      validator: ValidatorUtils.validateProcess,
      // coverage:ignore-start
      onChanged: (String? value) async {
        if (value == null || value.length == 0) {
          state.viewModel.numberReferenceProcess = "";

          this._cubit.changeState(viewModel: state.viewModel);

          return;
        }

        if (value.isNotEmpty && value.length == 25) {
          Get.focusScope?.unfocus();

          value = value.numericOnly();

          state.viewModel.numberReferenceProcess = value;

          this._cubit.changeState(viewModel: state.viewModel);

          // await this._cubit.getProcessNumber(value);
        }
      },
      // coverage:ignore-end
    );
  }

  Widget _documentFormField(PjePublicConsultFormState state) {
    String? helperText;
    Widget? suffix;

    if (state.validateCNPJContentState == ValidateCNPJContentState.LOADING) {
      helperText = "Buscando CNPJ...";
      // coverage:ignore-start
      suffix = Container(
        padding: const EdgeInsets.all(16.0),
        child: CircularProgressIndicator(color: context.theme.primaryColor),
      );
      // coverage:ignore-end
    }

    if (state.validateCNPJContentState == ValidateCNPJContentState.ERROR) {
      helperText = "O CNPJ não é válido";
      // coverage:ignore-start
      suffix = Icon(Icons.error_outline, size: 24.sp, color: Colors.red);
      // coverage:ignore-end
    }

    if (state.validateCNPJContentState == ValidateCNPJContentState.SUCCESS) {
      helperText = "CNPJ válido";
      // coverage:ignore-start
      suffix = Icon(Icons.check, size: 24.sp, color: Colors.green);
      // coverage:ignore-end
    }

    return CustomTextFormField(
      labelText: "CPF ou CNPJ",
      inputFormat: InputFormatEnum.DOCUMENT,
      keyboardType: TextInputType.number,
      validator: (String? value) {
        if (value == null || value.isEmpty) return null;

        if (state.isCPF) {
          return ValidatorUtils.validateCPF(value);
        }

        return ValidatorUtils.validateCNPJ(value);
      },
      helperText: !state.isCPF ? helperText : null,
      suffix: !state.isCPF ? suffix : null,
      // coverage:ignore-start
      onChanged: (String? value) async {
        state.viewModel.numberRegistrationFederalRevenue = value;

        this._cubit.changeState(viewModel: state.viewModel);

        this._cubit.isCPF = true;

        if (value != null && value.length > 14) {
          this._cubit.isCPF = false;

          if (value.length == 18) {
            await this._cubit.validateCNJP(value);
          }
        }
      },
      // coverage:ignore-end
    );
  }
}
