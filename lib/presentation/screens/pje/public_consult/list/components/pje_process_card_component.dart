import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:tjcemobile/app/shared/utils/process_format_utils.dart';
import 'package:tjcemobile/data/model/pje_process_complete_model.dart';
import 'package:tjcemobile/presentation/resources/font_manager.dart';
import 'package:tjcemobile/presentation/resources/padding_manager.dart';
import 'package:tjcemobile/presentation/resources/routes_manager.dart';
import 'package:tjcemobile/presentation/widgets/custom_base_card.dart';

class PjeProcessCardComponent extends StatelessWidget {
  final PjeProcessCompleteDataModel _pjeProcessCompleteDataModel;
  final String? _route;
  final String? _oab;

  PjeProcessCardComponent({
    required PjeProcessCompleteDataModel pjeProcessCompleteDataModel,
    String? route,
    String? oab,
    Key? key,
  })  : this._pjeProcessCompleteDataModel = pjeProcessCompleteDataModel,
        this._route = route,
        this._oab = oab,
        super(key: key);

  @override
  Widget build(BuildContext context) {
    var basicData = this._pjeProcessCompleteDataModel.processo?.dadosBasicos;
    var pole = basicData?.polo;

    var activePole =
        pole?.firstWhereOrNull((item) => item.polo == "AT")?.parte?.firstOrNull;

    var passivePole =
        pole?.firstWhereOrNull((item) => item.polo == "PA")?.parte?.firstOrNull;

    var degree = this._pjeProcessCompleteDataModel.grau?.split("G").first;

    return CustomBaseCard(
      semanticsLabel: "Card no qual consta as breves informações do processo,"
          "como: Número do processo,"
          " grau do processo, Orgão julgador, Polo ativo e Polo passivo",
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: <Widget>[
              Text(
                semanticsLabel: "Processo",
                "Processo",
                style: TextStyle(
                  fontSize: FontSizeManager.s14,
                  color: context.theme.colorScheme.outline,
                ),
              ),
              Row(
                children: <Widget>[
                  SizedBox(width: 8.r),
                  ClipRRect(
                    borderRadius: BorderRadius.circular(34.r),
                    child: Container(
                      color: context.theme.colorScheme.secondaryContainer,
                      padding: PaddingManager.symmetricHorizontaSmall,
                      child: Text.rich(
                        TextSpan(
                          children: <TextSpan>[
                            TextSpan(
                              semanticsLabel: degree,
                              text: degree,
                              style: TextStyle(
                                fontWeight: FontWeightManager.black,
                              ),
                            ),
                            TextSpan(
                              text: "º",
                              semanticsLabel: "º",
                            ),
                            TextSpan(
                              text: " ",
                              semanticsLabel: " ",
                            ),
                            TextSpan(
                              text: "Grau",
                              semanticsLabel: "Grau",
                            ),
                          ],
                        ),
                        style: TextStyle(
                          fontSize: FontSizeManager.s12,
                          color: context.theme.colorScheme.onSecondaryContainer,
                          fontWeight: FontWeightManager.medium,
                          // fontWeight: FontWeightManager.medium,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
          Text(
            semanticsLabel: "${basicData?.numero?.value}",
            ProcessFormatUtils.format(basicData?.numero?.value ?? "") ?? "-",
            style: TextStyle(
              fontSize: FontSizeManager.s16,
              color: context.theme.colorScheme.onSurface,
              fontWeight: FontWeightManager.medium,
            ),
          ),
          if (basicData?.orgaoJulgador != null) ...[
            SizedBox(height: 8.r),
            Text(
              semanticsLabel: "Órgão Julgador",
              "Órgão Julgador",
              style: TextStyle(
                fontSize: FontSizeManager.s14,
                color: context.theme.colorScheme.outline,
              ),
            ),
            Text(
              semanticsLabel: basicData?.orgaoJulgador?.nomeOrgao,
              basicData?.orgaoJulgador?.nomeOrgao ?? "-",
              maxLines: 1,
              softWrap: true,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                fontSize: FontSizeManager.s16,
                color: context.theme.colorScheme.onSurface,
                fontWeight: FontWeightManager.medium,
              ),
            ),
          ],
          SizedBox(height: 8.r),
          if (activePole != null || passivePole != null) ...[
            const Divider(),
            SizedBox(height: 8.r),
            this._pole(context, part: activePole),
            SizedBox(height: 8.r),
            this._pole(context, part: passivePole, isActive: false),
          ],
        ],
      ),
      onTap: () async {
        var number = basicData?.numero;

        var processNumber = ProcessFormatUtils.format(number?.value ?? "");

        await Get.toNamed(
          Routes.pjePublicConsultDetailsRoute,
          arguments: {
            "processNumber": processNumber,
            "degree": this._pjeProcessCompleteDataModel.grau,
            "route": this._route,
            "oab": this._oab
          },
        );
      },
    );
  }

  Widget _pole(
    BuildContext context, {
    Parte? part,
    bool isActive = true,
  }) {
    if (part == null) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Text(
          semanticsLabel: "Polo ${isActive ? "Ativo" : "Passivo"}",
          "Polo ${isActive ? "Ativo" : "Passivo"}",
          style: TextStyle(
            fontSize: FontSizeManager.s14,
            color: context.theme.colorScheme.outline,
          ),
        ),
        Text(
          semanticsLabel: part.pessoa?.nome?.toUpperCase() ?? "-",
          part.pessoa?.nome?.toUpperCase() ?? "-",
          maxLines: 1,
          softWrap: true,
          overflow: TextOverflow.ellipsis,
          style: TextStyle(
            fontSize: FontSizeManager.s16,
            color: context.theme.colorScheme.onSurface,
            fontWeight: FontWeightManager.medium,
          ),
        ),
      ],
    );
  }
}
