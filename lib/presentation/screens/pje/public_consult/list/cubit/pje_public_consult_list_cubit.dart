import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';
import 'package:tjcemobile/app/shared/utils/process_format_utils.dart';
import 'package:tjcemobile/app/shared/utils/snackbar_utils.dart';
import 'package:tjcemobile/data/model/keycloak_authorization_model.dart';
import 'package:tjcemobile/data/model/pje_process_complete_model.dart';
import 'package:tjcemobile/data/network/failure.dart';
import 'package:tjcemobile/domain/usecase/pje/pje_public_consult_list_usecase.dart';
import 'package:tjcemobile/domain/viewmodel/pje_log_viewmodel.dart';
import 'package:tjcemobile/domain/viewmodel/pje_process_mni_viewmodel.dart';
import 'package:tjcemobile/presentation/resources/routes_manager.dart';

part 'pje_public_consult_list_state.dart';

class PjePublicConsultListCubit extends Cubit<PjePublicConsultListState> {
  final PjePublicConsultListUseCase _useCase;

  PjePublicConsultListCubit(this._useCase) : super(PjePublicConsultListState());

  List<PjeProcessCompleteDataModel>? _list;

  Future<void> execute(
    List<String> processNumberList, {
    PjeLoginViewModel? pjeLoginViewModel,
    PjeLogViewModel? pjeLogViewModel,
    String? oab,
    String? route,
  }) async {
    this._list = null;

    emit(state.copyWith(
      viewContentState: PjePublicConsultListContentState.LOADING,
    ));

    await Future.forEach(processNumberList, (String processNumber) async {
      var localLoginViewModel;

      if (processNumber.contains("/")) {
        var processDegree = processNumber.split("/");
        var degree = processDegree[1];

        localLoginViewModel = await this.getPjeLoginViewModel(degree);
      }

      await this._verifyProcessNumber(
        processNumber,
        pjeLoginViewModel: localLoginViewModel ?? pjeLoginViewModel,
        pjeLogViewModel: pjeLogViewModel,
      );
    });

    if (this._list == null) this._list = [];

    emit(state.copyWith(
      pjeProcessCompleteDataModelList: this._list,
      viewContentState: PjePublicConsultListContentState.SUCCESS,
    ));

    if (state.pjeProcessCompleteDataModelList?.length == 1) {
      var process = state.pjeProcessCompleteDataModelList?.single;
      var number = process?.processo?.dadosBasicos?.numero;

      var processNumber = ProcessFormatUtils.format(number?.value ?? "");

      if (pjeLoginViewModel != null) {
        await this.setPjeLoginViewModel(pjeLoginViewModel);

        Get.back();
      }

      await Get.toNamed(Routes.pjePublicConsultDetailsRoute, arguments: {
        "processNumber": processNumber,
        "degree": process?.grau,
        "route": route,
        "oab": pjeLogViewModel?.oab ?? oab,
      });
    }
  }

  Future<void> _verifyProcessNumber(
    String processNumber, {
    PjeLoginViewModel? pjeLoginViewModel,
    PjeLogViewModel? pjeLogViewModel,
  }) async {
    bool useBaseUri2G = false;

    if (processNumber.contains("/")) {
      List<String> processNumberList = processNumber.split("/");

      processNumber = processNumberList[0];

      if (processNumberList[1] == "PJE2G") {
        useBaseUri2G = true;
      }

      await this._postProcessMNISoap(processNumber,
          pjeLoginViewModel: pjeLoginViewModel,
          useBaseUri2G: useBaseUri2G,
          pjeLogViewModel: pjeLogViewModel);

      return;
    }

    do {
      await this._postProcessMNISoap(processNumber,
          pjeLoginViewModel: pjeLoginViewModel,
          useBaseUri2G: useBaseUri2G,
          pjeLogViewModel: pjeLogViewModel);

      useBaseUri2G = !useBaseUri2G;
    } while (useBaseUri2G);
  }

  Future<void> _postProcessMNISoap(
    String processNumber, {
    PjeLoginViewModel? pjeLoginViewModel,
    PjeLogViewModel? pjeLogViewModel,
    bool useBaseUri2G = false,
  }) async {
    await (await this._useCase.postProcessMNISoap(
              processNumber,
              pjeLoginViewModel: pjeLoginViewModel,
              useBaseUri2G: useBaseUri2G,
              includeHeader: true,
            ))
        .fold(
      (Failure failure) {
        emit(state.copyWith(
          viewContentState: PjePublicConsultListContentState.ERROR,
          failure: failure,
        ));

        if (state.pjeProcessCompleteDataModelList == null &&
            failure.message == "Erro ao realizar Login.") {
          SnackbarUtils.error(
              failure.title, failure.message ?? "Erro ao realizar login");
        }
      },
      (PjeProcessCompleteModel model) async {
        if (model.data != null) {
          model.data!.grau = useBaseUri2G ? "2G" : "1G";

          if (this._list == null) this._list = [];

          // var hasProcess = this._list!.any(
          //       (item) =>
          //           item.idProcesso == model.data!.idProcesso &&
          //           model.data?.grau == item.grau,
          //     );
          // if (!hasProcess) {
          // }
          this._list!.add(model.data!);
        }
      },
    );
  }

  Future<void> setPjeLoginViewModel(
    PjeLoginViewModel viewModel,
  ) async {
    (await this._useCase.setPjeLoginViewModel(viewModel)).fold(
      (Failure failure) => print(failure),
      (void _) => null,
    );
  }

  Future<PjeLoginViewModel?> getPjeLoginViewModel(String degree) async {
    return await (await this._useCase.getPjeLoginViewModel()).fold(
        (Failure failure) async {
      //
    }, (List<PjeLoginViewModel> list) async {
      var response =
          list.firstWhereOrNull((element) => element.degree == degree);
      return response;
    });
  }
}
