part of 'pje_public_consult_list_cubit.dart';

enum PjePublicConsultListContentState { DEFAULT, LOADING, SUCCESS, ERROR }

enum PjeLogContentState { DEFAULT, LOADING, SUCCESS, ERROR }

class PjePublicConsultListState extends Equatable {
  final PjePublicConsultListContentState viewContentState;
  final PjeLogContentState pjeLogContentState;
  final Failure? failure;
  final KeycloakAuthorizationModel? keycloakAuthorizationModel;
  final List<PjeProcessCompleteDataModel>? pjeProcessCompleteDataModelList;

  const PjePublicConsultListState({
    this.viewContentState = PjePublicConsultListContentState.DEFAULT,
    this.pjeLogContentState = PjeLogContentState.DEFAULT,
    this.failure,
    this.keycloakAuthorizationModel,
    this.pjeProcessCompleteDataModelList,
  });

  PjePublicConsultListState copyWith({
    PjePublicConsultListContentState? viewContentState,
    PjeLogContentState? pjeLogContentState,
    Failure? failure,
    KeycloakAuthorizationModel? keycloakAuthorizationModel,
    List<PjeProcessCompleteDataModel>? pjeProcessCompleteDataModelList,
  }) {
    return PjePublicConsultListState(
      viewContentState: viewContentState ?? this.viewContentState,
      pjeLogContentState: pjeLogContentState ?? this.pjeLogContentState,
      failure: failure ?? this.failure,
      keycloakAuthorizationModel:
          keycloakAuthorizationModel ?? this.keycloakAuthorizationModel,
      pjeProcessCompleteDataModelList: pjeProcessCompleteDataModelList ??
          this.pjeProcessCompleteDataModelList,
    );
  }

  List<Object?> get props => [
        viewContentState,
        pjeLogContentState,
        failure,
        keycloakAuthorizationModel,
        pjeProcessCompleteDataModelList,
      ];
}
