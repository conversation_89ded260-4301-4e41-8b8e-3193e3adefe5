import 'package:flutter/material.dart';
import 'package:tjcemobile/presentation/resources/color_manager.dart';

enum StatusProcessCode {
  ELABORATION,
  VERIFIED,
  DISTRIBUTED,
}

class StatusProcessCodeMessage {
  static const String ELABORATION = "Elaboração";
  static const String VERIFIED = "Verificado";
  static const String DISTRIBUTED = "Distribuído";
}

extension StatusProcessCodeExtension on StatusProcessCode {
  String get id {
    return switch (this) {
      StatusProcessCode.ELABORATION => "E",
      StatusProcessCode.VERIFIED => "V",
      StatusProcessCode.DISTRIBUTED => "D",
    };
  }

  String get name {
    return switch (this) {
      StatusProcessCode.ELABORATION => StatusProcessCodeMessage.ELABORATION,
      StatusProcessCode.VERIFIED => StatusProcessCodeMessage.VERIFIED,
      StatusProcessCode.DISTRIBUTED => StatusProcessCodeMessage.DISTRIBUTED,
    };
  }

  Color get color {
    return switch (this) {
      StatusProcessCode.ELABORATION => ColorManager.secondaryColor,
      StatusProcessCode.VERIFIED => ColorManager.judgedColor,
      StatusProcessCode.DISTRIBUTED => ColorManager.endedProcessColor,
    };
  }
}

class StatusProcessHandler {
  late StatusProcessCode methodCode;

  StatusProcessHandler.handle(String value) {
    methodCode = _handleMethodCode(value);
  }

  StatusProcessCode _handleMethodCode(String value) {
    return switch (value) {
      "E" => StatusProcessCode.ELABORATION,
      "V" => StatusProcessCode.VERIFIED,
      "D" => StatusProcessCode.DISTRIBUTED,
      _ => StatusProcessCode.ELABORATION
    };
  }
}
