import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:tjcemobile/presentation/resources/color_manager.dart';
import 'package:tjcemobile/presentation/resources/font_manager.dart';
import 'package:tjcemobile/presentation/resources/padding_manager.dart';
import 'package:tjcemobile/presentation/screens/pje/public_consult/list/components/pje_process_card_component.dart';
import 'package:tjcemobile/presentation/screens/pje/public_consult/list/cubit/pje_public_consult_list_cubit.dart';
import 'package:tjcemobile/presentation/widgets/arrow_back_component.dart';
import 'package:tjcemobile/presentation/widgets/custom_base_scaffold.dart';

class PjePublicConsultListView extends StatefulWidget {
  final List<String> _processNumberList;
  final String? _route;
  final String? _oab;

  const PjePublicConsultListView({
    required List<String> processNumberList,
    String? route,
    String? oab,
    Key? key,
  })  : this._processNumberList = processNumberList,
        this._route = route,
        this._oab = oab,
        super(key: key);

  @override
  State<PjePublicConsultListView> createState() =>
      _PjePublicConsultListViewState();
}

class _PjePublicConsultListViewState extends State<PjePublicConsultListView> {
  final PjePublicConsultListCubit _cubit =
      Get.find<PjePublicConsultListCubit>();

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await this._cubit.execute(
            this.widget._processNumberList,
            route: this.widget._route,
            oab: this.widget._oab,
          );
    });
  }

  @override
  void dispose() {
    Get.delete<PjePublicConsultListCubit>(force: true);

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return CustomBaseScaffold(
      appBar: this._buildAppBar(),
      body: this._buildBody(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text("Consulta pública"),
      leading: ArrowBackComponent(onPressed: () async => Get.back()
          // await Get.offNamed(Routes.homeRoute),
          ),
    );
  }

  Widget _buildBody() {
    return BlocBuilder<PjePublicConsultListCubit, PjePublicConsultListState>(
      bloc: Get.find<PjePublicConsultListCubit>(),
      builder: (BuildContext context, PjePublicConsultListState state) {
        var list = state.pjeProcessCompleteDataModelList;

        if (list == null) {
          return Center(
              child: CircularProgressIndicator(
            semanticsLabel: "carregando tela",
          ));
        }

        if (list.isEmpty) {
          return Container(
            width: 360.r,
            padding: PaddingManager.large,
            child: AlertDialog(
              title: Text(
                semanticsLabel: "Pop-up de atenção.",
                "Atenção!",
                style: TextStyle(
                  fontSize: FontSizeManager.s20,
                  color: ColorManager.darkColor,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              insetPadding: PaddingManager.symmetricHorizontalMedium,
              content: Text(
                "A presente consulta não retornará qualquer resultado em caso de informações prestadas incorretamente ou de processos sob segredo de justiça, conforme art. 1º, parágrafo único, da Resolução nº 121 do Conselho Nacional de Justiça.",
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: FontSizeManager.s14,
                  color: ColorManager.darkerColor,
                ),
              ),
              backgroundColor: ColorManager.whiteColor,
            ),
          );
        }

        if (list.isNotEmpty) {
          return Column(
            children: <Widget>[
              Center(
                child: Text(
                  semanticsLabel: "${list.length} processos encontrados",
                  "${list.length} processos encontrados",
                  style: TextStyle(
                    fontSize: FontSizeManager.s14,
                    fontWeight: FontWeight.bold,
                    color: ColorManager.whiteColor,
                  ),
                ),
              ),
              SizedBox(height: 32.r),
              Expanded(
                child: ListView.builder(
                  itemCount: list.length,
                  itemBuilder: (BuildContext context, int index) {
                    var item = list[index];

                    return PjeProcessCardComponent(
                      pjeProcessCompleteDataModel: item,
                      route: this.widget._route,
                      oab: this.widget._oab,
                    );
                  },
                ),
              ),
              SizedBox(height: 8.r),
            ],
          );
        }

        return const SizedBox.shrink();
      },
    );
  }
}
