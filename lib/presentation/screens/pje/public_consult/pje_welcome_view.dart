import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:tjcemobile/app/shared/utils/device_type_utils.dart';
import 'package:tjcemobile/presentation/resources/color_manager.dart';
import 'package:tjcemobile/presentation/resources/font_manager.dart';
import 'package:tjcemobile/presentation/resources/padding_manager.dart';
import 'package:tjcemobile/presentation/resources/routes_manager.dart';
import 'package:tjcemobile/presentation/widgets/arrow_back_component.dart';

class PjeWelcomeView extends StatefulWidget {
  PjeWelcomeView({super.key});

  @override
  State<PjeWelcomeView> createState() => _PjeWelcomeViewState();
}

class _PjeWelcomeViewState extends State<PjeWelcomeView> {
  @override
  Widget build(BuildContext context) {
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: SystemUiOverlayStyle(
        statusBarIconBrightness: Brightness.dark,
        statusBarColor: Colors.transparent,
        systemNavigationBarColor: ColorManager.primaryColor,
      ),
      child: Material(
        child: Scaffold(
          appBar: AppBar(toolbarHeight: 0),
          body: this._buildBody(),
        ),
      ),
    );
  }

  Widget _buildBody() {
    return Stack(
      children: <Widget>[
        SvgPicture.asset(
          "assets/pje.svg",
          fit: BoxFit.cover,
        ),
        Container(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              SizedBox(height: 36.r),
              ArrowBackComponent(
                onPressed: () async => Get.offNamed(Routes.homeRoute),
                color: ColorManager.outlineColor,
              ),
              Spacer(flex: 3),
              Container(
                padding: PaddingManager.symmetricHorizontalExtraLarge,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    SizedBox(
                      width: DeviceTypeUtils.current == DeviceTypeManager.TABLET
                          ? 460.r
                          : 260.r,
                      child: Text(
                        semanticsLabel: 'Solução de Conflitos\n' +
                            'Formulário para solicitação de uma audiência de conciliação/mediação em processos judiciais em trâmite no estado do Ceará',
                        "Processo Judicial Eletrônico - PJe",
                        style: TextStyle(
                          color: ColorManager.whiteColor,
                          fontSize: FontSizeManager.s28,
                          fontWeight: FontWeightManager.black,
                          height: 1.15,
                        ),
                      ),
                    ),
                    SizedBox(height: 4.r),
                    SizedBox(
                      width: 75.r,
                      child: Divider(
                        color: ColorManager.whiteColor,
                        thickness: 1.r,
                      ),
                    ),
                    if (context.height > 720.r) ...[
                      SizedBox(height: 16.r),
                    ] else ...[
                      SizedBox(height: 8.r),
                    ],
                    SizedBox(
                      width: DeviceTypeUtils.current == DeviceTypeManager.TABLET
                          ? 620.r
                          : context.width,
                      child: ExcludeSemantics(
                        child: Text.rich(
                          TextSpan(
                            children: <TextSpan>[
                              TextSpan(
                                  text: "Acompanhe o andamento de seu processo "
                                      "sempre que precisar. Acesse as"),
                              TextSpan(text: " "),
                              TextSpan(
                                text: "movimentações, decisões",
                                style: TextStyle(fontWeight: FontWeight.bold),
                              ),
                              TextSpan(text: " "),
                              TextSpan(text: "e"),
                              TextSpan(text: " "),
                              TextSpan(
                                text: "documentos",
                                style: TextStyle(fontWeight: FontWeight.bold),
                              ),
                              TextSpan(text: " "),
                              TextSpan(text: "diretamente pelo aplicativo."),
                            ],
                          ),
                          // textAlign: TextAlign.justify,
                          style: TextStyle(
                            fontSize: FontSizeManager.s14,
                            color: ColorManager.whiteColor,
                          ),
                        ),
                      ),
                    ),
                    if (context.height > 720.r) ...[
                      SizedBox(height: 16.r),
                    ] else ...[
                      SizedBox(height: 4.r),
                    ],
                    SizedBox(
                      width: DeviceTypeUtils.current == DeviceTypeManager.TABLET
                          ? 620.r
                          : context.width,
                      child: ExcludeSemantics(
                        child: Text.rich(
                          TextSpan(
                            children: <TextSpan>[
                              TextSpan(
                                text: "Saiba que estando logado como advogado "
                                    "você tem acesso a íntegra "
                                    "dos seus processos pela",
                              ),
                              TextSpan(text: " "),
                              TextSpan(
                                text: "Área do Advogado.",
                                style: TextStyle(fontWeight: FontWeight.bold),
                              ),
                            ],
                          ),
                          // textAlign: TextAlign.justify,
                          style: TextStyle(
                            fontSize: FontSizeManager.s14,
                            color: ColorManager.whiteColor,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Spacer(),
              Container(
                padding: PaddingManager.symmetricHorizontalLarge,
                width: DeviceTypeUtils.current == DeviceTypeManager.TABLET
                    ? 620.r
                    : context.width,
                child: ElevatedButton(
                  child: Text(
                    semanticsLabel:
                        'Quero Conciliar. Toque para iniciar a solicitação de conciliação',
                    "Acessar consulta processual",
                    style: TextStyle(
                      fontSize: FontSizeManager.s14,
                      fontWeight: FontWeight.w600,
                      color: ColorManager.whiteColor,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  style: ButtonStyle(
                    minimumSize:
                        WidgetStateProperty.all<Size>(Size.fromHeight(40.sp)),
                    shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                      RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(40.sp),
                      ),
                    ),
                    backgroundColor: WidgetStateProperty.all<Color>(
                      ColorManager.onboardingButtonColor,
                    ),
                  ),
                  onPressed: () async {
                    await Get.toNamed(Routes.pjePublicConsultFormRoute);
                  },
                ),
              ),
              SizedBox(height: 16.r)
            ],
          ),
        ),
      ],
    );
  }
}
