import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:tjcemobile/app/shared/enum/input_format_enum.dart';
import 'package:tjcemobile/app/shared/utils/obscure_cpf_utils.dart';
import 'package:tjcemobile/app/shared/utils/snackbar_utils.dart';
import 'package:tjcemobile/app/shared/utils/url_launcher_utils.dart';
import 'package:tjcemobile/app/shared/utils/validator_utils.dart';
import 'package:tjcemobile/domain/viewmodel/pje_log_viewmodel.dart';
import 'package:tjcemobile/domain/viewmodel/pje_process_mni_viewmodel.dart';
import 'package:tjcemobile/presentation/resources/color_manager.dart';
import 'package:tjcemobile/presentation/resources/font_manager.dart';
import 'package:tjcemobile/presentation/resources/icons_manager.dart';
import 'package:tjcemobile/presentation/resources/padding_manager.dart';
import 'package:tjcemobile/presentation/screens/pje/public_consult/list/cubit/pje_public_consult_list_cubit.dart';
import 'package:tjcemobile/presentation/widgets/custom_base_card.dart';
import 'package:tjcemobile/presentation/widgets/custom_text_form_field_secondary.dart';
import 'package:tjcemobile/presentation/widgets/primary_button.dart';

class PjeLoginBottomSheet extends StatefulWidget {
  final String _degree;
  final String _processNumber;
  final String? _cpf;
  final String? _oab;

  const PjeLoginBottomSheet({
    required String degree,
    required String processNumber,
    String? cpf,
    String? oab,
    Key? key,
  })  : this._degree = degree,
        this._processNumber = processNumber,
        this._cpf = cpf,
        this._oab = oab,
        super(key: key);

  @override
  _PjeLoginBottomSheetState createState() => _PjeLoginBottomSheetState();
}

class _PjeLoginBottomSheetState extends State<PjeLoginBottomSheet> {
  final _cubit = Get.find<PjePublicConsultListCubit>();

  final _formKey = GlobalKey<FormState>();

  final _cpfController = TextEditingController();
  final _passwordController = TextEditingController();
  final _obscureAccessCodeNotifier = ValueNotifier<bool>(true);

  @override
  void initState() {
    super.initState();
    _populateForm();
  }

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.only(
        topLeft: Radius.circular(32),
        topRight: Radius.circular(32),
      ),
      child: Container(
        padding: PaddingManager.large,
        color: Colors.white,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            this._buildHeader(),

            if (this.widget._cpf != null) ...[
              SizedBox(height: 16.r),
              this._buildTextForm(),
              this._buildLoginButton(),
              this._buildAuthenticationText(),
            ] else ...[
              SizedBox(height: 4.r),
              this._buildSubTitles(),
              this._buildRedirectButtons(),
              SizedBox(height: 16.r),
            ],
            // SizedBox(height: 8.r),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: <Widget>[
        if (this.widget._cpf != null) ...[
          SizedBox(width: 35.r),
        ] else ...[
          SizedBox(width: 55.r),
        ],
        Expanded(
          child: Text(
            this.widget._cpf != null
                ? 'Autenticar no PJe (${this.widget._degree})'
                : 'Acessar o PJe',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        IconButton(
          icon: Icon(
            Icons.close,
            color: ColorManager.secondaryColor,
          ),
          onPressed: () => Get.back(),
        ),
      ],
    );
  }

  Widget _buildTextForm() {
    return Form(
      key: this._formKey,
      child: Column(
        children: [
          CustomTextFormFieldSecondary(
            enabled: false,
            hintText: "CPF",
            labelStyle: TextStyle(color: context.theme.colorScheme.outline),
            controller: _cpfController,
            autoFocus: false,
            suffix: Icon(Icons.person_outline,
                color: context.theme.colorScheme.outline),
            keyboardType: TextInputType.number,
            inputAction: TextInputAction.next,
            inputFormat: InputFormatEnum.CPF,
            validator: (String? value) =>
                ValidatorUtils.validateString(value, fieldName: "CPF"),
          ),
          SizedBox(height: 16.r),
          ValueListenableBuilder(
            valueListenable: _obscureAccessCodeNotifier,
            builder: (context, bool obscureAccessCode, _) {
              return CustomTextFormFieldSecondary(
                hintText: "Senha",
                controller: _passwordController,
                obscureText: obscureAccessCode,
                keyboardType: TextInputType.text,
                inputAction: TextInputAction.done,
                validator: (String? value) =>
                    ValidatorUtils.validateString(value, fieldName: "Senha"),
                suffix: IconButton(
                  icon: obscureAccessCode
                      ? Icon(Icons.visibility_outlined,
                          color: context.theme.colorScheme.outline)
                      : Icon(Icons.visibility_off_outlined,
                          color: context.theme.colorScheme.outline),
                  tooltip:
                      obscureAccessCode ? 'Visualizar senha' : 'Ocultar senha',
                  onPressed: () =>
                      _obscureAccessCodeNotifier.value = !obscureAccessCode,
                ),
              );
            },
          ),
          SizedBox(height: 16.r),
        ],
      ),
    );
  }

  Widget _buildSubTitles() {
    return Container(
      padding: PaddingManager.large,
      child: Column(
        children: [
          _buildSubtitleText(
            "Você precisa da chave de acesso para visualizar a íntegra do processo.",
          ),
          SizedBox(height: 8.0),
          _buildSubtitleTextRich(),
        ],
      ),
    );
  }

  Widget _buildSubtitleText(String text) {
    return Text(
      text,
      textAlign: TextAlign.center,
      style: TextStyle(
          fontWeight: FontWeightManager.medium,
          fontSize: 14.sp,
          color: Colors.black54),
    );
  }

  Widget _buildSubtitleTextRich() {
    return Text.rich(
      TextSpan(
        children: <TextSpan>[
          const TextSpan(
            text: "Caso tenha a chave de acesso selecione ",
            style: TextStyle(
                fontWeight: FontWeightManager.medium, color: Colors.black54),
          ),
          const TextSpan(
            text: "Acessar o PJe no navegador",
            style: TextStyle(fontWeight: FontWeightManager.bold),
          ),
          const TextSpan(text: " "),
          const TextSpan(
            text: "caso contrário, selecione ",
            style: TextStyle(
                fontWeight: FontWeightManager.medium, color: Colors.black54),
          ),
          const TextSpan(
            text: "Solicitar chave de acesso",
            style: TextStyle(fontWeight: FontWeightManager.bold),
          ),
        ],
      ),
      style: TextStyle(
          color: ColorManager.darkColor, fontWeight: FontWeightManager.bold),
      textAlign: TextAlign.center,
    );
  }

  Widget _buildRedirectButtons() {
    return Column(
      children: [
        _buildSectionButton(
          "Acessar o PJe no navegador",
          IconsManager.pje,
          onTap: this._onAreaPjeTap,
        ),
        SizedBox(height: 8.r),
        _buildSectionButton(
          "Solicitar chave de acesso",
          Icons.print_outlined,
          onTap: this._onEmissaoSenhaTap,
        ),
      ],
    );
  }

  Widget _buildSectionButton(
    String title,
    IconData icon, {
    VoidCallback? onTap,
  }) {
    return CustomBaseCard(
      child: Container(
        width: 250.r,
        child: Row(
          children: [
            Padding(
              padding: const EdgeInsets.only(top: 6.5),
              child: Icon(
                icon,
                color: ColorManager.secondaryColor,
              ),
            ),
            SizedBox(width: 16.r),
            Text(
              title,
              style: TextStyle(
                fontSize: FontSizeManager.s14,
                fontWeight: FontWeight.bold,
                color: ColorManager.outlineColor,
              ),
            ),

            // CustomCard(iconData: icon, title: title),
          ],
        ),
      ),
      onTap: onTap,
    );
  }

  Future<void> _onAreaPjeTap() async {
    if (this.widget._degree.contains("1G")) {
      String pje1GLinkWeb =
          "https://pje.tjce.jus.br/pje1grau/Processo/ConsultaDocumento/listView.seam";

      await UrlLauncherUtils.launchUrl(pje1GLinkWeb);

      return;
    }

    String pje2GLinkWeb =
        "https://pje.tjce.jus.br/pje2grau/Processo/ConsultaDocumento/listView.seam";

    await UrlLauncherUtils.launchUrl(pje2GLinkWeb);
  }

  Future<void> _onEmissaoSenhaTap() async {
    if (Platform.isIOS) {
      String whatsAppIOS = "https://wa.me/+5585987322127";
      // coverage:ignore-start
      await UrlLauncherUtils.launchUrl(whatsAppIOS);
      // coverage:ignore-end
    } else {
      String whatsAppAndroid = "whatsapp://send?phone=+5585987322127";
      // coverage:ignore-start
      await UrlLauncherUtils.launchUrl(whatsAppAndroid);
      // coverage:ignore-end
    }
  }

  Widget _buildLoginButton() {
    return BlocBuilder<PjePublicConsultListCubit, PjePublicConsultListState>(
      bloc: Get.find<PjePublicConsultListCubit>(),
      builder: (BuildContext context, PjePublicConsultListState state) {
        if (state.viewContentState ==
            PjePublicConsultListContentState.LOADING) {
          return Center(
            child: CircularProgressIndicator(),
          );
        }

        return Container(
          width: 250.r,
          child: PrimaryButton(
            "Autenticar",
            onPressed: () async {
              if (!this._formKey.currentState!.validate()) {
                SnackbarUtils.error(
                  "Preencha todos os campos.",
                  "Informe a senha para continuar.",
                );

                return;
              }

              PjeLoginViewModel pjeLoginViewModel = PjeLoginViewModel(
                consultantId: this.widget._cpf,
                consultantPassword: this._passwordController.text,
                degree: this.widget._degree,
              );

              PjeLogViewModel pjeLogViewModel = PjeLogViewModel(
                cpf: this.widget._cpf,
                oab: this.widget._oab,
                processo: this.widget._processNumber,
              );

              var list = [
                "${this.widget._processNumber}/${this.widget._degree}",
              ];

              await this._cubit.execute(
                    list,
                    pjeLoginViewModel: pjeLoginViewModel,
                    route: Get.currentRoute,
                    pjeLogViewModel: pjeLogViewModel,
                  );
            },
          ),
        );
      },
    );
  }

  Widget _buildAuthenticationText() {
    return Container(
      padding: PaddingManager.large,
      child: Text(
        "Para ter acesso aos autos do processo é necessário a sua autenticação no PJe",
        textAlign: TextAlign.center,
        style: TextStyle(
          fontWeight: FontWeightManager.medium,
          fontSize: 14.sp,
          color: Colors.black54,
        ),
      ),
    );
  }

  void _populateForm() {
    this._cpfController.value = TextEditingValue(
      text: ObscureCPFUtils.cpf(this.widget._cpf ?? ""),
    );
  }
}
