import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:share_plus/share_plus.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';
import 'package:tjcemobile/presentation/widgets/arrow_back_component.dart';
import 'package:tjcemobile/presentation/widgets/custom_base_card.dart';
import 'package:tjcemobile/presentation/widgets/custom_base_scaffold.dart';
import 'package:tjcemobile/presentation/widgets/secondary_button.dart';

class PjePDFComponent extends StatefulWidget {
  final String _title;
  final String _path;
  final String _documentId;
  final String _degree;

  PjePDFComponent({
    required String title,
    required String path,
    required String documentId,
    required String degree,
    Key? key,
  })  : this._title = title,
        this._path = path,
        this._documentId = documentId,
        this._degree = degree,
        super(key: key);

  @override
  State<PjePDFComponent> createState() => _PjePDFComponentState();
}

class _PjePDFComponentState extends State<PjePDFComponent> {
  @override
  Widget build(BuildContext context) {
    return CustomBaseScaffold(
      appBar: this._buildAppBar(),
      body: this._buildBody(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    String fileName = this.widget._title;

    return AppBar(
      leading: ArrowBackComponent(onPressed: () => Get.back()),
      title: Text(
        fileName,
        semanticsLabel: fileName,
      ),
      actions: <Widget>[
        IconButton(
          icon: Icon(
            semanticLabel: "Ao clicar no ícone voce compartilha o seu PDF",
            Icons.share,
            size: 24.sp,
          ),
          onPressed: () async {
            var xFile = XFile(this.widget._path);

            await Share.shareXFiles([xFile]);
          },
        ),
      ],
    );
  }

  Widget _buildBody() {
    return Column(
      children: <Widget>[
        SizedBox(height: 16.r),
        //Caso volte a parte de autenticar o documento

        // Container(
        //   width: DeviceTypeUtils.current == DeviceTypeManager.TABLET
        //       ? 460.r
        //       : 320.r,
        //   child: Text.rich(
        //     TextSpan(
        //       children: <TextSpan>[
        //         TextSpan(
        //           text:
        //               "Consulte a autenticidade do documento utilizando o identificador",
        //         ),
        //         TextSpan(text: " "),
        //         TextSpan(
        //           text: this.widget._documentId,
        //           style: TextStyle(fontWeight: FontWeightManager.black),
        //         ),
        //         TextSpan(text: " "),
        //         TextSpan(text: "diretamente no PJe."),
        //       ],
        //       style: TextStyle(
        //         fontSize: FontSizeManager.s12,
        //         fontWeight: FontWeightManager.medium,
        //         color: ColorManager.whiteColor,
        //       ),
        //     ),
        //     textAlign: TextAlign.center,
        //   ),
        // ),
        // SizedBox(height: 4.r),
        // Container(
        //   width: DeviceTypeUtils.current == DeviceTypeManager.TABLET
        //       ? 460.r
        //       : 320.r,
        //   child: InkWell(
        //     child: Row(
        //       mainAxisAlignment: MainAxisAlignment.center,
        //       mainAxisSize: MainAxisSize.min,
        //       children: <Widget>[
        //         Flexible(
        //           child: Text(
        //             "Copiar identificador",
        //             style: TextStyle(
        //               fontSize: FontSizeManager.s12,
        //               fontWeight: FontWeightManager.medium,
        //               color: ColorManager.secondaryColor,
        //             ),
        //           ),
        //         ),
        //         SizedBox(width: 8.r),
        //         Icon(
        //           Icons.copy,
        //           size: 14.sp,
        //           color: ColorManager.secondaryColor,
        //         ),
        //       ],
        //     ),
        //     onTap: () async {
        //       await Clipboard.setData(
        //         ClipboardData(text: this.widget._documentId),
        //       );

        //       SnackbarUtils.success(
        //         "Identificador copiado",
        //         "O identificador foi copiado para a área de transferência",
        //       );
        //     },
        //   ),
        // ),
        // SizedBox(height: 8.r),
        Expanded(
          child: CustomBaseCard(
            child: SfPdfViewer.file(
              File(this.widget._path),
              canShowPaginationDialog: false,
            ),
          ),
        ),
        // Botão de validação

        // SizedBox(height: 8.r),
        // PrimaryButton(
        //   "Verificar autenticidade no PJe",
        //   // coverage:ignore-start
        //   onPressed: () async {
        //     var flavorValues = FlavorConfig.instance.values;

        //     var baseUrl = flavorValues.pjeMni1GBaseUrl;

        //     if (this.widget._degree == "2G") {
        //       baseUrl = flavorValues.pjeMni2GBaseUrl;
        //     }

        //     var url = baseUrl + "/Processo/ConsultaDocumento/listView.seam";

        //     await UrlLauncherUtils.launchUrl(url);
        //   },
        //   // coverage:ignore-end
        // ),
        SizedBox(height: 8.r),
        SecondaryButton(
          "Voltar",
          // coverage:ignore-start
          onPressed: () => Get.back(),
          // coverage:ignore-end
        ),
        SizedBox(height: 8.r),
      ],
    );
  }
}
