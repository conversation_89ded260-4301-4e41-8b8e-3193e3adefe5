import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:tjcemobile/app/shared/utils/format_phone_utils.dart';
import 'package:tjcemobile/app/shared/utils/url_launcher_utils.dart';
import 'package:tjcemobile/presentation/resources/color_manager.dart';
import 'package:tjcemobile/presentation/resources/font_manager.dart';
import 'package:tjcemobile/presentation/resources/padding_manager.dart';
import 'package:tjcemobile/presentation/widgets/custom_base_card.dart';

class PjeDialogExceptionComponent extends StatelessWidget {
  const PjeDialogExceptionComponent({super.key});

  @override
  Widget build(BuildContext context) {
    return Semantics(
      label:
          'Aviso importante! Os dados não estão disponíveis no momento. Para mais informações, entre em contato com a equipe do PJE.',
      child: AlertDialog(
        title: ExcludeSemantics(
          child: SingleChildScrollView(
            child: Column(
              children: [
                Container(
                  padding: PaddingManager.large,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: <Widget>[
                      Icon(
                        Icons.info_outline,
                        size: 24.sp,
                        color: ColorManager.secondaryColor,
                      ),
                      SizedBox(height: 8.r),
                      Text(
                        "Visualização indisponível:",
                        textAlign: TextAlign.center,
                      ),
                      SizedBox(height: 8.r),
                      Text.rich(
                        TextSpan(
                          children: <TextSpan>[
                            TextSpan(
                                text:
                                    "Os dados não estão disponíveis no momento. Para mais informações, entre em contato com a equipe do"),
                            TextSpan(text: " "),
                            TextSpan(
                              text: "PJe.",
                              style: TextStyle(fontWeight: FontWeight.bold),
                            ),
                          ],
                        ),
                        textAlign: TextAlign.center,
                        style: TextStyle(
                            color: ColorManager.darkColor, fontSize: 14.sp),
                      ),
                    ],
                  ),
                ),
                // Botões superiores (2 em linha)
                Padding(
                  padding: EdgeInsets.symmetric(
                      horizontal: 24.r), // Espaço das bordas
                  child: Row(
                    mainAxisAlignment:
                        MainAxisAlignment.spaceEvenly, // Espaçamento igual
                    children: [
                      _buildSectionButton(
                        "Telefone",
                        Icons.phone_outlined,
                        onTap: () async {
                          await UrlLauncherUtils.launchUrl(
                            'tel:+55(85) 3366-2966',
                          );
                        },
                      ),
                      _buildSectionButton(
                        "E-mail",
                        Icons.email_outlined,
                        onTap: () async {
                          await UrlLauncherUtils.launchUrl(
                            'mailto:"<EMAIL>"',
                          );
                        },
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 16.r), // Espaço entre as linhas de botões
                // Botão inferior (centralizado)
                Center(
                  child: _buildSectionButton(
                    "Balcão virtual",
                    Icons.computer_outlined,
                    onTap: () async {
                      await UrlLauncherUtils.launchUrl(
                        "https://tjce-teams-apps-bv.azurefd.net/meeting/SuportePJe",
                      );
                    },
                  ),
                ),
                SizedBox(height: 16.r),
              ],
            ),
          ),
        ),
        actions: <Widget>[
          TextButton(
            child: Semantics(
              label: 'Botão de confimação',
              hint:
                  'Clique para prosseguir com a visualização dos seus processos',
              child: Text(
                "OK",
                style: TextStyle(fontSize: FontSizeManager.s14),
              ),
            ),
            onPressed: () => Get.back(),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionButton(
    String title,
    IconData icon, {
    VoidCallback? onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 60.r,
            height: 60.r,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black12,
                  blurRadius: 4,
                  offset: Offset(0, 2),
                ),
              ],
            ),
            child: Icon(
              icon,
              color: ColorManager.secondaryColor,
              size: 24.r,
            ),
          ),
          SizedBox(height: 8.r),
          Text(
            title,
            style: TextStyle(
              fontSize: FontSizeManager.s14,
              fontWeight: FontWeight.bold,
              color: ColorManager.outlineColor,
            ),
          ),
        ],
      ),
    );
  }
}
