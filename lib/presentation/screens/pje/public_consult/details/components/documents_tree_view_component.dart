import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:tjcemobile/data/model/pje_process_complete_model.dart';
import 'package:tjcemobile/presentation/resources/color_manager.dart';
import 'package:tjcemobile/presentation/resources/font_manager.dart';
import 'package:tjcemobile/presentation/resources/padding_manager.dart';
import 'package:tjcemobile/presentation/screens/pje/public_consult/details/cubit/pje_public_consult_details_cubit.dart';
import 'package:tjcemobile/presentation/widgets/custom_base_card.dart';

class DocumentsTreeViewComponent extends StatefulWidget {
  final List<DocumentoDataModel> _documentsList;
  final List<Movimento>? _movementsList;

  const DocumentsTreeViewComponent({
    required List<DocumentoDataModel> documentsList,
    List<Movimento>? movementsList,
    Key? key,
  })  : this._documentsList = documentsList,
        this._movementsList = movementsList,
        super(key: key);

  @override
  State<DocumentsTreeViewComponent> createState() =>
      _DocumentsTreeViewComponentState();
}

class _DocumentsTreeViewComponentState
    extends State<DocumentsTreeViewComponent> {
  final PjePublicConsultDetailsCubit _cubit =
      Get.find<PjePublicConsultDetailsCubit>();

  @override
  Widget build(BuildContext context) {
    return Scrollbar(
      child: Container(
        padding: PaddingManager.medium,
        child: ListView.separated(
          itemCount: widget._documentsList.length,
          separatorBuilder: (context, index) => SizedBox(height: 8.0),
          itemBuilder: (context, index) {
            final item = widget._documentsList[index];
            final movement = widget._movementsList
                ?.where((movement) =>
                    movement.identificadorMovimento == item.movimento)
                .firstOrNull;

            final date = _formatDate(item.dataHora?.value);
            final documentsLinked = item.documentoVinculado;

            if (documentsLinked != null && documentsLinked.isNotEmpty) {
              return _buildLinkedDocumentCard(
                item,
                movement,
                date,
                documentsLinked,
              );
            }

            return _buildDocumentCard(item, date);
          },
        ),
      ),
    );
  }

  Widget _buildLinkedDocumentCard(
    DocumentoDataModel item,
    Movimento? movement,
    String date,
    List<DocumentoVinculado> documentsLinked,
  ) {
    return CustomBaseCard(
      child: Container(
        padding: PaddingManager.medium,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(date),
            if (movement?.identificadorMovimento == item.movimento) ...[
              _buildMovementRow(movement),
            ],
            SizedBox(height: 8.r),
            _buildDocumentRow(item, isLinked: false),
            _buildLinkedDocumentsList(documentsLinked, item),
          ],
        ),
      ),
    );
  }

  Widget _buildDocumentCard(DocumentoDataModel item, String date) {
    return CustomBaseCard(
      child: Container(
        padding: PaddingManager.medium,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(date),
            SizedBox(height: 4.r),
            _buildDocumentRow(item),
          ],
        ),
      ),
    );
  }

  Widget _buildMovementRow(Movimento? movement) {
    return Row(
      children: [
        Icon(
          Icons.campaign_outlined,
          color: ColorManager.outlineColor,
          size: FontSizeManager.s18,
        ),
        SizedBox(width: 4.r),
        Expanded(
          child: Text(
            movement?.movimentoNacional?.complemento?.firstOrNull?.value
                    ?.toUpperCase() ??
                "",
            style: TextStyle(
              fontSize: FontSizeManager.s12,
              fontWeight: FontWeightManager.bold,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDocumentRow(DocumentoDataModel item, {bool isLinked = true}) {
    return Row(
      children: [
        if (!item.loadContent) ...[
          Icon(
            _getTypeDocumentIcon(item.mimetype),
            color: ColorManager.outlineColor,
            size: FontSizeManager.s18,
          ),
        ] else ...[
          Container(
            padding: PaddingManager.medium,
            height: 38.r,
            width: 38.r,
            child: CircularProgressIndicator(),
          ),
        ],
        SizedBox(width: 4.r),
        Expanded(
          child: InkWell(
            overlayColor: WidgetStateProperty.all<Color>(Colors.transparent),
            child: Text.rich(
              TextSpan(
                children: [
                  TextSpan(text: item.idDocumento),
                  TextSpan(text: " - "),
                  TextSpan(
                    text: item.descricao?.toUpperCase() ?? '-',
                    style: TextStyle(
                      fontSize: FontSizeManager.s12,
                    ),
                  ),
                ],
              ),
              style: TextStyle(
                fontSize: FontSizeManager.s14,
                color: ColorManager.primaryColor,
              ),
            ),
            onTap: () async {
              await this._cubit.getDocumentFromProcessesSoap(item);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildLinkedDocumentsList(
    List<DocumentoVinculado> documentsLinked,
    DocumentoDataModel item,
  ) {
    return Container(
      margin: const EdgeInsets.only(left: 10.0),
      child: Stack(
        children: [
          SizedBox(
            height: (50.0 * documentsLinked.length) - 25.0,
            child: VerticalDivider(
              thickness: 1.25,
              color: ColorManager.outlineColor,
            ),
          ),
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: documentsLinked.length,
            itemBuilder: (context, index) {
              final linked = documentsLinked[index];

              return _buildLinkedDocumentRow(
                linked,
                numberProcess: item.numeroProcesso ?? '',
                degree: item.grau ?? '',
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildLinkedDocumentRow(
    DocumentoVinculado item, {
    required String numberProcess,
    required String degree,
  }) {
    return Row(
      children: [
        SizedBox(
          height: 50.0,
          child: VerticalDivider(
            thickness: 1.25,
            color: ColorManager.outlineColor,
            indent: 0.0,
            endIndent: 25.0,
          ),
        ),
        SizedBox(
          width: 25.0,
          child: Divider(
            thickness: 1.25,
            color: ColorManager.outlineColor,
          ),
        ),
        Expanded(
          child: Row(
            children: [
              const SizedBox(width: 4.0),
              if (!item.loadContent) ...[
                Icon(
                  _getTypeDocumentIcon(item.mimetype),
                  color: ColorManager.outlineColor,
                  size: FontSizeManager.s14,
                )
              ] else ...[
                Container(
                  padding: PaddingManager.medium,
                  height: 38.r,
                  width: 38.r,
                  child: CircularProgressIndicator(),
                ),
              ],
              const SizedBox(width: 4.0),
              Expanded(
                child: InkWell(
                  overlayColor:
                      WidgetStateProperty.all<Color>(Colors.transparent),
                  child: Text.rich(
                    overflow: TextOverflow.ellipsis,
                    softWrap: false,
                    maxLines: 2,
                    TextSpan(
                      children: [
                        TextSpan(text: item.idDocumento),
                        TextSpan(text: " - "),
                        TextSpan(
                          text: item.descricao?.toUpperCase() ?? '-',
                          style: TextStyle(
                            color: ColorManager.primaryColor,
                            fontSize: FontSizeManager.s12,
                          ),
                        ),
                      ],
                    ),
                    style: TextStyle(
                      fontSize: FontSizeManager.s14,
                      color: ColorManager.primaryColor,
                    ),
                  ),
                  onTap: () async {
                    var document = item.fromDocumentoDataModel;

                    document.numeroProcesso = numberProcess;
                    document.grau = degree;

                    await this._cubit.getDocumentFromProcessesSoap(document);
                  },
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  String _formatDate(String? dateString) {
    if (dateString == null) return "-";

    final dateTime = DateTime.parse(
      dateString.substring(0, 8) + 'T' + dateString.substring(8),
    );
    return DateFormat('dd/MM/yyyy HH:mm').format(dateTime);
  }

  IconData _getTypeDocumentIcon(String? typeDocument) {
    return switch (typeDocument) {
      "text/html" => Icons.picture_as_pdf,
      "application/pdf" => Icons.picture_as_pdf,
      "video/mp4" => Icons.video_camera_back_rounded,
      _ => Icons.insert_drive_file_rounded
    };
  }
}
