import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:tjcemobile/app/shared/utils/process_format_utils.dart';
import 'package:tjcemobile/data/model/pje_process_complete_model.dart';
import 'package:tjcemobile/presentation/resources/color_manager.dart';
import 'package:tjcemobile/presentation/resources/font_manager.dart';
import 'package:tjcemobile/presentation/resources/padding_manager.dart';
import 'package:tjcemobile/presentation/resources/routes_manager.dart';
import 'package:tjcemobile/presentation/screens/pje/public_consult/details/components/documents_tree_view_component.dart';
import 'package:tjcemobile/presentation/screens/pje/public_consult/details/components/pje_login_component.dart';
import 'package:tjcemobile/presentation/screens/pje/public_consult/details/components/pje_secret_dialog_component.dart';
import 'package:tjcemobile/presentation/screens/pje/public_consult/details/cubit/pje_public_consult_details_cubit.dart';
import 'package:tjcemobile/presentation/widgets/arrow_back_component.dart';
import 'package:tjcemobile/presentation/widgets/custom_base_card.dart';
import 'package:tjcemobile/presentation/widgets/custom_base_scaffold.dart';
import 'package:tjcemobile/presentation/widgets/not_content_found_component.dart';
import 'package:tjcemobile/presentation/widgets/primary_button.dart';

class PjePublicConsultDetailsView extends StatefulWidget {
  final String _processNumber;
  final String _degree;
  final String? _route;
  final String? _oab;

  const PjePublicConsultDetailsView({
    required String processNumber,
    required String degree,
    String? route,
    String? oab,
    Key? key,
  })  : this._processNumber = processNumber,
        this._degree = degree,
        this._route = route,
        this._oab = oab,
        super(key: key);

  @override
  State<PjePublicConsultDetailsView> createState() =>
      _PjePublicConsultDetailsViewState();
}

class _PjePublicConsultDetailsViewState
    extends State<PjePublicConsultDetailsView> with TickerProviderStateMixin {
  late TabController _tabController;

  final PjePublicConsultDetailsCubit _cubit =
      Get.find<PjePublicConsultDetailsCubit>();

  Map<String, dynamic>? mapWithDictionaryMovement;

  @override
  void initState() {
    super.initState();

    this._tabController = TabController(length: 3, vsync: this);

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await this._cubit.verifyLoginPJe(this.widget._route, this.widget._degree);

      await this._cubit.getProcessWithoutDocuments(
            this.widget._oab,
            this.widget._processNumber,
            this.widget._degree,
            isFirstAccess: true,
          );

      mapWithDictionaryMovement = await this._cubit.getLocalJsonMovementID();
    });
  }

  @override
  void dispose() {
    this._tabController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return CustomBaseScaffold(
      appBar: this._buildAppBar(context),
      appBarBottom: this._buildAppBarBottom(),
      body: this._buildBody(context),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return AppBar(
      title: Text(
        semanticsLabel: "Detalhes do processo\n Aba selecionada por padrão:"
            "Detalhes do processo\n"
            " as abas disponíveis: movimentações para detalhar as movimentações do processo\n"
            "ou documentos para que possa baixar os documentos do processo.",
        "Processo",
      ),
      leading: ArrowBackComponent(onPressed: () => Get.back()),
      actions: <Widget>[
        BlocBuilder<PjePublicConsultDetailsCubit, PjePublicConsultDetailsState>(
          bloc: Get.find<PjePublicConsultDetailsCubit>(),
          builder: (BuildContext context, PjePublicConsultDetailsState state) {
            var reportState = state.pjeReportContentState;

            if (reportState != PjeReportContentState.LOADING) {
              return IconButton(
                icon: Icon(
                  semanticLabel:
                      "Botão que gera um relatório do seu processo em PDF",
                  Icons.picture_as_pdf_rounded,
                  size: 24.sp,
                ),
                onPressed: () async {
                  var pjeSecret = state.pjeProcessCompleteModel?.data?.processo
                          ?.dadosBasicos?.nivelSigilo !=
                      "0";
                  if (pjeSecret) {
                    return await Get.dialog(PjeDialogExceptionComponent());
                  }
                  await this._cubit.getReportProcessFromPJe(
                        this.widget._processNumber,
                        this.widget._degree,
                      );
                },
              );
            }

            return Container(
              padding: PaddingManager.medium,
              child: CircularProgressIndicator(
                semanticsLabel: "Carregando informações",
                semanticsValue: "Carregando",
                color: context.theme.secondaryHeaderColor,
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildAppBarBottom() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        SizedBox(height: 20.r),
        BlocBuilder<PjePublicConsultDetailsCubit, PjePublicConsultDetailsState>(
          bloc: Get.find<PjePublicConsultDetailsCubit>(),
          builder: (BuildContext context, PjePublicConsultDetailsState state) {
            return SizedBox(
              width: double.infinity,
              child: SegmentedButton<OptionsDetailsContentState>(
                showSelectedIcon: false,
                style: ButtonStyle(
                  padding: WidgetStateProperty.all(EdgeInsets.zero),
                ),
                segments: <ButtonSegment<OptionsDetailsContentState>>[
                  ButtonSegment<OptionsDetailsContentState>(
                    value: OptionsDetailsContentState.DETAILS,
                    label: Text(
                      semanticsLabel: "Detalhes",
                      "Detalhes",
                      style: TextStyle(fontSize: FontSizeManager.s12),
                    ),
                  ),
                  ButtonSegment<OptionsDetailsContentState>(
                    value: OptionsDetailsContentState.MOVEMENTS,
                    label: Text(
                      semanticsLabel: "Movimentações",
                      "Movimentações",
                      style: TextStyle(fontSize: FontSizeManager.s12),
                      overflow: TextOverflow.visible,
                    ),
                  ),
                  ButtonSegment<OptionsDetailsContentState>(
                    value: OptionsDetailsContentState.DOCUMENTS,
                    label: Text(
                      semanticsLabel: "Documentos",
                      "Documentos",
                      style: TextStyle(fontSize: FontSizeManager.s12),
                    ),
                  ),
                ],
                selected: <OptionsDetailsContentState>{
                  state.optionsDetailsContentState
                },
                onSelectionChanged:
                    (Set<OptionsDetailsContentState> newSelection) async {
                  var option = newSelection.first;
                  this._cubit.setSegmentedSelected(option);

                  await this._cubit.verifyLoginPJe(
                        this.widget._route,
                        this.widget._degree,
                      );

                  await this._cubit.getProcessWithoutDocuments(
                        this.widget._oab,
                        this.widget._processNumber,
                        this.widget._degree,
                      );
                },
              ),
            );
          },
        ),
        SizedBox(height: 20.r),
      ],
    );
  }

  Widget _buildBody(BuildContext context) {
    return BlocBuilder<PjePublicConsultDetailsCubit,
        PjePublicConsultDetailsState>(
      bloc: Get.find<PjePublicConsultDetailsCubit>(),
      builder: (BuildContext context, PjePublicConsultDetailsState state) {
        if (state.pjeDocumentsContentState ==
            PjeDocumentsContentState.LOADING) {
          return Center(
            child: CircularProgressIndicator(
              color: context.theme.primaryColor,
              semanticsLabel: "Carregando informações",
              semanticsValue: "Carregando",
            ),
          );
        }

        if (state.optionsDetailsContentState ==
                OptionsDetailsContentState.DOCUMENTS &&
            state.pjeLoginViewModel != null) {
          return DocumentsTreeViewComponent(
            documentsList: state.documentsList ?? [],
            movementsList:
                state.pjeProcessCompleteModel?.data?.processo?.movimento,
          );
        }

        return Column(
          children: <Widget>[
            Expanded(
              child: this._buildContent(context, state),
            ),
            SizedBox(height: 8.r),
            if (state.optionsDetailsContentState ==
                    OptionsDetailsContentState.DOCUMENTS &&
                state.pjeLoginViewModel == null) ...[
              PrimaryButton(
                "Visualizar o processo na integra",
                onPressed: () async {
                  await Get.bottomSheet(
                    PjeLoginBottomSheet(
                      processNumber: this.widget._processNumber,
                      degree: this.widget._degree,
                      oab: this.widget._oab,
                    ),
                    isScrollControlled: true,
                  );
                },
              ),
              SizedBox(height: 16.r),
            ]
          ],
        );
      },
    );
  }

  Widget _buildContent(
    BuildContext context,
    PjePublicConsultDetailsState state,
  ) {
    return CustomBaseCard(
      semanticsLabel:
          "Detalhes do processo. Tem a aba: Detalhes do processo, movimentações e Documentos",
      padding: PaddingManager.medium,
      child: Padding(
        padding: PaddingManager.medium,
        child: SizedBox(
          height: context.height,
          width: context.width,
          child: Column(
            children: <Widget>[
              if (state.optionsDetailsContentState ==
                  OptionsDetailsContentState.DETAILS) ...[
                Semantics(
                  label:
                      "Aba detalhes selecionada, esta aba exibe os detalhes do processo",
                ),
                Expanded(child: this._detailsList())
              ],
              if (state.optionsDetailsContentState ==
                  OptionsDetailsContentState.MOVEMENTS) ...[
                Semantics(
                  label: "Aba Movimentações, nela tem um card cheio de"
                      " movimentações do processo",
                ),
                Expanded(child: this._movementList()),
              ],
              if (state.optionsDetailsContentState ==
                  OptionsDetailsContentState.DOCUMENTS) ...[
                Semantics(
                  label:
                      "Aba documentos, você pode abrir os seus documentos aqui",
                ),
                Expanded(child: this._documentsList())
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _detailsList() {
    return BlocBuilder<PjePublicConsultDetailsCubit,
        PjePublicConsultDetailsState>(
      bloc: Get.find<PjePublicConsultDetailsCubit>(),
      builder: (BuildContext context, PjePublicConsultDetailsState state) {
        if (state.pjeProcessCompleteContentState ==
            PjeProcessCompleteContentState.LOADING) {
          return Center(
            child: CircularProgressIndicator(
              color: context.theme.primaryColor,
              semanticsLabel: "Carregando informações",
              semanticsValue: "Carregando",
            ),
          );
        }

        if (state.pjeProcessCompleteContentState ==
            PjeProcessCompleteContentState.SUCCESS) {
          var item = state.pjeProcessCompleteModel?.data?.processo;

          String subjects = item?.dadosBasicos?.assunto
                  ?.map((e) => e.codigoNacionalDescricao)
                  .where((desc) => desc?.isNotEmpty ?? false)
                  .join(", ") ??
              "-";

          return SingleChildScrollView(
            child: Column(
              children: <Widget>[
                this._buildSection(
                  title: 'Processo',
                  content: ProcessFormatUtils.format(
                          item?.dadosBasicos?.numero?.value ?? "") ??
                      "-",
                  showDegree: true,
                ),
                SizedBox(height: 12.r),
                Divider(
                  color: context.theme.colorScheme.outlineVariant,
                ),
                SizedBox(height: 12.r),
                this._buildSection(
                  title: 'Localização',
                  content: item?.dadosBasicos?.orgaoJulgador?.nomeOrgao ?? "-",
                ),
                SizedBox(height: 12.r),
                Divider(
                  color: context.theme.colorScheme.outlineVariant,
                ),
                SizedBox(height: 12.r),
                this._buildSection(
                  title: 'Classe',
                  content: item?.dadosBasicos?.classeProcessualDescricao ?? "-",
                ),
                SizedBox(height: 12.r),
                Divider(
                  color: context.theme.colorScheme.outlineVariant,
                ),
                SizedBox(height: 12.r),
                this._buildSection(title: 'Assunto', content: subjects),
                SizedBox(height: 12.r),
                Divider(
                  color: context.theme.colorScheme.outlineVariant,
                ),
                SizedBox(height: 12.r),
                this._buildSection(
                  title: 'Competência',
                  content: item?.dadosBasicos?.competenciaDescricao ?? "-",
                ),
                SizedBox(height: 12.r),
                Divider(
                  color: context.theme.colorScheme.outlineVariant,
                ),
                SizedBox(height: 12.r),
                this._buildSection(
                  title: 'Jurisdição',
                  content: item?.dadosBasicos?.codigoLocalidadeDescricao ?? "-",
                ),
                SizedBox(height: 12.r),
                Divider(
                  color: context.theme.colorScheme.outlineVariant,
                ),
                SizedBox(height: 12.r),
                this._pole(context, poles: item?.dadosBasicos?.polo),
              ],
            ),
          );
        }

        return Container(
          height: context.height,
          width: context.width,
          child: NotContentFoundComponent(
            typeMessage: "Não há detalhes para este processo",
            isPersonalizedMessage: true,
          ),
        );
      },
    );
  }

  Widget _movementList() {
    return BlocBuilder<PjePublicConsultDetailsCubit,
        PjePublicConsultDetailsState>(
      bloc: Get.find<PjePublicConsultDetailsCubit>(),
      builder: (BuildContext context, PjePublicConsultDetailsState state) {
        if (state.pjeProcessCompleteContentState ==
            PjeProcessCompleteContentState.LOADING) {
          return Center(
            child: CircularProgressIndicator(
              color: context.theme.primaryColor,
              semanticsLabel: "Carregando informações",
              semanticsValue: "Carregando",
            ),
          );
        }

        if (state.pjeProcessCompleteContentState ==
            PjeProcessCompleteContentState.SUCCESS) {
          var movementsList =
              state.pjeProcessCompleteModel?.data?.processo?.movimento;

          return ListView.separated(
            itemCount: movementsList?.length ?? 0,
            itemBuilder: (BuildContext context, index) {
              Movimento item = movementsList![index];

              String date = "-";

              if (item.dataHora?.value != null) {
                var dataHora = item.dataHora!.value!;
                var dateTime = DateTime.parse(
                  dataHora.substring(0, 8) + 'T' + dataHora.substring(8),
                );

                date = DateFormat('dd/MM/yyyy HH:mm').format(dateTime);
              }

              DocumentoDataModel? document;

              if (state.documentsList != null) {
                document = state.documentsList
                    ?.where((e) => item.idDocumentoVinculado == e.idDocumento)
                    .firstOrNull;
              }

              return Row(
                children: <Widget>[
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        SizedBox(height: 12.r),
                        this._buildSection(
                          title: date,
                          content: item.movimentoNacional?.complemento
                                  ?.firstOrNull?.value ??
                              "-",
                          document: document,
                          code: item.movimentoNacional?.codigoNacional,
                        ),
                      ],
                    ),
                  ),
                ],
              );
            },
            separatorBuilder: (BuildContext context, index) {
              return Column(
                children: <Widget>[
                  SizedBox(height: 12.r),
                  Divider(
                    color: context.theme.colorScheme.outlineVariant,
                  ),
                ],
              );
            },
          );
        }

        return Container(
          height: context.height,
          width: context.width,
          child: NotContentFoundComponent(
            typeMessage: "Não há movimentações para este processo",
            isPersonalizedMessage: true,
          ),
        );
      },
    );
  }

  Widget _documentsList() {
    return BlocBuilder<PjePublicConsultDetailsCubit,
        PjePublicConsultDetailsState>(
      bloc: Get.find<PjePublicConsultDetailsCubit>(),
      builder: (BuildContext context, PjePublicConsultDetailsState state) {
        if (state.pjeDocumentsContentState ==
            PjeDocumentsContentState.LOADING) {
          return Center(
            child: CircularProgressIndicator(
              color: context.theme.primaryColor,
              semanticsLabel: "Carregando informações",
              semanticsValue: "Carregando",
            ),
          );
        }

        return ListView.separated(
          itemCount: state.documentsList?.length ?? 0,
          itemBuilder: (BuildContext context, index) {
            DocumentoDataModel item = state.documentsList![index];

            String date = "-";

            if (item.dataHora?.value != null) {
              var dataHora = item.dataHora!.value!;
              var dateTime = DateTime.parse(
                dataHora.substring(0, 8) + 'T' + dataHora.substring(8),
              );

              date = DateFormat('dd/MM/yyyy HH:mm').format(dateTime);
            }

            return Row(
              children: <Widget>[
                Expanded(
                  child: this._buildSection(
                    title: date,
                    content: item.descricao?.toUpperCase() ?? "-",
                  ),
                ),
                SizedBox(width: 8.r),
                if (!item.loadContent) ...[
                  InkWell(
                    overlayColor:
                        WidgetStateProperty.all<Color>(Colors.transparent),
                    child: Row(
                      children: <Widget>[
                        Text(
                          "Ver anexo",
                          style: TextStyle(
                            fontSize: FontSizeManager.s14,
                            color: ColorManager.primaryColor,
                          ),
                        ),
                        SizedBox(width: 8.r),
                        Icon(
                          semanticLabel: "Ao clicar neste ícone você"
                              "é redirecionado para o arquivo pdf do documento ",
                          Icons.attach_file,
                          size: FontSizeManager.s20,
                          color: ColorManager.primaryColor,
                        ),
                      ],
                    ),
                    onTap: () async =>
                        await this._cubit.getDocumentFromProcessesSoap(item),
                  ),
                ] else ...[
                  Container(
                    padding: PaddingManager.medium,
                    height: 38.r,
                    width: 38.r,
                    child: CircularProgressIndicator(),
                  ),
                ]
              ],
            );
          },
          separatorBuilder: (BuildContext context, index) {
            return Column(
              children: <Widget>[
                SizedBox(height: 12.r),
                Divider(
                  color: context.theme.colorScheme.outlineVariant,
                ),
                SizedBox(height: 12.r),
              ],
            );
          },
        );
      },
    );
  }

  Widget _buildSection({
    required String title,
    required String content,
    int? code,
    Color? color,
    DocumentoDataModel? document,
    bool showDegree = false,
  }) {
    String degree = this.widget._degree.split("G").first;
    String? sameCodeMovement;

    this.mapWithDictionaryMovement?.forEach((key, value) {
      if (value.contains(code)) {
        sameCodeMovement = key;
      }
    });

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: <Widget>[
                  Text(
                    semanticsLabel: title,
                    title,
                    style: TextStyle(
                      fontSize: FontSizeManager.s14,
                      fontWeight: FontWeightManager.medium,
                      color: context.theme.colorScheme.outline,
                    ),
                  ),
                  if (sameCodeMovement != null) ...[
                    Padding(
                      padding: const EdgeInsets.only(right: 3.0),
                      child: Tooltip(
                        message: "Explicação em linguagem simples",
                        triggerMode: TooltipTriggerMode.tap,
                        child: Icon(
                          Icons.info_outline_rounded,
                          size: 16.r,
                          color: ColorManager.secondaryColor,
                        ),
                      ),
                    ),
                  ]
                ],
              ),
              SizedBox(height: 4.r),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: <Widget>[
                  if (sameCodeMovement != null) ...[
                    Flexible(
                      child: InkWell(
                        overlayColor:
                            WidgetStateProperty.all<Color>(Colors.transparent),
                        child: Text(
                          content.isNotEmpty ? content.toUpperCase() : "-",
                          style: TextStyle(
                            fontSize: FontSizeManager.s14,
                            fontWeight: FontWeightManager.medium,
                            color: color ?? Colors.blue,
                          ),
                        ),
                        onTap: () async {
                          Get.toNamed(
                            Routes.dictionaryJudicial,
                            arguments: {
                              'movementCase': sameCodeMovement?.toUpperCase(),
                              'isProcessRoute': true,
                            },
                          );
                        },
                      ),
                    ),
                  ] else ...[
                    Expanded(
                      child: SelectableText(
                        content.isNotEmpty ? content.toUpperCase() : "-",
                        style: TextStyle(
                          fontSize: FontSizeManager.s14,
                          fontWeight: FontWeightManager.medium,
                          color: color ?? Colors.black,
                        ),
                      ),
                    ),
                  ],
                  if (document != null) ...[
                    SizedBox(width: 40.r),
                    InkWell(
                      overlayColor:
                          WidgetStateProperty.all<Color>(Colors.transparent),
                      child: Icon(
                        Icons.file_open_outlined,
                        size: FontSizeManager.s20,
                        color: ColorManager.primaryColor,
                      ),
                      onTap: () async => await this
                          ._cubit
                          .getDocumentFromProcessesSoap(document),
                    ),
                  ]
                ],
              ),
            ],
          ),
        ),
        if (showDegree) ...[
          SizedBox(width: 8.r),
          Container(
            padding: PaddingManager.symmetricHorizontaSmall,
            child: Text.rich(
              TextSpan(
                children: <TextSpan>[
                  TextSpan(
                    semanticsLabel: degree,
                    text: degree,
                    style: TextStyle(
                      fontWeight: FontWeightManager.black,
                    ),
                  ),
                  TextSpan(
                    text: "º",
                    semanticsLabel: "º",
                  ),
                  TextSpan(
                    text: " ",
                    semanticsLabel: " ",
                  ),
                  TextSpan(
                    text: "Grau",
                    semanticsLabel: "Grau",
                  ),
                ],
              ),
              style: TextStyle(
                fontSize: FontSizeManager.s12,
                color: context.theme.colorScheme.outline,
                fontWeight: FontWeightManager.medium,
                // fontWeight: FontWeightManager.medium,
              ),
            ),
          ),
        ]
      ],
    );
  }

  Widget _pole(
    BuildContext context, {
    List<Polo>? poles,
  }) {
    if (poles == null) return const SizedBox.shrink();

    List<Polo> otherPoles = List<Polo>.from(poles);
    otherPoles.removeWhere((item) => item.polo == "AT" || item.polo == "PA");

    List<Polo> activePassivePoles = List<Polo>.from(poles);
    activePassivePoles
        .removeWhere((item) => item.polo != "AT" && item.polo != "PA");

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        this._buildPolesList(activePassivePoles),
        if (otherPoles.isNotEmpty) ...[
          Text(
            "Outros interessados",
            style: TextStyle(
              fontSize: FontSizeManager.s14,
              fontWeight: FontWeightManager.bold,
              color: context.theme.colorScheme.outline,
            ),
          ),
          Container(
            padding: PaddingManager.symmetricHorizontalMedium,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                SizedBox(height: 8.r),
                this._buildPolesList(otherPoles),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildPolesList(List<Polo> poles) {
    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: poles.length,
      itemBuilder: (BuildContext context, int index) {
        var pole = poles[index];

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Text(
              semanticsLabel: this._poleName(pole.polo ?? ""),
              this._poleName(pole.polo ?? ""),
              style: TextStyle(
                fontSize: FontSizeManager.s14,
                fontWeight: FontWeightManager.medium,
                color: context.theme.colorScheme.outline,
              ),
            ),
            SizedBox(height: 4.r),
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: pole.parte?.length ?? 0,
              itemBuilder: (BuildContext context, int index) {
                var part = pole.parte![index];

                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      semanticsLabel: part.pessoa?.nome,
                      part.pessoa?.nome?.toUpperCase() ?? "-",
                      softWrap: true,
                      style: TextStyle(
                        fontSize: FontSizeManager.s14,
                        color: context.theme.colorScheme.onSurface,
                        fontWeight: FontWeightManager.bold,
                      ),
                    ),
                    if (part.advogado != null && part.advogado!.isNotEmpty) ...[
                      SizedBox(height: 8.r),
                      Text(
                        semanticsLabel: "Advogados",
                        "Advogados",
                        style: TextStyle(
                          fontSize: FontSizeManager.s14,
                          fontWeight: FontWeightManager.medium,
                          color: context.theme.colorScheme.outline,
                        ),
                      ),
                      SizedBox(height: 8.r),
                      ListView.separated(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: part.advogado!.length,
                        itemBuilder: (BuildContext context, int index) {
                          var item = part.advogado![index];

                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: <Widget>[
                              Text(
                                semanticsLabel: item.nome,
                                item.nome?.toUpperCase() ?? "-",
                                maxLines: 1,
                                softWrap: true,
                                overflow: TextOverflow.ellipsis,
                                style: TextStyle(
                                  fontSize: FontSizeManager.s14,
                                  color: context.theme.colorScheme.onSurface,
                                  fontWeight: FontWeightManager.medium,
                                ),
                              ),
                              Text(
                                semanticsLabel: "OAB: ${item.inscricao?.value}",
                                "OAB: ${item.inscricao?.value ?? "-"}",
                                maxLines: 1,
                                softWrap: true,
                                overflow: TextOverflow.ellipsis,
                                style: TextStyle(
                                  fontSize: FontSizeManager.s12,
                                  color: context.theme.colorScheme.onSurface,
                                  fontWeight: FontWeightManager.regular,
                                ),
                              ),
                            ],
                          );
                        },
                        separatorBuilder: (BuildContext context, int index) {
                          return const SizedBox.shrink();
                        },
                      ),
                    ],
                  ],
                );
              },
            ),
            SizedBox(height: 8.r),
            const Divider(),
            SizedBox(height: 8.r),
          ],
        );
      },
      separatorBuilder: (BuildContext context, int index) {
        return const SizedBox.shrink();
      },
    );
  }

  String _poleName(String pole) {
    return switch (pole) {
      "AT" => "Polo ativo",
      "PA" => "Polo passivo",
      "TC" => "Terceiro",
      "FL" => "Fiscal da lei diverso",
      "TJ" => "Testemunha do juízo",
      "AD" => "Assistente simples desinteressado (amicus curiae)",
      "VI" => "Vítima",
      _ => ""
    };
  }
}
