part of 'pje_public_consult_details_cubit.dart';

enum ViewContentState { DEFAULT, LOADING, SUCCESS, ERROR }

enum OptionsDetailsContentState { DETAILS, MOVEMENTS, DOCUMENTS }

enum PjeProcessCompleteContentState { DEFAULT, LOADING, SUCCESS, ERROR }

enum PjeDocumentsContentState { DEFAULT, LOADING, SUCCESS, ERROR }

enum PjeReportContentState { DEFAULT, LOADING, SUCCESS, ERROR }

enum PjeWebLinkContentState { DEFAULT, LOADING, SUCCESS, ERROR }

enum PjeLoginContentState { DEFAULT, LOADING, SUCCESS, ERROR }

class PjePublicConsultDetailsState extends Equatable {
  final ViewContentState viewContentState;
  final OptionsDetailsContentState optionsDetailsContentState;
  final PjeWebLinkContentState pjeWebLinkContentState;
  final PjeLoginContentState pjeLoginContentState;
  final PjeProcessCompleteContentState pjeProcessCompleteContentState;
  final KeycloakAuthorizationModel? keycloakAuthorizationModel;
  final PjeProcessCompleteModel? pjeProcessCompleteModel;
  final PjeDocumentsContentState pjeDocumentsContentState;
  final List<DocumentoDataModel>? documentsList;
  final List<Movimento>? movementList;
  final PjeReportContentState pjeReportContentState;
  final PjeLoginViewModel? pjeLoginViewModel;
  final Failure? failure;

  const PjePublicConsultDetailsState({
    this.viewContentState = ViewContentState.DEFAULT,
    this.pjeWebLinkContentState = PjeWebLinkContentState.DEFAULT,
    this.optionsDetailsContentState = OptionsDetailsContentState.DETAILS,
    this.pjeLoginContentState = PjeLoginContentState.DEFAULT,
    this.pjeProcessCompleteContentState =
        PjeProcessCompleteContentState.DEFAULT,
    this.keycloakAuthorizationModel,
    this.pjeLoginViewModel,
    this.pjeProcessCompleteModel,
    this.movementList,
    this.pjeDocumentsContentState = PjeDocumentsContentState.DEFAULT,
    this.documentsList,
    this.pjeReportContentState = PjeReportContentState.DEFAULT,
    this.failure,
  });

  PjePublicConsultDetailsState copyWith({
    ViewContentState? viewContentState,
    PjeWebLinkContentState? pjeWebLinkContentState,
    PjeLoginContentState? pjeLoginContentState,
    PjeLoginViewModel? pjeLoginViewModel,
    OptionsDetailsContentState? optionsDetailsContentState,
    PjeProcessCompleteContentState? pjeProcessCompleteContentState,
    KeycloakAuthorizationModel? keycloakAuthorizationModel,
    PjeProcessCompleteModel? pjeProcessCompleteModel,
    List<Movimento>? movemetList,
    PjeDocumentsContentState? pjeDocumentsContentState,
    List<DocumentoDataModel>? documentsList,
    PjeReportContentState? pjeReportContentState,
    Failure? failure,
  }) {
    return PjePublicConsultDetailsState(
      pjeWebLinkContentState:
          pjeWebLinkContentState ?? this.pjeWebLinkContentState,
      pjeLoginViewModel: pjeLoginViewModel ?? this.pjeLoginViewModel,
      viewContentState: viewContentState ?? this.viewContentState,
      optionsDetailsContentState:
          optionsDetailsContentState ?? this.optionsDetailsContentState,
      keycloakAuthorizationModel:
          keycloakAuthorizationModel ?? this.keycloakAuthorizationModel,
      pjeProcessCompleteContentState:
          pjeProcessCompleteContentState ?? this.pjeProcessCompleteContentState,
      pjeProcessCompleteModel:
          pjeProcessCompleteModel ?? this.pjeProcessCompleteModel,
      pjeDocumentsContentState:
          pjeDocumentsContentState ?? this.pjeDocumentsContentState,
      documentsList: documentsList ?? this.documentsList,
      pjeReportContentState:
          pjeReportContentState ?? this.pjeReportContentState,
      movementList: movemetList ?? this.movementList,
      failure: failure ?? this.failure,
    );
  }

  List<Object?> get props => [
        movementList,
        pjeLoginContentState,
        pjeWebLinkContentState,
        pjeLoginViewModel,
        viewContentState,
        optionsDetailsContentState,
        pjeProcessCompleteContentState,
        keycloakAuthorizationModel,
        pjeProcessCompleteModel,
        pjeDocumentsContentState,
        documentsList,
        pjeReportContentState,
        failure,
      ];
}
