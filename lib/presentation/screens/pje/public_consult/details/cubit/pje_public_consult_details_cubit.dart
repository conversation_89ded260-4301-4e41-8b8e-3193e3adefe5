import 'dart:convert';
import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:downloadsfolder/downloadsfolder.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';
import 'package:html_to_pdf_plus/html_to_pdf_plus.dart';
import 'package:mime/mime.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:tjcemobile/app/shared/utils/snackbar_utils.dart';
import 'package:tjcemobile/data/model/audience_pdf_model.dart';
import 'package:tjcemobile/data/model/keycloak_authorization_model.dart';
import 'package:tjcemobile/data/model/pje_general_response_model.dart';
import 'package:tjcemobile/data/model/pje_process_complete_model.dart';
import 'package:tjcemobile/data/network/failure.dart';
import 'package:tjcemobile/domain/usecase/pje/pje_public_consult_details_usecase.dart';
import 'package:tjcemobile/domain/viewmodel/pje_log_viewmodel.dart';
import 'package:tjcemobile/domain/viewmodel/pje_process_mni_viewmodel.dart';
import 'package:tjcemobile/domain/viewmodel/pje_process_report_viewmodel.dart';
import 'package:tjcemobile/presentation/resources/routes_manager.dart';
import 'package:tjcemobile/presentation/screens/pje/public_consult/details/components/pje_pdf_component.dart';
import 'package:tjcemobile/presentation/widgets/pdf_component.dart';
import 'package:uuid/uuid.dart';

part 'pje_public_consult_details_state.dart';

class PjePublicConsultDetailsCubit<T>
    extends Cubit<PjePublicConsultDetailsState> {
  final PjePublicConsultDetailsUseCase _useCase;

  PjePublicConsultDetailsCubit(this._useCase)
      : super(PjePublicConsultDetailsState());

  Future<void> setSegmentedSelected(
    OptionsDetailsContentState? selected,
  ) async {
    emit(state.copyWith(optionsDetailsContentState: selected));
  }

  Future<void> verifyLoginPJe(String? route, String degree) async {
    if ([Routes.lawyerProcessRoute, Routes.lawyerCalendarDetailsRoute]
        .contains(route)) {
      PjeLoginViewModel? pjeLoginViewModel =
          await this.getPjeLoginViewModel(degree);

      if (pjeLoginViewModel?.consultantPassword != null &&
          pjeLoginViewModel?.consultantId != null) {
        emit(state.copyWith(pjeLoginViewModel: pjeLoginViewModel));
      }
    }
  }

  Future<void> getProcessWithoutDocuments(
    String? oab,
    String processNumber,
    String degree, {
    bool isFirstAccess = false,
  }) async {
    var currentOption = state.optionsDetailsContentState;

    var isDetails = currentOption == OptionsDetailsContentState.DETAILS;
    var isMovements = currentOption == OptionsDetailsContentState.MOVEMENTS;
    var isDocuments = currentOption == OptionsDetailsContentState.DOCUMENTS;

    var pjeLogViewModel = PjeLogViewModel(
      oab: oab,
      cpf: state.pjeLoginViewModel?.consultantId,
      processo: processNumber,
    );

    if (isDetails || isMovements) {
      emit(state.copyWith(
        pjeProcessCompleteContentState: PjeProcessCompleteContentState.LOADING,
      ));
    }

    if (isDocuments) {
      emit(state.copyWith(
        pjeDocumentsContentState: PjeDocumentsContentState.LOADING,
      ));
    }

    await this._postProcessMNISoap(
      processNumber,
      degree,
      isFirstAccess: isDocuments,
      isDetails: isDetails,
      isMovements: isMovements || isDocuments,
      isDocuments: isMovements || isDocuments,
      pjeLogViewModel: pjeLogViewModel,
    );

    if (currentOption == OptionsDetailsContentState.DETAILS) {
      await this._mountDetails();

      return;
    }

    emit(state.copyWith(
      pjeProcessCompleteContentState: PjeProcessCompleteContentState.SUCCESS,
      pjeDocumentsContentState: PjeDocumentsContentState.SUCCESS,
    ));
  }

// No Cubit

  Future<void> _postProcessMNISoap(
    String processNumber,
    String degree, {
    bool isFirstAccess = false,
    PjeLogViewModel? pjeLogViewModel,
    bool isDetails = false,
    bool isMovements = false,
    bool isDocuments = false,
  }) async {
    bool useBaseUri2G = degree == "2G";

    if (isMovements && isDocuments) {
      await _fetchMovements(processNumber, degree, isDetails, useBaseUri2G);

      await _fetchDocuments(processNumber, degree, isFirstAccess,
          pjeLogViewModel, isDetails, useBaseUri2G);
      return;
    }

    await (await this._useCase.postProcessMNISoap(
              processNumber,
              pjeLoginViewModel: state.pjeLoginViewModel,
              useBaseUri2G: useBaseUri2G,
              includeHeader: isDetails,
              includeMovements: isMovements,
              includeDocuments: isDocuments,
            ))
        .fold(
      (Failure failure) {
        emit(state.copyWith(
          pjeProcessCompleteContentState: PjeProcessCompleteContentState.ERROR,
          failure: failure,
        ));
        SnackbarUtils.error("", failure.message ?? "Erro ao realizar login");
      },
      (PjeProcessCompleteModel model) async {
        if (isFirstAccess && state.pjeLoginViewModel?.consultantId != null) {
          await this._pjeLog(pjeLogViewModel);
        }

        this._processMovements(model, degree, processNumber);

        await this._processDocuments(model, degree, processNumber);

        emit(state.copyWith(pjeProcessCompleteModel: model));
      },
    );
  }

  Future<void> _fetchMovements(
    String processNumber,
    String degree,
    bool isDetails,
    bool useBaseUri2G,
  ) async {
    await (await this._useCase.postProcessMNISoap(
              processNumber,
              pjeLoginViewModel: state.pjeLoginViewModel,
              useBaseUri2G: useBaseUri2G,
              includeHeader: isDetails,
              includeMovements: true,
              includeDocuments: false,
            ))
        .fold(
      (Failure failure) {
        emit(state.copyWith(
          pjeProcessCompleteContentState: PjeProcessCompleteContentState.ERROR,
          failure: failure,
        ));

        SnackbarUtils.error("", failure.message ?? "Erro ao realizar login");
      },
      (PjeProcessCompleteModel model) async {
        this._processMovements(model, degree, processNumber);

        emit(state.copyWith(pjeProcessCompleteModel: model));
      },
    );
  }

  Future<void> _fetchDocuments(
    String processNumber,
    String degree,
    bool isFirstAccess,
    PjeLogViewModel? pjeLogViewModel,
    bool isDetails,
    bool useBaseUri2G,
  ) async {
    await (await this._useCase.postProcessMNISoap(
              processNumber,
              pjeLoginViewModel: state.pjeLoginViewModel,
              useBaseUri2G: useBaseUri2G,
              includeHeader: isDetails,
              includeMovements: false,
              includeDocuments: true,
            ))
        .fold(
      (Failure failure) {
        emit(state.copyWith(
          pjeProcessCompleteContentState: PjeProcessCompleteContentState.ERROR,
          failure: failure,
        ));

        SnackbarUtils.error("", failure.message ?? "Erro ao realizar login");
      },
      (PjeProcessCompleteModel model) async {
        if (isFirstAccess && state.pjeLoginViewModel?.consultantId != null) {
          await this._pjeLog(pjeLogViewModel);
        }

        await this._processDocuments(model, degree, processNumber);

        emit(state.copyWith(
          pjeProcessCompleteModel: state.pjeProcessCompleteModel,
        ));
      },
    );
  }

  void _processMovements(
    PjeProcessCompleteModel model,
    String degree,
    String processNumber,
  ) {
    model.data?.grau = degree;

    model.data?.processo?.movimento?.sort((a, b) {
      return b.dataHora?.value?.compareTo(a.dataHora?.value ?? "") ?? 0;
    });

    var movement = model.data?.processo?.movimento;

    if (movement?.isNotEmpty ?? false) {
      state.pjeProcessCompleteModel?.data?.processo?.movimento = movement;
    }
  }

  Future<void> _processDocuments(
    PjeProcessCompleteModel model,
    String degree,
    String processNumber,
  ) async {
    var document = model.data?.processo?.documento;

    if (document?.isNotEmpty ?? false) {
      List<DocumentoDataModel> documentsList = [];

      var pjeDegree = degree == "2G" ? "pje2g" : "pje1g";
      var response = await rootBundle
          .loadString('assets/data/document_type_${pjeDegree}.json');

      List<int> typeDocumentsList = await json.decode(response).cast<int>();

      model.data?.processo?.documento?.forEach((item) {
        item.grau = degree;
        item.numeroProcesso = processNumber;

        if (state.pjeLoginViewModel == null) {
          var typeDocument = int.tryParse(item.tipoDocumento ?? "0");

          if (typeDocumentsList.contains(typeDocument)) {
            documentsList.add(item);
          }

          return;
        }

        documentsList.add(item);
      });

      emit(state.copyWith(documentsList: documentsList));
    }
  }

  Future<void> _mountDetails() async {
    var degree = state.pjeProcessCompleteModel?.data?.grau ?? "";
    var process = state.pjeProcessCompleteModel?.data?.processo;

    var idCourt = process?.dadosBasicos?.codigoLocalidade ?? "";
    var idClass = process?.dadosBasicos?.classeProcessual.toString() ?? "";
    var idSubjects = process?.dadosBasicos?.assunto
            ?.map((e) => e.codigoNacional.toString())
            .toList() ??
        [];

    try {
      await Future.wait([
        this._postCourt(degree),
        this._postCourtClass(degree, idCourt),
        this._postCourtMatter(degree, idCourt, idClass),
        this._postCompetence(degree, idCourt, idClass, idSubjects),
      ]);

      emit(state.copyWith(
        pjeProcessCompleteContentState: PjeProcessCompleteContentState.SUCCESS,
      ));
    } on Failure catch (failure) {
      emit(state.copyWith(
        pjeProcessCompleteContentState: PjeProcessCompleteContentState.ERROR,
        failure: failure,
      ));
    } catch (e) {
      emit(state.copyWith(
        pjeProcessCompleteContentState: PjeProcessCompleteContentState.ERROR,
      ));
    }
  }

  Future<bool> _verifyStoragePermission() async {
    Map<Permission, PermissionStatus> statuses = await [
      Permission.storage,
      Permission.manageExternalStorage,
      Permission.mediaLibrary,
    ].request();

    var storage = statuses[Permission.storage];
    var externalStorage = statuses[Permission.manageExternalStorage];
    var mediaLibrary = statuses[Permission.mediaLibrary];

    if (Platform.isAndroid) {
      DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
      AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
      String androidVersion = androidInfo.version.release;

      var androidVersionInt = int.parse(androidVersion);
      if (androidVersionInt > 12 && mediaLibrary == PermissionStatus.granted) {
        return true;
      }

      if (externalStorage == PermissionStatus.permanentlyDenied ||
          mediaLibrary == PermissionStatus.permanentlyDenied) {
        await openAppSettings();

        return await this._verifyStoragePermission();
      }
    }

    if (storage == PermissionStatus.permanentlyDenied) {
      await openAppSettings();

      return await this._verifyStoragePermission();
    }

    if (storage != PermissionStatus.granted) {
      return false;
    }

    return true;
  }

  Future<void> getDocumentFromProcessesSoap(DocumentoDataModel item) async {
    await this._toggleLoadForDocument(item.idDocumento);

    var hasPermission = await this._verifyStoragePermission();

    if (!hasPermission) {
      SnackbarUtils.error(
        "Permissão de armazenamento não concedida.",
        "Para baixar o arquivo, conceda permissão de armazenamento ao aplicativo.",
      );

      await this._toggleLoadForDocument(item.idDocumento);

      return;
    }

    var process = item.numeroProcesso ?? "";

    bool useBaseUri2G = item.grau == "2G" ? true : false;

    await (await this._useCase.postProcessMNISoap(
              process,
              documentId: item.idDocumento,
              useBaseUri2G: useBaseUri2G,
              pjeLoginViewModel: state.pjeLoginViewModel,
            ))
        .fold(
      (Failure failure) {
        emit(state.copyWith(
          pjeDocumentsContentState: PjeDocumentsContentState.ERROR,
          failure: failure,
        ));
      },
      (PjeProcessCompleteModel model) async {
        item.conteudo = model.data?.processo?.documento?.single.conteudo;
      },
    );

    if (item.conteudo != null) {
      String? path = await this.mountFile(item);
      String? extension = extensionFromMime(item.mimetype ?? "");

      if (extension == null) {
        extension = item.mimetype?.toLowerCase() ?? "";
      }

      if (path != null && ["pdf", "html"].contains(extension)) {
        await this.redirectToAttachedFile(item, path);
      } else {
        SnackbarUtils.information(
          "O arquivo foi baixado para a pasta de 'Downloads' do seu dispositivo.",
        );
      }
    }

    await this._toggleLoadForDocument(item.idDocumento);
  }

  Future<void> _toggleLoadForDocument(String? documentId) async {
    var document = state.documentsList
        ?.firstWhereOrNull((element) => element.idDocumento == documentId);

    if (document != null) {
      await this._toggleDocumentLoad(document);

      return;
    }

    for (var item in state.documentsList ?? []) {
      var linkedDocumentIndex = item.documentoVinculado
          ?.indexWhere((linked) => linked.idDocumento == documentId);

      if (linkedDocumentIndex != null && linkedDocumentIndex != -1) {
        await this
            ._toggleDocumentLoad(item.documentoVinculado![linkedDocumentIndex]);

        return;
      }
    }
  }

  Future<void> _toggleDocumentLoad(dynamic document) async {
    document.loadContent = !document.loadContent;

    emit(state.copyWith(documentsList: state.documentsList));

    await Get.forceAppUpdate();
  }

  Future<void> _postCourt(String degree) async {
    bool useBaseUri2G = degree == "2G" ? true : false;

    await (await this._useCase.postCourt(useBaseUri2G: useBaseUri2G)).fold(
        (Failure failure) {
      emit(state.copyWith(
        pjeDocumentsContentState: PjeDocumentsContentState.ERROR,
        failure: failure,
      ));
    }, (PjeGeneralResponseModel model) async {
      var dataMap = {
        for (PjeGeneralResponseDataModel item in model.data ?? [])
          item.id: item.descricao
      };

      var process = state.pjeProcessCompleteModel?.data?.processo;
      var item = process?.dadosBasicos?.codigoLocalidade ?? "";

      if (process?.dadosBasicos != null) {
        process!.dadosBasicos!.codigoLocalidadeDescricao = dataMap[item];

        state.pjeProcessCompleteModel?.data?.processo?.dadosBasicos =
            process.dadosBasicos;

        emit(state.copyWith(
          pjeProcessCompleteModel: state.pjeProcessCompleteModel,
        ));
      }
    });
  }

  Future<void> _postCourtClass(String degree, String idCourt) async {
    bool useBaseUri2G = degree == "2G" ? true : false;

    await (await this
            ._useCase
            .postCourtClass(idCourt, useBaseUri2G: useBaseUri2G))
        .fold((Failure failure) {
      emit(state.copyWith(
        pjeDocumentsContentState: PjeDocumentsContentState.ERROR,
        failure: failure,
      ));
    }, (PjeGeneralResponseModel model) async {
      var dataMap = {
        for (PjeGeneralResponseDataModel item in model.data ?? [])
          item.codigo: item.descricao
      };

      var process = state.pjeProcessCompleteModel?.data?.processo;
      var item = process?.dadosBasicos?.classeProcessual.toString() ?? "";

      if (process?.dadosBasicos != null) {
        process!.dadosBasicos!.classeProcessualDescricao = dataMap[item];

        state.pjeProcessCompleteModel?.data?.processo?.dadosBasicos =
            process.dadosBasicos;

        emit(state.copyWith(
          pjeProcessCompleteModel: state.pjeProcessCompleteModel,
        ));
      }
    });
  }

  Future<void> _postCourtMatter(
    String degree,
    String idCourt,
    String idClass,
  ) async {
    bool useBaseUri2G = degree == "2G" ? true : false;

    await (await this
            ._useCase
            .postCourtMatter(idCourt, idClass, useBaseUri2G: useBaseUri2G))
        .fold(
      (Failure failure) {
        emit(state.copyWith(
          pjeDocumentsContentState: PjeDocumentsContentState.ERROR,
          failure: failure,
        ));
      },
      (PjeGeneralResponseModel model) async {
        var dataMap = {
          for (PjeGeneralResponseDataModel item in model.data ?? [])
            item.codigo: item.descricao
        };

        var process = state.pjeProcessCompleteModel?.data?.processo;

        if (process?.dadosBasicos != null) {
          process!.dadosBasicos!.assunto?.forEach((element) {
            var item = element.codigoNacional.toString();

            element.codigoNacionalDescricao = dataMap[item];
          });

          state.pjeProcessCompleteModel?.data?.processo?.dadosBasicos =
              process.dadosBasicos;

          emit(state.copyWith(
            pjeProcessCompleteModel: state.pjeProcessCompleteModel,
          ));
        }
      },
    );
  }

  Future<void> _postCompetence(
    String degree,
    String idCourt,
    String idClass,
    List<String> idSubjects,
  ) async {
    bool useBaseUri2G = degree == "2G" ? true : false;

    for (var idSubject in idSubjects) {
      await (await this._useCase.postCompetence(idCourt, idClass, idSubject,
              useBaseUri2G: useBaseUri2G))
          .fold(
        (Failure failure) {
          emit(state.copyWith(
            pjeDocumentsContentState: PjeDocumentsContentState.ERROR,
            failure: failure,
          ));
        },
        (PjeGeneralResponseModel model) async {
          var dataMap = {
            for (PjeGeneralResponseDataModel item in model.data ?? [])
              item.id: item.descricao
          };

          var process = state.pjeProcessCompleteModel?.data?.processo;
          var item = process?.dadosBasicos!.competencia.toString() ?? "";

          if (process?.dadosBasicos != null) {
            process!.dadosBasicos!.competenciaDescricao = dataMap[item];

            state.pjeProcessCompleteModel?.data?.processo?.dadosBasicos =
                process.dadosBasicos;

            emit(state.copyWith(
              pjeProcessCompleteModel: state.pjeProcessCompleteModel,
            ));
          }
        },
      );
    }
  }

  Future<String?> mountFile(DocumentoDataModel model) async {
    var content = model.conteudo?.include;
    var bytes = latin1.encode(content ?? "");

    Directory directory = await getApplicationCacheDirectory();
    String? extension = extensionFromMime(model.mimetype ?? "");

    if (extension == null) {
      extension = model.mimetype?.toLowerCase() ?? "";
    }

    if (!["pdf", "html"].contains(extension)) {
      Directory externalDirectory = Platform.isAndroid
          ? await getDownloadDirectory()
          : await getApplicationDocumentsDirectory();

      directory = externalDirectory;
    }

    var path = directory.path;
    var hasDirectory = await Directory(path).exists();

    if (!hasDirectory) {
      await Directory(path).create(recursive: true);
    }

    if (model.mimetype == "text/html") {
      String html = latin1.decode(bytes);

      html =
          "<style>body{margin: 1.5cm 2cm 1.5cm 2cm;} .page{ ;border:initial;border-radius:initial;width:initial;min-height:initial;box-shadow:initial;background:initial;page-break-after:always}}</style>"
          "<body><div class='page'>$html</div></body>";

      var generatedPdfFile = await HtmlToPdf.convertFromHtmlContent(
        htmlContent: html,
        configuration: PdfConfiguration(
          targetDirectory: path,
          targetName: Uuid().v4(),
          linksClickable: true,
        ),
      );

      return generatedPdfFile.path;
    }

    String? fileExtension = extensionFromMime(model.mimetype ?? "");

    if (fileExtension == null) {
      fileExtension = model.mimetype?.toLowerCase() ?? "";
    }

    String fileName = Uuid().v4() + "." + fileExtension;
    File file = File(path + "/" + fileName);

    await file.writeAsBytes(bytes);

    return file.path;
  }

  Future<void> redirectToAttachedFile(
    DocumentoDataModel model,
    String filePath,
  ) async {
    await Get.to(
      () => PjePDFComponent(
        title: model.descricao ?? "",
        path: filePath,
        documentId: model.idDocumento ?? "",
        degree: model.grau ?? "",
      ),
    );
  }

  Future<void> getReportProcessFromPJe(
    String processNumber,
    String degree,
  ) async {
    emit(state.copyWith(
      pjeReportContentState: PjeReportContentState.LOADING,
    ));

    var viewModel = PjeProcessReportViewModel(
      processo: processNumber,
      grau: degree,
    );

    (await this._useCase.getReportProcessFromPJe(viewModel)).fold(
      (Failure failure) async {
        emit(state.copyWith(
          pjeReportContentState: PjeReportContentState.ERROR,
          failure: failure,
        ));

        SnackbarUtils.error(
          failure.title,
          failure.message ?? "",
        );
      },
      (AudiencePDFModel model) async {
        emit(state.copyWith(
          pjeReportContentState: PjeReportContentState.SUCCESS,
        ));

        var fileBase64 = model.data?.relatorio ?? "";

        var path = await this.mountPathToPDF(processNumber, fileBase64);

        await Get.to(
          () => PDFComponent(title: "Relatório do processo", path: path),
        );
      },
    );
  }

  Future<String> mountPathToPDF(String process, String fileBase64) async {
    Uint8List bytes = base64.decode(fileBase64);
    String fileName = process + "_relatorio.pdf";

    Directory tempDir = await getTemporaryDirectory();
    String tempDocumentPath = tempDir.path + "/" + fileName;

    final file = await File(tempDocumentPath).create(recursive: true);
    await file.writeAsBytes(bytes);

    return file.path;
  }

  Future<Map<String, dynamic>> getLocalJsonMovementID() async {
    var response = await rootBundle
        .loadString('assets/data/local_dictionary_judicial_movement.json');
    Map<String, dynamic> data = await json.decode(response);

    return data;
  }

  Future<PjeLoginViewModel?> getPjeLoginViewModel(String degree) async {
    return await (await this._useCase.getPjeLoginViewModel()).fold(
      (_) => null,
      (List<PjeLoginViewModel> list) async {
        var response = list.firstWhereOrNull(
            (element) => element.degree?.contains(degree) ?? false);
        return response;
      },
    );
  }

  Future<void> _pjeLog(PjeLogViewModel? pjeLogViewModel) async {
    (await this._useCase.postPjeLog(pjeLogViewModel)).fold(
      (_) => null,
      (void _) => null,
    );
  }
}
