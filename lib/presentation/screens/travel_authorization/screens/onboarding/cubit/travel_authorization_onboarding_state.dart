part of 'travel_authorization_onboarding_cubit.dart';

class TravelAuthorizationOnboardingState extends Equatable {
  final PageController? pageController;

  const TravelAuthorizationOnboardingState({this.pageController});

  TravelAuthorizationOnboardingState copyWith({
    PageController? pageController,
  }) {
    return TravelAuthorizationOnboardingState(
      pageController: pageController ?? this.pageController,
    );
  }

  @override
  List<Object?> get props => [
        pageController,
      ];
}
