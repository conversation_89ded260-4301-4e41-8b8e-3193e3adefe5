import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tjcemobile/data/network/failure.dart';
import 'package:tjcemobile/domain/usecase/travel_authorization/travel_authorization_onboarding_usecase.dart';
import 'package:tjcemobile/domain/viewmodel/files_for_sign_viewmodel.dart';
import 'package:tjcemobile/domain/viewmodel/travel_authorization_form_viewmodel.dart';
import 'package:tjcemobile/presentation/screens/travel_authorization/components/travel_authorization_in_progress_component.dart';
import 'package:tjcemobile/presentation/screens/travel_authorization/components/travel_authorization_sign_component.dart';

part 'travel_authorization_onboarding_state.dart';

class TravelAuthorizationOnboardingCubit
    extends Cubit<TravelAuthorizationOnboardingState> {
  final TravelAuthorizationOnboardingUseCase _useCase;

  TravelAuthorizationOnboardingCubit(this._useCase)
      : super(TravelAuthorizationOnboardingState());

  Future<void> execute(PageController pageController) async {
    emit(state.copyWith(pageController: pageController));

    await this._getTravelAuthorizationFormViewModelList();
    await this._getFilesForSignViewModelList();
  }

  Future<void> _getTravelAuthorizationFormViewModelList() async {
    (await this._useCase.getTravelAuthorizationFormViewModelList())
        .fold((Failure failure) => null,
            (List<TravelAuthorizationFormViewModel> list) async {
      if (list.isNotEmpty) {
        await Get.dialog(
          TravelAuthorizationInProgressComponent(
            authorizationFormViewModel: list.single,
          ),
          barrierDismissible: false,
        );
      }
    });
  }

  Future<void> _getFilesForSignViewModelList() async {
    (await this._useCase.getFilesForSignViewModelList()).fold(
        (Failure failure) => null, (List<FilesForSignViewModel> list) async {
      if (list.isNotEmpty) {
        var viewModel = list.last;

        if (viewModel.travelDate == null || viewModel.travelDate!.isEmpty)
          return;

        var currentDateTime = DateTime.now();
        var travelDateTime = DateTime.parse(list.single.travelDate!);

        if (currentDateTime.isAfter(travelDateTime)) {
          await this.removeFilesForSignViewModelList();
          return;
        }

        await Get.dialog(
          TravelAuthorizationSignComponent(filesForSignViewModel: list.single),
          barrierDismissible: false,
        );
      }
    });
  }

  Future<void> removeFilesForSignViewModelList() async {
    (await this._useCase.removeFilesForSignViewModelList())
        .fold((Failure failure) => null, (void _) async {
      Get.back();
    });
  }

  Future<void> removeTravelAuthorizationFormViewModelList() async {
    (await this._useCase.removeTravelAuthorizationFormViewModelList())
        .fold((Failure failure) => null, (void _) async {
      Get.back();
    });
  }

  void nextPage() {
    state.pageController?.nextPage(
      duration: const Duration(milliseconds: 525),
      curve: Curves.ease,
    );
  }

  void previousPage() {
    state.pageController?.previousPage(
      duration: const Duration(milliseconds: 525),
      curve: Curves.ease,
    );
  }

  void goToNational() => state.pageController?.jumpToPage(2);

  void goToInternational() => state.pageController?.jumpToPage(3);
}
