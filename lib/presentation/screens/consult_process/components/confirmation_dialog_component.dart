import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ConfirmationDialogComponent extends StatelessWidget {
  final String title;
  final String content;
  final VoidCallback onConfirm;

  ConfirmationDialogComponent({
    required this.title,
    required this.content,
    required this.onConfirm,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(title),
      content: Text(content),
      actions: <Widget>[
        TextButton(
          child: Text("Não"),
          onPressed: () => Get.back(),
        ),
        TextButton(
          child: Text("Sim"),
          onPressed: () async {
            Future.delayed(const Duration(milliseconds: 500), () {
              Get.back();
            });
            onConfirm();
          },
        ),
      ],
    );
  }
}
