import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:tjcemobile/app/firebase/firebase_remote_config_preferences.dart';
import 'package:tjcemobile/app/shared/utils/device_type_utils.dart';
import 'package:tjcemobile/app/shared/utils/url_launcher_utils.dart';
import 'package:tjcemobile/presentation/widgets/accessibility_utils.dart';

class BannerComponentWidget extends StatefulWidget {
  BannerComponentWidget({Key? key}) : super(key: key);

  @override
  State<BannerComponentWidget> createState() => _BannerComponentWidgetState();
}

class _BannerComponentWidgetState extends State<BannerComponentWidget> {
  final ValueNotifier<bool> _hasNetworkNotifier = ValueNotifier(true);

  final String urlImage = FirebaseRemoteConfigPreferences.homeBannerUrl;
  final String urlLink = FirebaseRemoteConfigPreferences.homeBannerLinkUrl;

  void _launchUrl() async {
    await UrlLauncherUtils.launchUrl(urlLink);
  }

  @override
  Widget build(BuildContext context) {
    return AccessibilityUtils.button(
      semanticLabel: 'Botão. Banner de transformação digital',
      semanticHint:
          'toque duas vezes para saber mais. Você será redirecionado através do navegador.',
      type: ButtonType.gestureDetector,
      child: GestureDetector(
        onTap: _launchUrl,
        child: Card(
          elevation: 0.0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8.r),
            side: BorderSide(
              color: context.theme.colorScheme.outlineVariant,
              width: 1.r,
            ),
          ),
          child: ValueListenableBuilder<bool>(
            valueListenable: this._hasNetworkNotifier,
            builder: (context, bool hasNetwork, _) {
              return Container(
                height: DeviceTypeUtils.current == DeviceTypeManager.TABLET
                    ? 160.r
                    : 120.r,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8.r),
                  image: hasNetwork
                      ? DecorationImage(
                          image: NetworkImage(urlImage),
                          fit: BoxFit.cover,
                          onError: (exception, stackTrace) async {
                            this._hasNetworkNotifier.value = false;
                          },
                        )
                      : DecorationImage(
                          image: AssetImage('assets/transformacao_digital.png'),
                          fit: BoxFit.cover,
                        ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}
