import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:tjcemobile/app/di.dart';
import 'package:tjcemobile/data/model/process_model.dart';
import 'package:tjcemobile/presentation/resources/color_manager.dart';
import 'package:tjcemobile/presentation/resources/font_manager.dart';
import 'package:tjcemobile/presentation/resources/padding_manager.dart';
import 'package:tjcemobile/presentation/screens/citizen/components/dialogs/citizen_login_dialog.dart';
import 'package:tjcemobile/presentation/screens/citizen/components/dialogs/lawyer_inside_notification_logout_dialog.dart';
import 'package:tjcemobile/presentation/screens/consult_process/components/process_card_component.dart';
import 'package:tjcemobile/presentation/screens/home/<USER>/my_notified/cubit/my_notified_cubit.dart';
import 'package:tjcemobile/presentation/widgets/accessibility_utils.dart';
import 'package:tjcemobile/presentation/widgets/custom_base_scaffold.dart';
import 'package:tjcemobile/presentation/widgets/not_content_found_component.dart';
import 'package:tjcemobile/presentation/widgets/shimmer_card_component.dart';

class MyNotifiedComponent extends StatefulWidget {
  final BottomNavigationBar _bottomNavigationBar;

  const MyNotifiedComponent({
    required BottomNavigationBar bottomNavigationBar,
    Key? key,
  })  : this._bottomNavigationBar = bottomNavigationBar,
        super(key: key);

  @override
  State<MyNotifiedComponent> createState() => _MyNotifiedComponentState();
}

class _MyNotifiedComponentState extends State<MyNotifiedComponent> {
  final _cubit = Get.find<MyNotifiedCubit>();

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await this._cubit.execute();
    });
  }

  @override
  void dispose() {
    Get.delete<MyNotifiedCubit>(force: true);

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final bool talkBackOn = WidgetsBinding
        .instance.platformDispatcher.accessibilityFeatures.accessibleNavigation;

    return CustomBaseScaffold(
      // coverage:ignore-start
      popScope: (bool value, dynamic result) => exit(0),
      // coverage:ignore-end
      appBar: AppBar(
        automaticallyImplyLeading: false,
        title: Text("Notificados"),
      ),
      appBarBottom: Container(
        padding: EdgeInsets.symmetric(horizontal: 6.r),
        child: BlocBuilder<MyNotifiedCubit, MyNotifiedState>(
          bloc: Get.find<MyNotifiedCubit>(),
          builder: (BuildContext context, MyNotifiedState state) {
            if (state.citizenDataModel == null) return const SizedBox.shrink();

            final content = Row(
              children: <Widget>[
                Expanded(
                  child: Text(
                    semanticsLabel: '',
                    "Habilitar notificações WhatsApp",
                    style: TextStyle(
                      fontSize: FontSizeManager.s16,
                      fontWeight: FontWeight.w600,
                      color: ColorManager.whiteColor,
                    ),
                  ),
                ),
                Transform.scale(
                  scale: 0.75,
                  child: Switch(
                    activeTrackColor: ColorManager.secondaryColor,
                    inactiveTrackColor: ColorManager.borderColor,
                    value: state.whatsappNotificationIsEnabled,
                    onChanged: (bool value) {
                      Get.find<MyNotifiedCubit>()
                          .setCitizenNotificationViewModel(value);
                    },
                  ),
                ),
              ],
            );

            if (talkBackOn) {
              return Semantics(
                label:
                    'Botão. As notificações do WhatsApp estão. ${state.whatsappNotificationIsEnabled ? 'Ativadas.' : 'Desativadas.'}',
                hint:
                    'Toque duas vezes para ${state.whatsappNotificationIsEnabled ? 'desativar' : 'ativar'}',
                child: InkWell(
                  onTap: () async {
                    final currentState = !state.whatsappNotificationIsEnabled;
                    Get.find<MyNotifiedCubit>()
                        .setCitizenNotificationViewModel(currentState);
                    await AccessibilityUtils.announce(
                      'Notificações ${currentState ? 'ativadas' : 'desativadas'}',
                    );
                  },
                  child: Container(
                    width: context.width,
                    padding: PaddingManager.symmetricHorizontalMedium,
                    child: content,
                  ),
                ),
              );
            }

            return Container(
              width: context.width,
              padding: PaddingManager.symmetricHorizontalMedium,
              child: content,
            );
          },
        ),
      ),
      bottomNavigationBar: this.widget._bottomNavigationBar,
      body: BlocBuilder<MyNotifiedCubit, MyNotifiedState>(
        bloc: Get.find<MyNotifiedCubit>(),
        builder: (BuildContext context, MyNotifiedState state) {
          return this._buildContent(context, state);
        },
      ),
      resizeToAvoidBottomInset: false,
    );
  }

  Widget _buildContent(BuildContext context, MyNotifiedState state) {
    return this._buildProcessList(context, state);
  }

  Widget _buildProcessList(BuildContext context, MyNotifiedState state) {
    if (state.lawyerModel != null) {
      initCitizenLoginModule();
      return LawyerInsideNotificationLogoutDialog();
    }

    if (state.citizenDataModel == null) {
      initCitizenLoginModule();
      return CitizenLoginDialog();
    }

    if ((state.processFlaggedCitizenDataModelList?.isEmpty ?? true) &&
        (state.processDataNotifiedModelList?.isEmpty ?? true)) {
      return NotContentFoundComponent(typeMessage: 'processo notificado');
    }

    final isLoading = state.processDataNotifiedModelList == null ||
        state.processDataNotifiedModelList!.length <
            (state.processFlaggedCitizenDataModelList?.length ?? 0);

    return ListView.separated(
      shrinkWrap: true,
      itemCount: isLoading
          ? (state.processFlaggedCitizenDataModelList?.length ?? 0)
          : (state.processDataNotifiedModelList?.length ?? 0),
      itemBuilder: (BuildContext context, int index) {
        if (isLoading &&
            index >= (state.processDataNotifiedModelList?.length ?? 0)) {
          return ShimmerCardComponent(count: 1);
        }

        ProcessDataModel? item = state.processDataNotifiedModelList?[index];
        if (item == null) return const SizedBox.shrink();

        return ProcessCardComponent(
          processDataModel: item,
          citizenDataModel: state.citizenDataModel,
          showFavoriteButton: false,
        );
      },
      separatorBuilder: (BuildContext context, int index) {
        return SizedBox(height: 8.r);
      },
    );
  }
}
