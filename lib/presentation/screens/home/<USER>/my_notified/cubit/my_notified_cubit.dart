import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';
import 'package:tjcemobile/app/app_preferences.dart';
import 'package:tjcemobile/app/shared/utils/snackbar_utils.dart';
import 'package:tjcemobile/data/model/citizen_model.dart';
import 'package:tjcemobile/data/model/lawyer_model.dart';
import 'package:tjcemobile/data/model/process_flagged_citizen_model.dart';
import 'package:tjcemobile/data/model/process_model.dart';
import 'package:tjcemobile/data/network/error_handler.dart';
import 'package:tjcemobile/data/network/failure.dart';
import 'package:tjcemobile/domain/usecase/home/<USER>';
import 'package:tjcemobile/domain/viewmodel/citizen_notification_viewmodel.dart';
import 'package:tjcemobile/domain/viewmodel/process_viewmodel.dart';
import 'package:tjcemobile/presentation/screens/citizen/login/cubit/citizen_login_cubit.dart';

part 'my_notified_state.dart';

class MyNotifiedCubit extends Cubit<MyNotifiedState> {
  final HomeUseCase _useCase;
  final AppPreferences _appPreferences = Get.find<AppPreferences>();

  MyNotifiedCubit(this._useCase) : super(MyNotifiedState());

  Future<void> execute() async {
    emit(MyNotifiedState());

    await this._getLawyerModel();

    if (state.lawyerModel != null) return;

    try {
      await Future.wait([
        this._verifyPushWhatsappStatus(),
        this._getCitizenDataModel(),
      ]);
    } catch (e) {
      emit(state.copyWith(contentState: ContentState.ERROR));
    }
  }

  Future<void> _getLawyerModel() async {
    Map<String, dynamic> lawyerMap =
        await this._appPreferences.getLawyerAuthenticate();

    LawyerModel lawyerModel = LawyerModel.fromJson(lawyerMap);

    if (lawyerModel.data != null) {
      emit(state.copyWith(lawyerModel: lawyerModel));
    }
  }

  void clearList() {
    emit(state.copyWith(processDataNotifiedModelList: null));
  }

  Future<void> _getProcessDataModel({bool? toggleNotified}) async {
    await (await this._useCase.getProcessDataModel()).fold(
        (Failure failure) async {
      emit(state.copyWith(contentState: ContentState.ERROR));
      //
    }, (List<ProcessDataModel> processDataModelList) async {
      processDataModelList = processDataModelList
          .where((item) => item.isFlaggedPushWhatsapp)
          .toList();

      if (state.processFlaggedCitizenDataModelList == null) {
        emit(state.copyWith(
          processDataNotifiedModelList: processDataModelList,
        ));

        SnackbarUtils.information(
          "Você está visualizando as informações dos processos armazenados localmente.",
        );

        return;
      }

      await Future.wait(state.processFlaggedCitizenDataModelList!
          .map((flaggedProcessDataModel) async {
        var citizenProcessId = flaggedProcessDataModel.id ?? -1;
        var statusPushWhatsapp =
            toggleNotified ?? flaggedProcessDataModel.status;

        var remoteDataModel = await this._getProcessRemote(
          flaggedProcessDataModel.numeroProcesso?.numericOnly() ?? "",
          flaggedProcessDataModel.siglaSistema ?? "",
          citizenProcessId: citizenProcessId,
        );

        if (remoteDataModel == null) return;

        var list = List<ProcessDataModel>.from(
          state.processDataNotifiedModelList ?? [],
        );
        list.add(remoteDataModel);

        emit(state.copyWith(
          processDataNotifiedModelList: list,
        ));

        var localDataModel =
            await this._getProcessLocal(remoteDataModel.id ?? "");

        remoteDataModel.idUsuarioProcesso = citizenProcessId;
        remoteDataModel.statusPushWhatsapp = statusPushWhatsapp ?? true;

        remoteDataModel.isFlaggedPushWhatsapp = true;

        if (localDataModel != null) {
          remoteDataModel.isFavorite = localDataModel.isFavorite;
        }

        await this._useCase.setProcessDataModel(remoteDataModel);
      }));
    });
  }

  Future<void> removeProcessFromList(ProcessDataModel dataModel) async {
    final updatedNotifiedList = (state.processDataNotifiedModelList ?? [])
        .where((item) => item.id != dataModel.id)
        .toList();

    final updatedFlaggedList = (state.processFlaggedCitizenDataModelList ?? [])
        .where((item) => item.idProcesso != dataModel.id)
        .toList();

    bool isDeleted =
        await this._deleteProcessFromCitizen(dataModel.idUsuarioProcesso);

    if (!isDeleted) return;

    emit(state.copyWith(
      contentState: ContentState.SUCCESS,
      processDataNotifiedModelList: updatedNotifiedList,
      processFlaggedCitizenDataModelList: updatedFlaggedList,
    ));

    if (updatedNotifiedList.isEmpty) {
      this.clearList();
    }
  }

  Future<bool> _deleteProcessFromCitizen(int? citizenProcessId) async {
    return await (await this
            ._useCase
            .deleteProcessFromCitizen(citizenProcessId ?? -1))
        .fold((_) => false, (_) => true);
  }

  Future<ProcessDataModel?> _getProcessRemote(
    String numberProcess,
    String acronymSystem, {
    int? citizenProcessId,
  }) async {
    ProcessViewModel viewModel = ProcessViewModel(numberProcess);

    return await (await this._useCase.getProcessNumber(viewModel)).fold(
        (Failure failure) async {
      await this._deleteProcessFromCitizen(citizenProcessId);

      return;
    }, (ProcessModel processModel) async {
      List<ProcessDataModel>? list = processModel.data;

      if (list == null || list.length == 0) return null;

      return list
          .where((item) =>
              item.numeroFormatado?.numericOnly() == numberProcess &&
              item.sistema == acronymSystem &&
              item.flagSegredoJustica != 'S')
          .singleOrNull;
    });
  }

  Future<ProcessDataModel?> _getProcessLocal(String idProcess) async {
    return await (await this._useCase.getProcessDataModelById(idProcess)).fold(
      (Failure failure) async => null,
      (ProcessDataModel value) async => value,
    );
  }

  Future<void> _getProcessesByCitizenId(int citizenId) async {
    await (await this._useCase.getProcessesByCitizenId(citizenId)).fold(
        (Failure failure) {
      if (failure.code == ResponseCode.BAD_REQUEST) {
        Get.find<CitizenLoginCubit>().removeCitizenLogged();
      }
    }, (ProcessFlaggedCitizenModel processFlaggedCitizenModel) async {
      var list = processFlaggedCitizenModel.data ?? [];

      emit(state.copyWith(processFlaggedCitizenDataModelList: list));
    });

    await this._getProcessDataModel();
  }

  Future<void> _getCitizenDataModel() async {
    await (await this._useCase.getCitizenDataModel()).fold(
        (Failure failure) async {
      //
    }, (CitizenDataModel citizenDataModel) async {
      emit(state.copyWith(
        citizenDataModel: citizenDataModel,
        contentState: ContentState.LOADING,
      ));

      if (state.citizenDataModel?.id == null) return;

      await this._getProcessesByCitizenId(state.citizenDataModel!.id ?? -1);
    });
  }

  Future<void> setCitizenNotificationViewModel(bool whatsappIsEnabled) async {
    CitizenNotificationViewModel viewModel = CitizenNotificationViewModel(
      enableWhatsapp: whatsappIsEnabled,
    );

    await (await this._useCase.setCitizenNotificationViewModel(viewModel)).fold(
      (Failure failure) async {
        emit(state.copyWith(contentState: ContentState.ERROR));
      },
      (void _) async => null,
    );

    await this._verifyPushWhatsappStatus(requestAPI: true);
  }

  Future<void> _verifyPushWhatsappStatus({bool? requestAPI}) async {
    await (await this._useCase.getCitizenNotificationViewModelList()).fold(
        (Failure failure) async {
      //
    }, (List<CitizenNotificationViewModel> list) async {
      CitizenNotificationViewModel? viewModel = list.singleOrNull;

      var whatsappNotificationIsEnabled = viewModel?.enableWhatsapp ?? true;

      emit(state.copyWith(
        whatsappNotificationIsEnabled: viewModel?.enableWhatsapp ?? true,
      ));

      var processList = List<ProcessDataModel>.from(
        state.processDataNotifiedModelList ?? [],
      );

      processList.forEach(
        (item) => item.statusPushWhatsapp = whatsappNotificationIsEnabled,
      );

      emit(state.copyWith(
        whatsappNotificationIsEnabled: whatsappNotificationIsEnabled,
        processDataNotifiedModelList: processList,
      ));

      if (requestAPI == null) return;

      await this._switchProcessesNotified(state.whatsappNotificationIsEnabled);
    });
  }

  Future<void> _switchProcessesNotified(bool isEnabled) async {
    if (!isEnabled) {
      await this._getDisableNotificationProcessFromCitizen(
        state.citizenDataModel?.id ?? -1,
      );

      return;
    }

    await this._getEnableNotificationProcessFromCitizen(
      state.citizenDataModel?.id ?? -1,
    );
  }

  Future<void> _getEnableNotificationProcessFromCitizen(
    int citizenProcessId,
  ) async {
    await (await this
            ._useCase
            .getEnableNotificationProcessFromCitizen(citizenProcessId))
        .fold((_) => null, (_) => null);
  }

  Future<void> _getDisableNotificationProcessFromCitizen(
    int citizenProcessId,
  ) async {
    await (await this
            ._useCase
            .getDisableNotificationProcessFromCitizen(citizenProcessId))
        .fold((_) => null, (_) => null);
  }
}
