part of 'my_notified_cubit.dart';

enum ContentState { DEFAULT, LOADING, SUCCESS, ERROR }

enum OptionsSegment { PROCESS, ACTUATION }

class MyNotifiedState extends Equatable {
  final ContentState contentState;
  final bool whatsappNotificationIsEnabled;
  final List<ProcessFlaggedCitizenDataModel>?
      processFlaggedCitizenDataModelList;
  final List<ProcessDataModel>? processDataNotifiedModelList;
  final CitizenDataModel? citizenDataModel;
  final LawyerModel? lawyerModel;

  const MyNotifiedState({
    this.contentState = ContentState.DEFAULT,
    this.whatsappNotificationIsEnabled = false,
    this.processFlaggedCitizenDataModelList,
    this.processDataNotifiedModelList,
    this.citizenDataModel,
    this.lawyerModel,
  });

  MyNotifiedState copyWith({
    ContentState? contentState,
    bool? whatsappNotificationIsEnabled,
    List<ProcessFlaggedCitizenDataModel>? processFlaggedCitizenDataModelList,
    List<ProcessDataModel>? processDataNotifiedModelList,
    CitizenDataModel? citizenDataModel,
    LawyerModel? lawyerModel,
  }) {
    return MyNotifiedState(
      whatsappNotificationIsEnabled:
          whatsappNotificationIsEnabled ?? this.whatsappNotificationIsEnabled,
      contentState: contentState ?? this.contentState,
      processFlaggedCitizenDataModelList: processFlaggedCitizenDataModelList ??
          this.processFlaggedCitizenDataModelList,
      processDataNotifiedModelList:
          processDataNotifiedModelList ?? this.processDataNotifiedModelList,
      citizenDataModel: citizenDataModel ?? this.citizenDataModel,
      lawyerModel: lawyerModel,
    );
  }

  @override
  List<Object?> get props => [
        whatsappNotificationIsEnabled,
        processFlaggedCitizenDataModelList,
        processDataNotifiedModelList,
        citizenDataModel,
        lawyerModel,
      ];
}
