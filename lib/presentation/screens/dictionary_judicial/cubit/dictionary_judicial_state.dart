part of 'dictionary_judicial_cubit.dart';

enum DicitionaryContentState { DEFAULT, LOADING, SUCCESS, ERROR }

class DictionaryJudicialState extends Equatable {
  final DicitionaryContentState dicitionaryContentState;
  final List<LocalVideosDictionaryJudicialDataModel>?
      localVideosDictionaryJudicialDataModel;
  final List<LocalVideosDictionaryJudicialDataModel>? filtedList;
  final bool? noResultFound;
  final Failure? failure;

  const DictionaryJudicialState({
    this.dicitionaryContentState = DicitionaryContentState.DEFAULT,
    this.localVideosDictionaryJudicialDataModel,
    this.filtedList,
    this.noResultFound,
    this.failure,
  });

  DictionaryJudicialState copyWith({
    DicitionaryContentState? dictionaryContentState,
    List<LocalVideosDictionaryJudicialDataModel>?
        localVideosDictionaryJudicialDataModel,
    Failure? failure,
    List<LocalVideosDictionaryJudicialDataModel>? filtedList,
    bool? noResultFound,
  }) {
    return DictionaryJudicialState(
        dicitionaryContentState:
            dictionaryContentState ?? this.dicitionaryContentState,
        localVideosDictionaryJudicialDataModel:
            this.localVideosDictionaryJudicialDataModel ??
                localVideosDictionaryJudicialDataModel,
        filtedList: filtedList ?? this.filtedList,
        failure: failure ?? this.failure,
        noResultFound: noResultFound ?? this.noResultFound);
  }

  @override
  List<Object?> get props => [
        dicitionaryContentState,
        localVideosDictionaryJudicialDataModel,
        filtedList,
        failure,
        noResultFound,
      ];
}
