import 'dart:convert';

import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';
import 'package:tjcemobile/data/model/local_videos_dictionary_judicial_model.dart';
import 'package:tjcemobile/data/network/failure.dart';
import 'package:tjcemobile/presentation/widgets/youtube_player_component.dart';

part 'dictionary_judicial_state.dart';

class DictionaryJudicialCubit extends Cubit<DictionaryJudicialState> {
  DictionaryJudicialCubit() : super(DictionaryJudicialState(filtedList: []));

  Future<void> getLocalJsonDictionary() async {
    var response = await rootBundle
        .loadString('assets/data/local_dictionary_judicial_videos.json');
    Map<String, dynamic> data = await json.decode(response);
    var supportContent = LocalVideosDictionaryJudicialModel.fromJson(data);

    emit(state.copyWith(
      localVideosDictionaryJudicialDataModel: supportContent.data,
      filtedList: supportContent.data,
    ));
  }

  Future<void> searchCardSupportList(
    String? value,
  ) async {
    List<LocalVideosDictionaryJudicialDataModel> listFilter = [];
    if (value != null && value.isNotEmpty) {
      listFilter = state.localVideosDictionaryJudicialDataModel!
          .where((element) =>
              element.title!.toUpperCase().contains(value.toUpperCase()))
          .toList();
    } else {
      listFilter = state.localVideosDictionaryJudicialDataModel ?? [];
    }
    if (listFilter.isEmpty) {
      listFilter = [];
      emit(state.copyWith(filtedList: listFilter, noResultFound: true));
    } else {
      emit(state.copyWith(filtedList: listFilter, noResultFound: false));
    }
    // emit(state.copyWith(filtedList: listFilter));
  }

  Future<void> getVideoFromMovement({
    String? value,
    bool isProcessRoute = false,
  }) async {
    var matchedValue = state.localVideosDictionaryJudicialDataModel?.firstWhere(
        (entry) => entry.match?.toUpperCase() == value?.toUpperCase());

    await Get.dialog(
      SizedBox(
        height: Get.height * 0.8,
        child: YoutubePlayerComponent(
          title: matchedValue?.title ?? "-",
          videoId: matchedValue?.url ?? "jWTOVpt7Ag8",
          // matchedValue?.url ??
        ),
      ),
    );

    if (isProcessRoute) Get.back();
  }
}
