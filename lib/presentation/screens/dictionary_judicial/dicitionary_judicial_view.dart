import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:tjcemobile/presentation/resources/font_manager.dart';
import 'package:tjcemobile/presentation/screens/dictionary_judicial/cubit/dictionary_judicial_cubit.dart';
import 'package:tjcemobile/presentation/widgets/arrow_back_component.dart';
import 'package:tjcemobile/presentation/widgets/custom_base_scaffold.dart';
import 'package:tjcemobile/presentation/widgets/custom_text_form_field_secondary.dart';
import 'package:tjcemobile/presentation/widgets/list_tile_card_component.dart';
import 'package:tjcemobile/presentation/widgets/youtube_player_component.dart';

class DictionaryJudicialView extends StatefulWidget {
  final String? _movementCase;
  final bool? _isProcessRoute;

  const DictionaryJudicialView({
    String? movementCase,
    bool? isProcessRoute,
    Key? key,
  })  : this._movementCase = movementCase,
        this._isProcessRoute = isProcessRoute,
        super(key: key);

  @override
  State<DictionaryJudicialView> createState() => _DictionaryJudicialViewState();
}

class _DictionaryJudicialViewState extends State<DictionaryJudicialView> {
  final TextEditingController _searchController = TextEditingController();
  final _cubit = Get.find<DictionaryJudicialCubit>();

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await this._cubit.getLocalJsonDictionary();

      if (this.widget._movementCase != null) {
        await this._cubit.getVideoFromMovement(
              value: this.widget._movementCase,
              isProcessRoute: this.widget._isProcessRoute ?? false,
            );
      }
    });
  }

  @override
  Widget build(
    BuildContext context,
  ) {
    return CustomBaseScaffold(
      appBar: this._buildAppBar(),
      body: this._buildBody(),
    );
  }

  Widget _buildBody() {
    return Column(
      children: [
        CustomTextFormFieldSecondary(
          controller: this._searchController,
          inputAction: TextInputAction.search,
          hintText: "Pesquise o termo...",
          suffix: IconButton(
            icon: Icon(Icons.search, size: 24.sp),
            onPressed: () async {
              String search = this._searchController.text;

              if (this._searchController.text.isNotEmpty) {
                Get.focusScope?.unfocus();

                if (!RegExp(r'^[\d.-]+$').hasMatch(search)) {
                  await this._cubit.searchCardSupportList(search);
                }
              }
            },
          ),
          onChanged: (_) async {
            String search = this._searchController.text;

            await this._cubit.searchCardSupportList(search);
          },
        ),
        SizedBox(height: 12.r),
        Expanded(
          child: BlocBuilder(
            bloc: Get.find<DictionaryJudicialCubit>(),
            builder: (BuildContext context, DictionaryJudicialState state) {
              if (state.noResultFound == true) {
                return Center(
                  child: Text(
                    "Nenhum dado encontrado",
                    style: TextStyle(fontSize: FontSizeManager.s14),
                  ),
                );
              } else {
                return ListView.separated(
                  shrinkWrap: true,
                  itemCount: state.filtedList?.length ?? 0,
                  itemBuilder: (BuildContext context, int index) {
                    var item = state.filtedList?[index];
                    return ListTileCardComponent(
                      title: item?.title ?? "-",
                      subTitle: item?.subTitle ?? "-",
                      height: 85.r,
                      imageName: "mediaDictionaryVideos/${item?.match}.png",
                      sizeIcon: 32.sp,
                      onTap: () async {
                        await Get.dialog(
                          SizedBox(
                            height: Get.height * 0.8,
                            child: YoutubePlayerComponent(
                              title: item?.title ?? "-",
                              videoId: item?.url ?? "jWTOVpt7Ag8",
                              // item?.url ?? "-",
                            ),
                          ),
                        );
                      },
                    );
                  },
                  separatorBuilder: (BuildContext context, int index) {
                    return SizedBox(height: 8.r);
                  },
                );
              }
            },
          ),
        ),
        SizedBox(height: 8.r),
      ],
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text("Dicionário Jurídico"),
      leading: ArrowBackComponent(onPressed: () => Get.back()),
    );
  }
}
