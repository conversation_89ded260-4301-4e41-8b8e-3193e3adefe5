import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:tjcemobile/domain/viewmodel/conciliation_form_viewmodel.dart';
import 'package:tjcemobile/presentation/resources/color_manager.dart';
import 'package:tjcemobile/presentation/resources/padding_manager.dart';
import 'package:tjcemobile/presentation/resources/routes_manager.dart';
import 'package:tjcemobile/presentation/screens/conciliation/screens/solicitations/cubit/conciliation_solicitations_cubit.dart';

class ConciliationInProgressComponent extends StatelessWidget {
  final ConciliationFormViewModel _conciliationFormViewModel;

  ConciliationInProgressComponent({
    required ConciliationFormViewModel conciliationFormViewModel,
    Key? key,
  })  : this._conciliationFormViewModel = conciliationFormViewModel,
        super(key: key);

  final _cubit = Get.find<ConciliationSolicitationsCubit>();

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: AlertDialog(
        title: Stack(
          children: [
            Container(
              padding: PaddingManager.large,
              child: Column(
                children: <Widget>[
                  Icon(
                    Icons.mobile_friendly_rounded,
                    size: 24.sp,
                    color: ColorManager.secondaryColor,
                  ),
                  Text(
                    "Solicitação em andamento",
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
            Positioned(
              right: 0.0,
              child: GestureDetector(
                onTap: () async => await Get.toNamed(Routes.homeRoute),
                child: Align(
                  alignment: Alignment.topRight,
                  child: Icon(
                    Icons.close,
                    color: ColorManager.primaryColor,
                  ),
                ),
              ),
            ),
          ],
        ),
        content: SizedBox(
          width: 360.r,
          child: Container(
            padding: PaddingManager.large,
            child: Text(
              'Existe uma solicitação em andamento. Deseja continuar o '
              'preenchimento ou iniciar uma nova solicitação?',
              textAlign: TextAlign.justify,
            ),
          ),
        ),
        actions: <Widget>[
          TextButton(
            child: Text('Nova'),
            // coverage:ignore-start
            onPressed: () async {
              await this._cubit.removeConciliationFormViewModel();
            },
            // coverage:ignore-end
          ),
          TextButton(
            child: Text('Continuar'),
            // coverage:ignore-start
            onPressed: () async {
              await Get.offNamed(
                Routes.conciliationFormRoute,
                arguments: {
                  'formViewModel': this._conciliationFormViewModel,
                },
              );
            },
            // coverage:ignore-end
          ),
        ],
      ),
    );
  }
}
