import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';
import 'package:tjcemobile/app/shared/utils/snackbar_utils.dart';
import 'package:tjcemobile/data/model/conciliation_response_model.dart';
import 'package:tjcemobile/data/network/failure.dart';
import 'package:tjcemobile/domain/usecase/conciliation/conciliation_usecase.dart';
import 'package:tjcemobile/domain/viewmodel/conciliation_form_viewmodel.dart';
import 'package:tjcemobile/presentation/resources/routes_manager.dart';

part 'conciliation_solicitations_state.dart';

class ConciliationSolicitationsCubit
    extends Cubit<ConciliationSolicitationsState> {
  final ConciliationUseCase _useCase;

  ConciliationSolicitationsCubit(this._useCase)
      : super(ConciliationSolicitationsState());

  Future<void> execute() async {
    emit(state.copyWith(
      contentState: ContentState.LOADING,
      dataModelList: null,
    ));

    await (await _useCase.getConciliationResponseModelList()).fold(
      (Failure failure) {
        _handleFailure(failure);
      },
      (List<ConciliationResponseModel> conciliationResponseModelList) async {
        for (var item in conciliationResponseModelList) {
          String? solicitation = item.data?.dataSolicitacao;

          if (solicitation != null) {
            DateTime solicitationDate = _parseDateFromString(solicitation);

            int diffDays = DateTime.now().difference(solicitationDate).inDays;

            if (diffDays > 180) {
              await _useCase
                  .removeConciliationResponseModelByUuid(item.uuid.toString());
            }
          }
        }

        emit(state.copyWith(
          contentState: ContentState.SUCCESS,
          dataModelList: conciliationResponseModelList.reversed.toList(),
        ));
      },
    );
  }

  Future<void> reSendConciliation(
    String uuid,
    ConciliationResponseDataModel dataModel,
  ) async {
    if (state.dataModelList == null) return;

    state.dataModelList?.forEach((element) {
      if (element.uuid == uuid) {
        element.data?.isLoading = true;
      }
    });

    emit(state.copyWith(dataModelList: state.dataModelList));

    await Get.forceAppUpdate();
    await _postConciliationForm(
      uuid,
      dataModel,
    );

    state.dataModelList?.forEach((element) {
      if (element.data?.processo == dataModel.processo) {
        element.data?.isLoading = false;
      }
    });

    emit(state.copyWith(dataModelList: state.dataModelList));

    await Get.forceAppUpdate();
  }

  Future<void> _postConciliationForm(
    String uuid,
    ConciliationResponseDataModel dataModel,
  ) async {
    final ConciliationFormViewModel viewModel = ConciliationFormViewModel(
      processo: dataModel.processo,
      unidade: dataModel.unidade,
      orgaoJulgador: dataModel.orgaoJulgador,
      parte: dataModel.parte,
      solicitante: dataModel.solicitante,
      oab: dataModel.oab,
      emailParte: dataModel.emailParte,
      emailVara: dataModel.emailVara,
      emailConciliar: dataModel.emailConciliar,
      notificaWhatsapp: dataModel.notificaWhatsapp,
      whatsapp: dataModel.whatsapp,
      observacao: dataModel.observacao,
      reenvio: true,
    );

    (await _useCase.postConciliationForm(viewModel)).fold(
      (Failure failure) {
        _handleFailure(failure);
      },
      (_) async => null,
    );
  }

  DateTime _parseDateFromString(String dateString) {
    List<String> dateParts = dateString.split('/');
    int day = int.parse(dateParts[0]);
    int month = int.parse(dateParts[1]);
    int year = int.parse(dateParts[2]);

    return DateTime(year, month, day);
  }

  void _handleFailure(Failure failure) {
    emit(state.copyWith(
      contentState: ContentState.ERROR,
      failure: failure,
    ));
    SnackbarUtils.error(failure.title, failure.message ?? '');
  }

  Future<void> removeConciliationFormViewModel() async {
    (await this._useCase.removeConciliationFormViewModel())
        .fold((Failure failure) => null, (void _) async {
      Get.back();
    });
  }

  Future<void> checkForSavedForm() async {
    (await _useCase.getConciliationFormModelList()).fold((Failure failure) {},
        (List<ConciliationFormViewModel> list) async {
      if (list.isEmpty) {
        await Get.toNamed(Routes.conciliationProcessRoute);

        return;
      }
    });
  }
}
