part of 'conciliation_solicitations_cubit.dart';

enum ContentState { DEFAULT, LOADING, SUCCESS, ERROR }

class ConciliationSolicitationsState extends Equatable {
  final ContentState contentState;
  final List<ConciliationResponseModel>? dataModelList;
  final Failure? failure;

  const ConciliationSolicitationsState({
    this.contentState = ContentState.DEFAULT,
    this.dataModelList,
    this.failure,
  });

  ConciliationSolicitationsState copyWith({
    ContentState? contentState,
    List<ConciliationResponseModel>? dataModelList,
    Failure? failure,
  }) {
    return ConciliationSolicitationsState(
      contentState: contentState ?? this.contentState,
      dataModelList: dataModelList ?? this.dataModelList,
      failure: failure ?? failure,
    );
  }

  @override
  List<Object?> get props => [
        contentState,
        dataModelList,
        failure,
      ];
}
