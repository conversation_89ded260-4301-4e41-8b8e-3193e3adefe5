import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:tjcemobile/data/model/conciliation_response_model.dart';
import 'package:tjcemobile/presentation/resources/padding_manager.dart';
import 'package:tjcemobile/presentation/screens/conciliation/components/conciliation_card_component.dart';
import 'package:tjcemobile/presentation/screens/conciliation/cubit/conciliation_cubit.dart';
import 'package:tjcemobile/presentation/screens/conciliation/screens/solicitations/cubit/conciliation_solicitations_cubit.dart';
import 'package:tjcemobile/presentation/widgets/arrow_back_component.dart';
import 'package:tjcemobile/presentation/widgets/custom_base_scaffold.dart';

class ConciliationSolicitationsView extends StatefulWidget {
  const ConciliationSolicitationsView({Key? key}) : super(key: key);

  @override
  State<ConciliationSolicitationsView> createState() =>
      _ConciliationSolicitationsViewState();
}

class _ConciliationSolicitationsViewState
    extends State<ConciliationSolicitationsView> {
  final _cubit = Get.find<ConciliationSolicitationsCubit>();

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await this._cubit.execute();
    });
  }

  @override
  void dispose() {
    Get.delete<ConciliationSolicitationsCubit>(force: true);

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return CustomBaseScaffold(
      appBar: this._buildAppBar(),
      body: this._buildBody(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text("Minhas solicitações"),
      leading: ArrowBackComponent(
        onPressed: () => Get.back(),
      ),
    );
  }

  Widget _buildBody() {
    return Container(
      width: context.width,
      padding: PaddingManager.symmetricHorizontaSmall,
      child: BlocBuilder<ConciliationSolicitationsCubit,
          ConciliationSolicitationsState>(
        bloc: Get.find<ConciliationSolicitationsCubit>(),
        builder: (BuildContext context, ConciliationSolicitationsState state) {
          if (state.contentState == ConciliationContentState.LOADING) {
            return Center(
              child: CircularProgressIndicator(),
            );
          }

          if (state.dataModelList == null || state.dataModelList!.isEmpty) {
            return Center(
              child: Text(
                'Nenhuma solicitação encontrada',
                style: TextStyle(
                  color: context.theme.colorScheme.onSurfaceVariant,
                ),
              ),
            );
          }

          return ListView.separated(
            shrinkWrap: true,
            padding: EdgeInsets.zero,
            itemCount: state.dataModelList?.length ?? 0,
            itemBuilder: (BuildContext context, int index) {
              ConciliationResponseModel? item = state.dataModelList?[index];

              if (item?.data == null) return const SizedBox.shrink();

              return ConciliationCardComponent(
                uuid: item!.uuid!,
                solicitationJson: item.data!,
              );
            },
            separatorBuilder: (BuildContext context, int index) {
              return SizedBox(height: 8.r);
            },
          );
        },
      ),
    );
  }
}
