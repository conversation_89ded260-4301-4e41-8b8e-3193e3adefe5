import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:tjcemobile/app/shared/utils/device_type_utils.dart';
import 'package:tjcemobile/data/network/error_handler.dart';
import 'package:tjcemobile/data/network/failure.dart';
import 'package:tjcemobile/presentation/resources/border_manager.dart';
import 'package:tjcemobile/presentation/resources/color_manager.dart';
import 'package:tjcemobile/presentation/resources/font_manager.dart';
import 'package:tjcemobile/presentation/resources/padding_manager.dart';
import 'package:tjcemobile/presentation/widgets/arrow_back_component.dart';

class ErrorView extends StatefulWidget {
  final Failure failure;

  const ErrorView({required this.failure, Key? key}) : super(key: key);

  @override
  State<ErrorView> createState() => _ErrorViewState();
}

class _ErrorViewState extends State<ErrorView> {
  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: ArrowBackComponent(
          // coverage:ignore-start
          onPressed: () => Get.back(),
          // coverage:ignore-end
        ),
        titleSpacing: 0.0,
        title: Text(
          'Whoops, looks like something went wrong.',
          style: TextStyle(
            color: context.theme.colorScheme.onPrimary,
            fontSize: FontSizeManager.s14,
            fontWeight: FontWeightManager.medium,
          ),
        ),
        backgroundColor: HexColor.fromHex('#b0413e'),
      ),
      backgroundColor: HexColor.fromHex('#f9f9f9'),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          SizedBox(height: 16.r),
          Container(
            width: context.width,
            color: HexColor.fromHex('#e0e0e0'),
            padding: PaddingManager.symmetricHorizontalMedium,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                SizedBox(height: 16.r),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: <Widget>[
                    Text.rich(
                      TextSpan(
                        children: <TextSpan>[
                          const TextSpan(
                            text: "Screen:",
                            style: TextStyle(
                              fontWeight: FontWeightManager.medium,
                            ),
                          ),
                          const TextSpan(text: " "),
                          TextSpan(text: Get.previousRoute),
                        ],
                        style: TextStyle(fontSize: FontSizeManager.s12),
                      ),
                      textAlign: TextAlign.center,
                    ),
                    Text.rich(
                      TextSpan(
                        children: <TextSpan>[
                          const TextSpan(
                            text: "Error code:",
                            style: TextStyle(
                              fontWeight: FontWeightManager.medium,
                            ),
                          ),
                          const TextSpan(text: " "),
                          TextSpan(text: widget.failure.code.toString()),
                        ],
                        style: TextStyle(fontSize: FontSizeManager.s12),
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
                Text(
                  widget.failure.title,
                  // textAlign: TextAlign.justify,
                  style: TextStyle(
                    fontSize: FontSizeManager.s12,
                    fontWeight: FontWeightManager.medium,
                  ),
                ),
                SizedBox(height: 16.r),
              ],
            ),
          ),
          if (widget.failure.message != null &&
              widget.failure.message!.isNotEmpty) ...[
            SizedBox(height: 16.r),
            Expanded(
              child: Container(
                padding: PaddingManager.symmetricHorizontalMedium,
                child: Card(
                  child: Scrollbar(
                    child: Container(
                      padding:
                          DeviceTypeUtils.current == DeviceTypeManager.TABLET
                              ? PaddingManager.large
                              : PaddingManager.medium,
                      decoration: BoxDecoration(
                        color: ColorManager.whiteColor,
                        borderRadius: BorderManager.medium,
                        border: Border.all(
                          color: context.theme.colorScheme.outlineVariant,
                        ),
                      ),
                      child: ListView(
                        children: <Widget>[
                          Text(
                            widget.failure.message ?? ResponseMessage.DEFAULT,
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontSize: FontSizeManager.s12,
                              fontFamily: 'Courier',
                              fontWeight: FontWeightManager.semiBold,
                              height: 1.5,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
            SizedBox(height: 16.r),
          ]
        ],
      ),
    );
  }
}
