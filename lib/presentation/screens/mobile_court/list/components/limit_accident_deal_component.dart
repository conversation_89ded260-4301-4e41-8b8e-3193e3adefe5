import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:tjcemobile/presentation/resources/color_manager.dart';
import 'package:tjcemobile/presentation/resources/font_manager.dart';
import 'package:tjcemobile/presentation/resources/padding_manager.dart';

class LimitAccidentDealComponent extends StatelessWidget {
  LimitAccidentDealComponent();

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Container(
        padding: PaddingManager.large,
        child: Column(
          children: <Widget>[
            Icon(
              Icons.cancel_outlined,
              size: 34.sp,
              color: ColorManager.secondaryColor,
            ),
            SizedBox(height: 8.r),
            Text(
              "Atenção!",
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
      content: Text.rich(
        TextSpan(
          children: <TextSpan>[
            TextSpan(
                style: TextStyle(fontSize: FontSizeManager.s14),
                text: "Foi atingido o limite máximo de acordos por dia."),
          ],
        ),
        textAlign: TextAlign.justify,
      ),
      actions: <Widget>[
        TextButton(
          child: Text(
            "OK",
            style: TextStyle(fontSize: FontSizeManager.s14),
          ),
          onPressed: () async {
            Get.back();
          },
        ),
      ],
    );
  }
}
