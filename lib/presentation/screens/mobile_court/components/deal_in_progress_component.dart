import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:tjcemobile/domain/viewmodel/accident_deal_form_viewmodel.dart';
import 'package:tjcemobile/presentation/resources/color_manager.dart';
import 'package:tjcemobile/presentation/resources/padding_manager.dart';
import 'package:tjcemobile/presentation/resources/routes_manager.dart';
import 'package:tjcemobile/presentation/screens/mobile_court/cubit/mobile_court_cubit.dart';

class DealInProgressComponent extends StatelessWidget {
  final AccidentDealFormViewModel _formViewModel;

  DealInProgressComponent({
    required AccidentDealFormViewModel formViewModel,
    Key? key,
  })  : this._formViewModel = formViewModel,
        super(key: key);

  final _cubit = Get.find<MobileCourtCubit>();

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: AlertDialog(
        title: Stack(
          children: [
            Container(
              padding: PaddingManager.large,
              child: Column(
                children: <Widget>[
                  Icon(
                    Icons.mobile_friendly_rounded,
                    size: 24.sp,
                    color: ColorManager.secondaryColor,
                  ),
                  Text(
                    "Acordo em andamento",
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
            Positioned(
              right: 0.0,
              child: GestureDetector(
                onTap: () async => await Get.toNamed(Routes.homeRoute),
                child: Align(
                  alignment: Alignment.topRight,
                  child: Icon(
                    Icons.close,
                    color: ColorManager.primaryColor,
                  ),
                ),
              ),
            ),
          ],
        ),
        // Container(
        //   padding: PaddingManager.large,
        //   child: Column(
        //     children: <Widget>[
        //       Icon(
        //         Icons.mobile_friendly_rounded,
        //         size: 24.sp,
        //         color: ColorManager.secondaryColor,
        //       ),
        //       Text(
        //         "Acordo em andamento",
        //         textAlign: TextAlign.center,
        //       ),
        //     ],
        //   ),
        // ),
        content: Text.rich(
          TextSpan(
            children: <TextSpan>[
              TextSpan(text: "Você possui um acordo de"),
              TextSpan(text: " "),
              TextSpan(
                text: "colisão de veículos",
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              TextSpan(text: " "),
              TextSpan(text: "em andamento."),
              TextSpan(text: " "),
              TextSpan(
                text:
                    "Deseja continuar o preenchimento ou cancelar e iniciar um novo acordo?",
              ),
            ],
          ),
          textAlign: TextAlign.justify,
        ),
        actions: <Widget>[
          TextButton(
            child: Text("Nova"),
            // coverage:ignore-start
            onPressed: () async {
              await this._cubit.removeDealFromDataSource();
            },
            // coverage:ignore-end
          ),
          TextButton(
            child: Text("Continuar"),
            // coverage:ignore-start
            onPressed: () async {
              await Get.toNamed(
                Routes.mobileCourtFormRoute,
                arguments: {
                  "formViewModel": this._formViewModel,
                },
              );

              await Get.offNamed(Routes.homeRoute);
            },
            // coverage:ignore-end
          ),
        ],
      ),
    );
  }
}
