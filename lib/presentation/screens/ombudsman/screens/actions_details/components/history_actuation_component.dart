import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get_utils/get_utils.dart';
import 'package:tjcemobile/data/model/history_actuation_model.dart';
import 'package:tjcemobile/presentation/resources/color_manager.dart';
import 'package:tjcemobile/presentation/resources/font_manager.dart';
import 'package:tjcemobile/presentation/resources/padding_manager.dart';

class ProtocolActivityHistoryComponent extends StatelessWidget {
  final List<HistoryActuationDataModel> history;

  ProtocolActivityHistoryComponent({
    Key? key,
    required this.history,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      child: _buildBody(history),
    );
  }

  Widget _buildBody(List<HistoryActuationDataModel> history) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: ListView.builder(
            itemCount: history.length,
            itemBuilder: (context, index) {
              final item = history[index];
              int currentHistoryLength = history.length - index - 1;
              return Semantics(
                label:
                    '${history.length > 0 ? '' : 'Este acionamento ainda não possui movimentação'}',
                child: Padding(
                  padding: PaddingManager.medium,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildMovement(
                        historyLength: currentHistoryLength,
                        date: item.dataAtualizacao ?? '',
                        movement: item.movimentacao ?? '',
                        response: item.resposta ?? '',
                        color: item.movimentacao == "Resposta Definitiva"
                            ? ColorManager.successColor
                            : ColorManager.secondaryColor,
                      ),
                      SizedBox(height: 12.r),
                      Divider(
                        color: context.theme.colorScheme.outlineVariant,
                        height: 1.r,
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildMovement({
    required String date,
    required String movement,
    required String response,
    Color? color,
    int? historyLength,
  }) {
    return Semantics(
      hint:
          '${(historyLength ?? 0) > 0 ? 'Este acionamento tem movimentações anteriores. Toque abaixo para ver detalhes.' : 'Este acionamento não tem mais movimentações anteriores.'}',
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Text(
            _formatDate(date),
            semanticsLabel: 'Data da movimentação ${_formatDate(date)}',
            style: TextStyle(
              fontSize: FontSizeManager.s14,
              fontWeight: FontWeightManager.medium,
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: 4.r),
          Text(
            response,
            semanticsLabel: 'Mensagem da movimentação $response',
            style: TextStyle(
              fontSize: FontSizeManager.s14,
              fontWeight: FontWeightManager.medium,
              color: Colors.black,
            ),
          ),
          SizedBox(height: 4.r),
          Text(
            "${movement[0].toUpperCase() + movement.substring(1).toLowerCase()}",
            semanticsLabel: 'Status da movimentação $movement',
            style: TextStyle(
                fontSize: FontSizeManager.s12,
                fontWeight: FontWeightManager.medium,
                color: color ?? Colors.black),
          ),
        ],
      ),
    );
  }

  String _formatDate(String date) {
    DateTime parseDate = DateTime.parse(date);
    return DateFormat('dd/MM/yyyy HH:mm:ss').format(parseDate);
  }
}
