part of 'ombudsman_actions_details_cubit.dart';

enum OmbudsmanActionsDetailsContentState { DEFAULT, LOADING, SUCCESS, ERROR }

enum OmbudsmanActionsDetailsSegment { DETAILS, HISTORY }

class OmbudsmanActionsDetailsState extends Equatable {
  final OmbudsmanActionsDetailsContentState contentState;
  final Failure? failure;
  final ActuationModel? actuationModel;
  final HistoryActuationModel? historyActuationModel;
  final OmbudsmanActionsDetailsSegment selectedSegment;
  final String? protocol;
  final String? accessCode;

  const OmbudsmanActionsDetailsState({
    this.contentState = OmbudsmanActionsDetailsContentState.DEFAULT,
    this.failure,
    this.actuationModel,
    this.historyActuationModel,
    this.protocol,
    this.accessCode,
    this.selectedSegment = OmbudsmanActionsDetailsSegment.DETAILS,
  });

  OmbudsmanActionsDetailsState copyWith({
    OmbudsmanActionsDetailsContentState? contentState,
    Failure? failure,
    String? protocol,
    String? accessCode,
    ActuationModel? actuationModel,
    HistoryActuationModel? historyActuationModel,
    OmbudsmanActionsDetailsSegment? selectedSegment,
  }) {
    return OmbudsmanActionsDetailsState(
      protocol: protocol ?? this.protocol,
      accessCode: accessCode ?? this.accessCode,
      contentState: contentState ?? this.contentState,
      failure: failure ?? this.failure,
      actuationModel: actuationModel ?? this.actuationModel,
      historyActuationModel:
          historyActuationModel ?? this.historyActuationModel,
      selectedSegment: selectedSegment ?? this.selectedSegment,
    );
  }

  @override
  List<Object?> get props => [
        contentState,
        failure,
        actuationModel,
        historyActuationModel,
        selectedSegment,
      ];
}
