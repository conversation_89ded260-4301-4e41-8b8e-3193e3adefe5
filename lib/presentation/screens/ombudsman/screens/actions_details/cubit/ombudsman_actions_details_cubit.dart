import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:tjcemobile/data/model/actuation_model.dart';
import 'package:tjcemobile/data/model/history_actuation_model.dart';
import 'package:tjcemobile/data/network/failure.dart';
import 'package:tjcemobile/domain/usecase/ombudsman/ombudsman_actions_details_usecase.dart';
import 'package:tjcemobile/domain/viewmodel/actuation_consult_viewmodel.dart';

part 'ombudsman_actions_details_state.dart';

class OmbudsmanActionsDetailsCubit extends Cubit<OmbudsmanActionsDetailsState> {
  OmbudsmanActionsDetailsUseCase _useCase;

  OmbudsmanActionsDetailsCubit(this._useCase)
      : super(OmbudsmanActionsDetailsState());

  Future<void> execute(String actuationId) async {
    emit(state.copyWith(
      contentState: OmbudsmanActionsDetailsContentState.LOADING,
    ));

    (await _useCase.getActuationModelById(actuationId)).fold(
      (Failure failure) {
        emit(state.copyWith(
          contentState: OmbudsmanActionsDetailsContentState.ERROR,
          failure: failure,
        ));
      },
      (ActuationModel? actuationModel) {
        emit(state.copyWith(
          contentState: OmbudsmanActionsDetailsContentState.SUCCESS,
          actuationModel: actuationModel,
        ));
      },
    );
  }

  Future<void> loadHistory(ActuationModel value) async {
    emit(state.copyWith(
      contentState: OmbudsmanActionsDetailsContentState.LOADING,
    ));
    ActuationConsultViewModel viewModel = ActuationConsultViewModel(
      protocolo: int.parse(value.data?.protocolo ?? "-1"),
      codigoAcesso: value.data?.codigoAcesso ?? "",
    );

    (await _useCase.getHistory(viewModel)).fold(
      (Failure failure) {
        emit(state.copyWith(
          contentState: OmbudsmanActionsDetailsContentState.ERROR,
          failure: failure,
        ));
      },
      (HistoryActuationModel history) {
        emit(state.copyWith(
          contentState: OmbudsmanActionsDetailsContentState.SUCCESS,
          historyActuationModel: history,
        ));
      },
    );
  }

  void changeSegment(OmbudsmanActionsDetailsSegment segment) {
    emit(state.copyWith(selectedSegment: segment));
  }
}
