import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:tjcemobile/app/di.dart';
import 'package:tjcemobile/data/model/actuation_model.dart';
import 'package:tjcemobile/data/model/history_actuation_model.dart';
import 'package:tjcemobile/presentation/resources/color_manager.dart';
import 'package:tjcemobile/presentation/resources/padding_manager.dart';
import 'package:tjcemobile/presentation/resources/routes_manager.dart';
import 'package:tjcemobile/presentation/screens/ombudsman/screens/actions_details/components/history_actuation_component.dart';
import 'package:tjcemobile/presentation/screens/ombudsman/screens/actions_details/cubit/ombudsman_actions_details_cubit.dart';
import 'package:tjcemobile/presentation/screens/ombudsman/screens/consult_protocol/components/protocol_details_component.dart';
import 'package:tjcemobile/presentation/widgets/arrow_back_component.dart';
import 'package:tjcemobile/presentation/widgets/custom_base_card.dart';
import 'package:tjcemobile/presentation/widgets/custom_base_scaffold.dart';

class OmbudsmanActionsDetailsView extends StatefulWidget {
  final ActuationModel _actuationModel;

  const OmbudsmanActionsDetailsView({
    required ActuationModel actuationModel,
    required HistoryActuationModel historyModel,
    Key? key,
  })  : this._actuationModel = actuationModel,
        super(key: key);

  @override
  _OmbudsmanActionsDetailsViewState createState() =>
      _OmbudsmanActionsDetailsViewState();
}

class _OmbudsmanActionsDetailsViewState
    extends State<OmbudsmanActionsDetailsView> {
  final _cubit = Get.find<OmbudsmanActionsDetailsCubit>();

  @override
  void initState() {
    super.initState();
    _cubit.execute(widget._actuationModel.data?.id ?? "");
    _cubit.loadHistory(widget._actuationModel);
  }

  @override
  void dispose() {
    _cubit.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return CustomBaseScaffold(
      appBar: _buildAppBar(),
      appBarBottom: _buildSegmentedButtons(context),
      body: _buildBody(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text("Acionamento"),
      leading: ArrowBackComponent(
        onPressed: () => Get.back(),
      ),
    );
  }

  Widget _buildSegmentedButtons(BuildContext context) {
    return BlocBuilder<OmbudsmanActionsDetailsCubit,
        OmbudsmanActionsDetailsState>(
      bloc: _cubit,
      builder: (BuildContext context, OmbudsmanActionsDetailsState state) {
        return Semantics(
          label:
              'Selecione uma das opções para visualizar detalhes ou movimentações do acionamento',
          child: Container(
            width: context.width,
            child: SegmentedButton<OmbudsmanActionsDetailsSegment>(
              showSelectedIcon: false,
              segments: <ButtonSegment<OmbudsmanActionsDetailsSegment>>[
                ButtonSegment<OmbudsmanActionsDetailsSegment>(
                  value: OmbudsmanActionsDetailsSegment.DETAILS,
                  label: Text(
                    "Detalhes",
                    semanticsLabel:
                        '${state.selectedSegment == OmbudsmanActionsDetailsSegment.DETAILS ? 'Detalhes. Toque na lista abaixo para conferir as informações.' : 'Detalhes'}',
                  ),
                ),
                ButtonSegment<OmbudsmanActionsDetailsSegment>(
                  value: OmbudsmanActionsDetailsSegment.HISTORY,
                  label: Text(
                    "Movimentação",
                    semanticsLabel:
                        '${state.selectedSegment == OmbudsmanActionsDetailsSegment.HISTORY ? 'Movimentação. Toque na lista abaixo para conferir as movimentações.' : 'Movimentação'}',
                  ),
                ),
              ],
              selected: <OmbudsmanActionsDetailsSegment>{state.selectedSegment},
              onSelectionChanged:
                  (Set<OmbudsmanActionsDetailsSegment> newSelection) {
                _cubit.changeSegment(newSelection.first);
              },
              style: ButtonStyle(
                side: WidgetStateProperty.all(
                  BorderSide(
                    color: ColorManager.whiteColor,
                    width: 1.r,
                    style: BorderStyle.solid,
                  ),
                ),
                backgroundColor: WidgetStateProperty.resolveWith<Color>(
                  (Set<WidgetState> states) {
                    if (states.contains(WidgetState.selected)) {
                      return context.theme.colorScheme.secondaryContainer;
                    }
                    return context.theme.primaryColor;
                  },
                ),
                foregroundColor: WidgetStateProperty.resolveWith<Color>(
                  (Set<WidgetState> states) {
                    if (states.contains(WidgetState.selected)) {
                      return context.theme.colorScheme.onSurfaceVariant;
                    }
                    return context.theme.colorScheme.onPrimary;
                  },
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildBody() {
    return BlocBuilder<OmbudsmanActionsDetailsCubit,
        OmbudsmanActionsDetailsState>(
      bloc: _cubit,
      builder: (BuildContext context, OmbudsmanActionsDetailsState state) {
        if (state.selectedSegment == OmbudsmanActionsDetailsSegment.DETAILS) {
          initOmbudsmanConsultProtocolModule();
        }

        return CustomBaseCard(
          child: Padding(
            padding: PaddingManager.medium,
            child: Column(
              children: <Widget>[
                Expanded(
                  child: state.selectedSegment ==
                          OmbudsmanActionsDetailsSegment.DETAILS
                      ? ProtocolDetailsComponent(
                          actuationModel: widget._actuationModel,
                        )
                      : ProtocolActivityHistoryComponent(
                          history:
                              state.historyActuationModel?.data?.toList() ?? [],
                        ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
