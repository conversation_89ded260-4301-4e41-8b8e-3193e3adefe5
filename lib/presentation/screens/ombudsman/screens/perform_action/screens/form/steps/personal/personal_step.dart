import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';
import 'package:tjcemobile/app/shared/enum/input_format_enum.dart';
import 'package:tjcemobile/app/shared/utils/snackbar_utils.dart';
import 'package:tjcemobile/app/shared/utils/validator_utils.dart';
import 'package:tjcemobile/data/model/company_model.dart';
import 'package:tjcemobile/data/model/general_list_model.dart';
import 'package:tjcemobile/domain/viewmodel/actuation_form_viewmodel.dart';
import 'package:tjcemobile/presentation/resources/font_manager.dart';
import 'package:tjcemobile/presentation/resources/padding_manager.dart';
import 'package:tjcemobile/presentation/screens/ombudsman/screens/perform_action/screens/form/cubit/ombudsman_perform_action_form_cubit.dart';
import 'package:tjcemobile/presentation/widgets/custom_base_card.dart';
import 'package:tjcemobile/presentation/widgets/custom_dropdown_menu_item.dart';
import 'package:tjcemobile/presentation/widgets/custom_text_form_field.dart';
import 'package:tjcemobile/presentation/widgets/primary_button.dart';
import 'package:tjcemobile/presentation/widgets/secondary_button.dart';

class PersonalStep extends StatefulWidget {
  final ActuationFormViewModel? _viewModel;

  PersonalStep({
    required ActuationFormViewModel? viewModel,
    Key? key,
  })  : this._viewModel = viewModel,
        super(key: key);

  @override
  _PersonalStepState createState() => _PersonalStepState();
}

class _PersonalStepState extends State<PersonalStep> {
  final _cubit = Get.find<OmbudsmanPerformActionFormCubit>();

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _cnpjController = TextEditingController();
  final TextEditingController _socialReasonController = TextEditingController();
  final TextEditingController _cpfController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();

  final cellPhoneMaskFormatter = MaskTextInputFormatter(
      mask: '(##) # ####-####', filter: {"#": RegExp(r'[0-9]')});

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      FocusScope.of(context).unfocus();
    });

    this._populateForm(this.widget._viewModel);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: <Widget>[
        this._buildBody(),
        SizedBox(height: 16.r),
        SizedBox(
            width: Get.width.r,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                Expanded(
                  child: SecondaryButton(
                    "Voltar",
                    onPressed: () async {
                      this._cubit.decrementStep();
                      await this._cubit.saveContentInPrefs();
                    },
                  ),
                ),
                SizedBox(width: FontSizeManager.s16),
                Expanded(
                  child: PrimaryButton(
                    "Avançar",
                    // coverage:ignore-start
                    onPressed: () async {
                      if (this._formKey.currentState!.validate()) {
                        this._cubit.incrementStep();
                        await this._cubit.saveContentInPrefs();
                      } else {
                        SnackbarUtils.error(
                          "Preencha todos os campos.",
                          "O formulário possui campos nulos e/ou vazios.",
                        );
                      }
                    },
                    // coverage:ignore-end
                  ),
                )
              ],
            )),
      ],
    );
  }

  Widget _buildBody() {
    return BlocBuilder<OmbudsmanPerformActionFormCubit,
        OmbudsmanPerformActionFormState>(
      bloc: Get.find<OmbudsmanPerformActionFormCubit>(),
      builder: (BuildContext context, OmbudsmanPerformActionFormState state) {
        return Expanded(
          child: CustomBaseCard(
            child: this._buildForm(state),
          ),
        );
      },
    );
  }

  Widget _buildForm(
    OmbudsmanPerformActionFormState state,
  ) {
    bool isLegalPerson = state.formViewModel?.tipoAcionador == 3;

    return Form(
      key: this._formKey,
      child: Container(
        padding: PaddingManager.medium,
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (isLegalPerson) ...[
                SizedBox(height: 8.r),
                this._buildCNPJTextField(
                  state.validateCNPJContentState,
                  state.companyModel,
                ),
                SizedBox(height: 8.r),
                this._companyNameTextFormField(isLegalPerson, state)
              ],
              SizedBox(height: 8.r),
              this._nameTextFormField(isLegalPerson, state),
              SizedBox(height: 8.r),
              this._cpfTextFormField(isLegalPerson, state),
              SizedBox(height: 8.r),
              this._typeGenderDropdownButtonFormField(state),
              SizedBox(height: 8.r),
              this._typeEducationDropdownButtonFormField(state),
              SizedBox(height: 8.r),
              this._typeProfessionDropdownButtonFormField(state),
              SizedBox(height: 8.r),
              this._haveDesabilityDropdownButtonFormField(state),
              if (state.formViewModel?.pessoaComDeficiencia == true) ...[
                SizedBox(height: 8.r),
                this._typDesabilityDropdownButtonFormField(state),
              ],
              SizedBox(height: 8.r),
              this._isOldDropdownButtonFormField(state),
              if (state.formViewModel?.pessoaIdosa == true) ...[
                SizedBox(height: 8.r),
                this._typeAgeListDropdownButtonFormField(state),
              ],
              SizedBox(height: 8.r),
              this._cellPhoneTextFormField(state),
              SizedBox(height: 8.r),
              this._emailTextFormField(state),
              SizedBox(height: 8.r),
            ],
          ),
        ),
      ),
    );
  }

  Widget _cpfTextFormField(
    bool isLegalPerson,
    OmbudsmanPerformActionFormState state,
  ) {
    String labelText = isLegalPerson ? "CPF do contato*" : "CPF*";

    return CustomTextFormField(
      labelText: labelText,
      controller: this._cpfController,
      keyboardType: TextInputType.number,
      inputFormat: InputFormatEnum.CPF,
      validator: ValidatorUtils.validateCPF,
      // coverage:ignore-start
      onChanged: (String? value) {
        if (value == null || value.length == 0) {
          this._cubit.changeState(formViewModel: state.formViewModel);

          return;
        }
        isLegalPerson
            ? this.widget._viewModel?.cpfAcionadorPessoaJuridica = value
            : this.widget._viewModel?.cpfAcionadorPessoaFisica = value;
        if (value.isNotEmpty && value.length == 14) {
          Get.focusScope?.unfocus();

          value = value.numericOnly();

          this._cubit.changeState(formViewModel: state.formViewModel);
        }
      },
      // coverage:ignore-end
    );
  }

  void _populateForm(ActuationFormViewModel? formViewModel) {
    if (formViewModel != null) {
      bool isLegalPerson = formViewModel.tipoAcionador == 3;

      this._nameController.value = TextEditingValue(
        text: isLegalPerson
            ? formViewModel.nomeContatoAcionadorPessoaJuridica
            : formViewModel.nomeAcionadorPessoaFisica,
      );

      this._socialReasonController.value = TextEditingValue(
        text: this.widget._viewModel?.razaoSocialAcionadorPessoaJuridica ?? "",
      );

      this._cpfController.value = TextEditingValue(
        text: isLegalPerson
            ? formViewModel.cpfAcionadorPessoaJuridica
            : formViewModel.cpfAcionadorPessoaFisica,
      );

      this._emailController.value = TextEditingValue(
        text: formViewModel.email ?? "",
      );

      this._cnpjController.value = TextEditingValue(
        text: formViewModel.cnpjAcionadorPessoaJuridica,
      );

      this._phoneController.value = TextEditingValue(
          text: cellPhoneMaskFormatter.maskText(
        formViewModel.telefone ?? "",
      ));
      this._cubit.getAgeGroupList();
      this._cubit.getTypeDisabilityList();

      this._cubit.changeState(formViewModel: formViewModel);
    }
  }

  CustomTextFormField _buildCNPJTextField(
    ValidateCNPJContentState? validateCNPJContentState,
    CompanyModel? companyModel,
  ) {
    String? helperText;
    Widget? suffix;

    if (validateCNPJContentState == ValidateCNPJContentState.LOADING) {
      helperText = "Buscando CNPJ...";
      suffix = Container(
        padding: PaddingManager.large,
        child: CircularProgressIndicator(color: context.theme.primaryColor),
      );
    }

    if (validateCNPJContentState == ValidateCNPJContentState.ERROR) {
      helperText = "O CNPJ não é válido";
      suffix = Icon(Icons.error_outline, color: Colors.red);
    }

    if (validateCNPJContentState == ValidateCNPJContentState.SUCCESS) {
      helperText = "CNPJ válido";
      suffix = Icon(Icons.check, color: Colors.green);

      SchedulerBinding.instance.addPostFrameCallback((_) {
        this._populateFieldsByCNPJ(companyModel);
      });
    }

    return CustomTextFormField(
      labelText: "CNPJ*",
      hintText: "Digite o CNPJ",
      helperText: helperText,
      suffix: suffix,
      maxLength: 18,
      keyboardType: TextInputType.number,
      controller: this._cnpjController,
      // coverage:ignore-start
      inputFormat: InputFormatEnum.CNPJ,
      validator: ValidatorUtils.validateCNPJ,
      onChanged: (String? value) async {
        if (value == null) return;

        this.widget._viewModel?.cnpjAcionadorPessoaJuridica = value;

        if (value.length == 18) {
          value = value.numericOnly();

          await this._cubit.getCompanyByCNPJ(value);

          return;
        }

        if (value.isEmpty) {
          this._populateFieldsByCNPJ(
            CompanyModel(data: CompanyDataModel()),
          );

          this._cubit.clearInformationCNPJ();
        }
      },
      // coverage:ignore-end
    );
  }

  void _populateFieldsByCNPJ(CompanyModel? companyModel) {
    if (companyModel != null &&
        companyModel.data != null &&
        companyModel.data!.nome != null) {
      this.widget._viewModel?.razaoSocialAcionadorPessoaJuridica =
          companyModel.data!.nome!;

      this._socialReasonController.value = TextEditingValue(
        text: this.widget._viewModel?.razaoSocialAcionadorPessoaJuridica ?? "",
      );

      return;
    }

    this.widget._viewModel?.razaoSocialAcionadorPessoaJuridica = "";
    this._socialReasonController.value = TextEditingValue(text: "");
  }

  Widget _nameTextFormField(
    bool isLegalPerson,
    OmbudsmanPerformActionFormState state,
  ) {
    String labelText = "Nome*";

    if (isLegalPerson) {
      labelText = "Nome do Contato*";
    }

    return CustomTextFormField(
      labelText: labelText,
      controller: this._nameController,
      keyboardType: TextInputType.text,
      validator: (String? value) {
        return ValidatorUtils.validateTwoStrings(
          isRequired: true,
          value,
          fieldName: labelText.toLowerCase().replaceAll("*", ""),
        );
      },
      onChanged: (String? value) {
        if (value == null || value.length == 0) {
          this._cubit.changeState(formViewModel: state.formViewModel);

          return;
        }
        isLegalPerson
            ? this.widget._viewModel?.nomeContatoAcionadorPessoaJuridica = value
            : this.widget._viewModel?.nomeAcionadorPessoaFisica = value;

        this._cubit.changeState(formViewModel: this.widget._viewModel);
      },
    );
  }

  Widget _cellPhoneTextFormField(
    OmbudsmanPerformActionFormState state,
  ) {
    String labelText = "Telefone";

    return CustomTextFormField(
      labelText: labelText,
      controller: this._phoneController,
      keyboardType: TextInputType.number,
      inputFormat: InputFormatEnum.PHONE,
      validator: ValidatorUtils.validatePhone,
      // coverage:ignore-start
      onChanged: (String? value) {
        if (value == null || value.length == 0) {
          this._cubit.changeState(formViewModel: state.formViewModel);

          return;
        }

        if (value.isNotEmpty && value.length == 16) {
          value = value.numericOnly();
          this.widget._viewModel?.telefone = value;
          this._cubit.changeState(formViewModel: state.formViewModel);
        }
      },
      // coverage:ignore-end
    );
  }

  Widget _emailTextFormField(
    OmbudsmanPerformActionFormState state,
  ) {
    String labelText = "E-mail*";

    return CustomTextFormField(
      labelText: labelText,
      controller: this._emailController,
      keyboardType: TextInputType.emailAddress,
      inputAction: TextInputAction.done,
      validator: ValidatorUtils.validateEmail,
      // coverage:ignore-start
      onChanged: (String? value) {
        this.widget._viewModel?.email = value;
        this._cubit.changeState(formViewModel: state.formViewModel);
      },
      // coverage:ignore-end
    );
  }

  Widget _companyNameTextFormField(
    bool isLegalPerson,
    OmbudsmanPerformActionFormState state,
  ) {
    String labelText = "Razão social*";

    return CustomTextFormField(
      labelText: labelText,
      hintText: "Digite a razão social",
      enabled: state.companyModel != null ? true : false,
      keyboardType: TextInputType.text,
      controller: this._socialReasonController,
      inputFormat: InputFormatEnum.TEXT,
      // coverage:ignore-start
      validator: (String? value) {
        return ValidatorUtils.validateTwoStrings(
          value,
          fieldName: "razão social",
        );
      },
      onChanged: (String? value) async {
        if (value == null) return;

        this.widget._viewModel?.razaoSocialAcionadorPessoaJuridica = value;

        this._cubit.changeState(formViewModel: this.widget._viewModel);
      },
      // coverage:ignore-end
    );
  }

  Widget _typeGenderDropdownButtonFormField(
    OmbudsmanPerformActionFormState state,
  ) {
    List<String> listGender = ["M", "F", "O"];
    String? selectedValue;

    if (this.widget._viewModel?.sexo != null) {
      selectedValue = this.widget._viewModel!.sexo;
    }

    return CustomDropdownMenuItem<String>(
      labelText: "Gênero*",
      icon: Icon(Icons.arrow_drop_down, size: FontSizeManager.s24),
      value: selectedValue!.isNotEmpty ? selectedValue : null,
      items: listGender.map(
        (String item) {
          return DropdownMenuItem<String>(
            value: item,
            child: FittedBox(
              fit: BoxFit.contain,
              child: Text(item == "F"
                  ? "Feminino"
                  : item == "M"
                      ? "Masculino"
                      : "Outro"),
            ),
          );
        },
      ).toList(),
      validator: (String? value) {
        return ValidatorUtils.validateString(
          value,
          fieldName: "Gênero",
        );
      },
      onChanged: (String? value) async {
        if (value == null) return;

        this.widget._viewModel?.sexo = value;

        this._cubit.changeState(
              formViewModel: this.widget._viewModel,
            );

        Get.focusScope?.requestFocus(FocusNode());
      },
    );
  }

  Widget _typeEducationDropdownButtonFormField(
    OmbudsmanPerformActionFormState state,
  ) {
    List<GeneralListDataModel> items = [];

    if (state.educationListModel?.data != null) {
      items = state.educationListModel?.data ?? [];
    }

    return CustomDropdownMenuItem<int>(
      labelText: "Escolaridade*",
      icon: Icon(Icons.arrow_drop_down, size: FontSizeManager.s24),
      items: items.map(
        (GeneralListDataModel item) {
          return DropdownMenuItem<int>(
            value: item.codEscolaridade,
            child: FittedBox(
              fit: BoxFit.contain,
              child: Text(item.dscEscolaridade ?? "-"),
            ),
          );
        },
      ).toList(),
      value: this.widget._viewModel?.escolaridade,
      validator: (int? value) {
        return ValidatorUtils.validateInt(
          value,
          fieldName: "Escolaridade",
        );
      },
      // coverage:ignore-start
      onChanged: (int? value) async {
        if (value == null) return;

        this.widget._viewModel?.escolaridade = value;

        this._cubit.changeState(
              formViewModel: this.widget._viewModel,
            );

        Get.focusScope?.requestFocus(FocusNode());
      },
      // coverage:ignore-end
    );
  }

  Widget _typeProfessionDropdownButtonFormField(
    OmbudsmanPerformActionFormState state,
  ) {
    List<GeneralListDataModel> items = [];

    if (state.professionListModel?.data != null) {
      items = state.professionListModel?.data ?? [];
    }
    return CustomDropdownMenuItem<int>(
      labelText: "Atividade profissional*",
      icon: Icon(Icons.arrow_drop_down, size: FontSizeManager.s24),
      items: items.map(
        (GeneralListDataModel item) {
          return DropdownMenuItem<int>(
            value: item.codAtividadeProfissional,
            child: FittedBox(
              fit: BoxFit.contain,
              child: Text(item.dscAtividadeProfissional ?? "-"),
            ),
          );
        },
      ).toList(),
      value: this.widget._viewModel?.atividadeProfissional,
      validator: (int? value) {
        return ValidatorUtils.validateInt(
          value,
          fieldName: "Atividade profissional",
        );
      },
      // coverage:ignore-start
      onChanged: (int? value) async {
        if (value == null) return;

        this.widget._viewModel?.atividadeProfissional = value;

        this._cubit.changeState(
              formViewModel: this.widget._viewModel,
            );

        Get.focusScope?.requestFocus(FocusNode());
      },
      // coverage:ignore-end
    );
  }

  Widget _isOldDropdownButtonFormField(
    OmbudsmanPerformActionFormState state,
  ) {
    List<String> listisOld = ["Sim", "Não"];
    bool? selectedValue;

    if (this.widget._viewModel?.pessoaIdosa != null) {
      selectedValue = this.widget._viewModel!.pessoaIdosa;
    }

    return CustomDropdownMenuItem<bool>(
      labelText: "Pessoa idosa*",
      icon: Icon(Icons.arrow_drop_down, size: FontSizeManager.s24),
      value: selectedValue,
      items: listisOld.map(
        (String item) {
          return DropdownMenuItem<bool>(
            value: item == "Sim" ? true : false,
            child: FittedBox(
              fit: BoxFit.contain,
              child: Text(item),
            ),
          );
        },
      ).toList(),
      validator: (bool? value) {
        return ValidatorUtils.validateBool(
          value,
          fieldName: "Pessoa idosa*",
        );
      },
      onChanged: (bool? value) async {
        if (value == null) return;

        this.widget._viewModel?.pessoaIdosa = value;

        this.widget._viewModel?.faixaEtaria = null;

        this._cubit.getAgeGroupList();

        this._cubit.changeState(
              formViewModel: this.widget._viewModel,
            );

        Get.focusScope?.requestFocus(FocusNode());
      },
    );
  }

  Widget _haveDesabilityDropdownButtonFormField(
    OmbudsmanPerformActionFormState state,
  ) {
    List<String> listHaveDesability = ["Sim", "Não"];
    bool? selectedValue;

    if (this.widget._viewModel?.pessoaComDeficiencia != null) {
      selectedValue = this.widget._viewModel!.pessoaComDeficiencia;
    }

    return CustomDropdownMenuItem<bool>(
      labelText: "Possui alguma deficiência ?*",
      icon: Icon(Icons.arrow_drop_down, size: FontSizeManager.s24),
      value: selectedValue,
      items: listHaveDesability.map(
        (String item) {
          return DropdownMenuItem<bool>(
            value: item == "Sim" ? true : false,
            child: FittedBox(
              fit: BoxFit.contain,
              child: Text(item),
            ),
          );
        },
      ).toList(),
      validator: (bool? value) {
        return ValidatorUtils.validateBool(
          value,
          fieldName: "Possui alguma deficiência ?",
        );
      },
      onChanged: (bool? value) async {
        if (value == null) return;

        this.widget._viewModel?.pessoaComDeficiencia = value;

        this.widget._viewModel?.tipoDeficiencia = null;

        this._cubit.getTypeDisabilityList();

        this._cubit.changeState(
              formViewModel: this.widget._viewModel,
            );

        Get.focusScope?.requestFocus(FocusNode());
      },
    );
  }

  Widget _typDesabilityDropdownButtonFormField(
    OmbudsmanPerformActionFormState state,
  ) {
    List<GeneralListDataModel> items = [];

    String? helperText;
    Widget? icon;

    if (state.typeDesabilityContentState ==
        TypeDesabilityContentState.LOADING) {
      helperText = "Buscando tipo de deficiência...";
      icon = Container(
        width: 24.r,
        child: CircularProgressIndicator(color: context.theme.primaryColor),
      );
    }

    if (state.typeDesabilityContentState == TypeDesabilityContentState.ERROR) {
      helperText = "Falha ao buscar tipo de deficiência";
      icon = Icon(Icons.error_outline,
          size: FontSizeManager.s16, color: Colors.red);
    }

    if (state.typeDesabilityContentState ==
        TypeDesabilityContentState.SUCCESS) {
      icon = Icon(Icons.arrow_drop_down, size: FontSizeManager.s24);

      if (state.disabilityList?.data != null) {
        items = state.disabilityList?.data ?? [];
      }
    }

    return CustomDropdownMenuItem<int>(
      labelText: "Tipo de deficiência*",
      helperText: helperText,
      icon: icon,
      items: items.map((GeneralListDataModel value) {
        // coverage:ignore-start
        return DropdownMenuItem<int>(
          value: value.codTipoDeficiencia,
          child: FittedBox(
            fit: BoxFit.contain,
            child: Text(
              value.dscTipoDeficiencia ?? "",
              overflow: TextOverflow.ellipsis,
            ),
          ),
        );
        // coverage:ignore-end
      }).toList(),
      value: this.widget._viewModel?.tipoDeficiencia,
      // coverage:ignore-start
      validator: (int? value) {
        return ValidatorUtils.validateInt(value,
            fieldName: "tipo da deficiência");
      },
      onChanged: (int? value) {
        if (value == null) return;

        this.widget._viewModel?.tipoDeficiencia = value;

        this._cubit.changeState(formViewModel: this.widget._viewModel);

        Get.focusScope?.requestFocus(FocusNode());
      },
      // coverage:ignore-end
    );
  }

  Widget _typeAgeListDropdownButtonFormField(
    OmbudsmanPerformActionFormState state,
  ) {
    List<GeneralListDataModel> items = [];

    String? helperText;
    Widget? icon;

    if (state.typeAgeListContentState == TypeAgeListContentState.LOADING) {
      helperText = "Buscando faixa etária...";
      icon = Container(
        width: 24.r,
        child: CircularProgressIndicator(color: context.theme.primaryColor),
      );
    }

    if (state.typeAgeListContentState == TypeAgeListContentState.ERROR) {
      helperText = "Falha ao buscar faixa etária";
      icon = Icon(Icons.error_outline,
          size: FontSizeManager.s16, color: Colors.red);
    }

    if (state.typeAgeListContentState == TypeAgeListContentState.SUCCESS) {
      icon = Icon(Icons.arrow_drop_down, size: FontSizeManager.s24);

      if (state.ageGroupListModel?.data != null) {
        items = state.ageGroupListModel?.data ?? [];
      }
    }

    return CustomDropdownMenuItem<int>(
      labelText: "Faixa etária*",
      helperText: helperText,
      icon: icon,
      items: items.map((GeneralListDataModel value) {
        // coverage:ignore-start
        return DropdownMenuItem<int>(
          value: value.codFaixaEtaria,
          child: FittedBox(
            fit: BoxFit.contain,
            child: Text(
              value.dscIntervFaixaEtaria ?? "",
              overflow: TextOverflow.ellipsis,
            ),
          ),
        );
        // coverage:ignore-end
      }).toList(),
      value: this.widget._viewModel?.faixaEtaria,
      // coverage:ignore-start
      validator: (int? value) {
        return ValidatorUtils.validateInt(
          value,
          fieldName: "Faixa etária",
        );
      },
      onChanged: (int? value) {
        if (value == null) return;

        this.widget._viewModel?.faixaEtaria = value;

        this._cubit.changeState(formViewModel: this.widget._viewModel);

        Get.focusScope?.requestFocus(FocusNode());
      },
      // coverage:ignore-end
    );
  }
}
