import 'dart:convert';
import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:tjcemobile/app/shared/enum/input_format_enum.dart';
import 'package:tjcemobile/app/shared/utils/snackbar_utils.dart';
import 'package:tjcemobile/app/shared/utils/validator_utils.dart';
import 'package:tjcemobile/data/model/general_list_model.dart';
import 'package:tjcemobile/domain/viewmodel/actuation_form_viewmodel.dart';
import 'package:tjcemobile/presentation/resources/color_manager.dart';
import 'package:tjcemobile/presentation/resources/font_manager.dart';
import 'package:tjcemobile/presentation/resources/padding_manager.dart';
import 'package:tjcemobile/presentation/screens/ombudsman/screens/perform_action/screens/form/cubit/ombudsman_perform_action_form_cubit.dart';
import 'package:tjcemobile/presentation/widgets/custom_base_card.dart';
import 'package:tjcemobile/presentation/widgets/custom_dropdown_menu_item.dart';
import 'package:tjcemobile/presentation/widgets/custom_dropdown_search.dart';
import 'package:tjcemobile/presentation/widgets/custom_text_form_field.dart';
import 'package:tjcemobile/presentation/widgets/document_picker_component.dart';
import 'package:tjcemobile/presentation/widgets/primary_button.dart';

class TypeMessageStep extends StatefulWidget {
  final ActuationFormViewModel? _viewModel;

  TypeMessageStep({
    required ActuationFormViewModel? viewModel,
    Key? key,
  })  : this._viewModel = viewModel,
        super(key: key);
  @override
  _TypeMessageStepState createState() => _TypeMessageStepState();
}

class _TypeMessageStepState extends State<TypeMessageStep> {
  final OmbudsmanPerformActionFormCubit _cubit =
      Get.find<OmbudsmanPerformActionFormCubit>();

  final _formKey = GlobalKey<FormState>();

  final TextEditingController _messageController = TextEditingController();
  final TextEditingController _processController = TextEditingController();

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await this._populateForm(this.widget._viewModel);
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OmbudsmanPerformActionFormCubit,
        OmbudsmanPerformActionFormState>(
      bloc: Get.find<OmbudsmanPerformActionFormCubit>(),
      builder: (BuildContext context, OmbudsmanPerformActionFormState state) {
        return Column(
          children: <Widget>[
            Expanded(
              child: CustomBaseCard(
                child: this._buildForm(state),
              ),
            ),
            SizedBox(height: 8.r),
            SizedBox(
              width: context.width,
              child: PrimaryButton(
                "Avançar",
                // coverage:ignore-start
                onPressed: () async {
                  if (this._formKey.currentState!.validate() &&
                      state.processContentState != ProcessContentState.ERROR) {
                    this._cubit.incrementStep();
                    await this._cubit.saveContentInPrefs();
                  } else {
                    SnackbarUtils.error(
                      "Preencha todos os campos.",
                      "O formulário possui campos nulos e/ou vazios",
                    );
                  }
                },
                // coverage:ignore-end
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildForm(
    OmbudsmanPerformActionFormState state,
  ) {
    var maxSizeFile =
        state.configurationListModel?.data?.tamanhoMaxArquivoAnexo ?? 0;

    return Form(
      key: _formKey,
      child: Container(
        padding: PaddingManager.medium,
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Text(
                "Deseja manter o anonimato*:",
                semanticsLabel: "Deseja manter o anonimato?",
                style: TextStyle(
                  fontSize: FontSizeManager.s14,
                  fontWeight: FontWeightManager.medium,
                ),
              ),
              SizedBox(height: 8.r),
              this._anonymityRadioButtons(state),
              SizedBox(height: 8.r),
              this._typePersonDropdownButtonFormField(
                state,
              ),
              SizedBox(height: 8.r),
              this._messageRoutingDropdownButtonFormField(
                state.subjectListModel?.data ?? [],
                state.formViewModel?.assuntoId,
                state,
              ),
              SizedBox(height: 8.r),
              this._messageTypeDropdownButtonFormField(
                state,
              ),
              if (state.formViewModel?.tipoMensagem == 11 ||
                  this.widget._viewModel?.tipoMensagem == 11) ...[
                SizedBox(height: 8.r),
                this._subTypeComplaintDropdownButtonFormField(
                  state,
                  state.formViewModel?.idSubtipoMensagem,
                ),
              ],
              if (state.formViewModel?.assuntoId == 2 ||
                  this.widget._viewModel?.assuntoId == 2) ...[
                SizedBox(height: 8.r),
                this._serviceProtocolDropdownButtonFormField(
                  state,
                ),
              ],
              if (state.formViewModel?.assuntoId != null)
                Column(
                  children: <Widget>[
                    SizedBox(height: 8.r),
                    _occurrenceLocationFormField(state),
                  ],
                ),
              SizedBox(height: 8.r),
              CustomTextFormField(
                labelText: "Mensagem*",
                controller: this._messageController,
                keyboardType: TextInputType.multiline,
                textAlign: TextAlign.justify,
                maxLines: 5,
                maxLength: 1000,
                validator: (String? value) {
                  return ValidatorUtils.validadeStringWithMinLength(
                    value,
                    fieldName: 'Mensagem',
                    sizeCaracteres: 10,
                  );
                },
                // coverage:ignore-start
                onChanged: (String? value) {
                  this.widget._viewModel?.mensagem = value;
                  this._cubit.changeState(
                        formViewModel: this.widget._viewModel,
                      );
                },
                // coverage:ignore-end
              ),
              SizedBox(height: 8.r),
              this._processFormField(state.processContentState, state),
              SizedBox(height: 8.r),
              ClipRRect(
                borderRadius: BorderRadius.circular(8.r),
                child: Container(
                  height: 200.r,
                  decoration: BoxDecoration(
                    color: ColorManager.whiteColor,
                    borderRadius: BorderRadius.circular(8.r),
                    border: Border.all(
                      width: 1.r,
                      color: context.theme.colorScheme.outlineVariant,
                    ),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: <Widget>[
                      Expanded(
                        child: this._buildDocumentsList(state),
                      ),
                      ClipRRect(
                        borderRadius: BorderRadius.only(
                          bottomLeft: Radius.circular(8.r),
                          bottomRight: Radius.circular(8.r),
                        ),
                        child: Container(
                          color: context.theme.colorScheme.surface,
                          child: Row(
                            children: <Widget>[
                              Expanded(
                                child: Text(
                                  "Anexe até 3 arquivos\n"
                                  "Extensões: JPG, PNG, PDF\n"
                                  "Anexo (limite de ${(maxSizeFile / 1000).toStringAsFixed(0)} MB):",
                                  softWrap: true,
                                  overflow: TextOverflow.ellipsis,
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    fontSize: FontSizeManager.s12,
                                    color: context.theme.colorScheme.outline,
                                  ),
                                ),
                              ),
                              this._buildAddFileButton(state: state),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              SizedBox(height: 16.r),
              Text(
                "Os campos sinalizados com (*) são obrigatórios",
                style: TextStyle(
                  fontSize: FontSizeManager.s12,
                  color: ColorManager.borderColor,
                  fontWeight: FontWeightManager.medium,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDocumentsList(OmbudsmanPerformActionFormState state) {
    if (state.formViewModel?.arquivosAnexo?.length == 0) {
      // coverage:ignore-start
      return Center(
        child: Text(
          "Nenhum arquivo selecionado",
          style: TextStyle(
            fontSize: FontSizeManager.s14,
            color: context.theme.colorScheme.error,
          ),
        ),
      );
      // coverage:ignore-end
    }

    return Container(
      padding: PaddingManager.symmetricHorizontalMedium,
      child: ListView.separated(
        shrinkWrap: true,
        physics: NeverScrollableScrollPhysics(),
        itemCount: state.formViewModel?.arquivosAnexo?.length ?? 0,
        itemBuilder: (context, int index) {
          ArquivosAnexoViewModel? item =
              state.formViewModel?.arquivosAnexo?[index];

          if (item?.nome == null || item?.arquivo == null) {
            return const SizedBox.shrink();
          }

          return Row(
            children: <Widget>[
              Expanded(
                child: Text(
                  item!.nome!.split('/').last,
                  softWrap: true,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    fontSize: 12.r,
                    color: context.theme.colorScheme.outline,
                  ),
                ),
              ),
              IconButton(
                icon: Icon(
                  Icons.close,
                  size: 16.r,
                  color: ColorManager.primaryColor,
                ),
                // coverage:ignore-start
                padding: EdgeInsets.zero,
                constraints: BoxConstraints(),
                onPressed: () async {
                  Get.closeAllSnackbars();

                  await Get.dialog(
                    AlertDialog(
                      title: Text("Excluir documento"),
                      content: Text("Deseja realmente remover?"),
                      actions: <Widget>[
                        TextButton(
                          child: Text("Não"),
                          onPressed: () => Get.back(),
                        ),
                        TextButton(
                          child: Text("Sim"),
                          onPressed: () async {
                            state.formViewModel?.arquivosAnexo?.removeAt(index);

                            this._cubit.changeState(
                                formViewModel: state.formViewModel);
                            Get.back();

                            await Get.forceAppUpdate();
                          },
                        ),
                      ],
                    ),
                    barrierDismissible: false,
                  );
                },
                // coverage:ignore-end
              ),
            ],
          );
        },
        separatorBuilder: (BuildContext context, int index) {
          return const Divider();
        },
      ),
    );
  }

  Widget _anonymityRadioButtons(
    OmbudsmanPerformActionFormState state,
  ) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: <Widget>[
        Row(
          children: <Widget>[
            Radio<String>(
              value: "s",
              groupValue: state.formViewModel?.desejaManterAnonimato ?? "n",
              // coverage:ignore-start
              onChanged: (String? value) async {
                if (value != null) {
                  this.widget._viewModel?.desejaManterAnonimato = value;
                  this
                      ._cubit
                      .changeState(formViewModel: this.widget._viewModel);
                  await Get.forceAppUpdate();
                }
              },
              // coverage:ignore-end
            ),
            Text(
              "Sim",
              semanticsLabel: "Sim",
              style: TextStyle(fontSize: FontSizeManager.s14),
            ),
          ],
        ),
        Row(
          children: <Widget>[
            Radio<String>(
              value: "n",
              groupValue: state.formViewModel?.desejaManterAnonimato ?? "n",
              // coverage:ignore-start
              onChanged: (String? value) async {
                if (value != null) {
                  state.formViewModel?.desejaManterAnonimato = value;

                  this._cubit.changeState(formViewModel: state.formViewModel);
                  await Get.forceAppUpdate();
                }
              },
              // coverage:ignore-end
            ),
            Text(
              "Não",
              semanticsLabel: "Não",
              style: TextStyle(fontSize: FontSizeManager.s14),
            ),
          ],
        ),
      ],
    );
  }

  Widget _messageTypeDropdownButtonFormField(
    OmbudsmanPerformActionFormState state,
  ) {
    List<GeneralListDataModel> items = [];

    String? helperText;
    Widget? icon;

    if (state.typesMessageContentState == TypesMessageContentState.LOADING) {
      helperText = "Buscando tipo de mensagem...";
      icon = Container(
        width: 24.r,
        child: CircularProgressIndicator(color: context.theme.primaryColor),
      );
    }

    if (state.typesMessageContentState == TypesMessageContentState.ERROR) {
      helperText = "Falha ao buscar tipo de mensagem";
      icon = Icon(Icons.error_outline,
          size: FontSizeManager.s16, color: Colors.red);
    }

    if (state.typesMessageContentState == TypesMessageContentState.SUCCESS) {
      icon = Icon(Icons.arrow_drop_down, size: FontSizeManager.s24);

      if (state.messageTypeListModel?.data != null) {
        items = state.messageTypeListModel?.data ?? [];
      }
    }

    return CustomDropdownMenuItem<int>(
      labelText: "Tipo de mensagem*",
      semanticsLabel: "Tipo de mensagem",
      helperText: helperText,
      icon: icon,
      items: items.map((GeneralListDataModel value) {
        // coverage:ignore-start
        return DropdownMenuItem<int>(
          value: value.id,
          child: FittedBox(
            fit: BoxFit.contain,
            child: Text(
              value.descricao ?? "",
              overflow: TextOverflow.ellipsis,
            ),
          ),
        );
        // coverage:ignore-end
      }).toList(),
      value: this.widget._viewModel?.tipoMensagem,
      // coverage:ignore-start
      validator: (int? value) {
        return ValidatorUtils.validateInt(value, fieldName: "tipo da mensagem");
      },
      onChanged: (int? value) {
        if (value == null) return;

        this.widget._viewModel?.tipoMensagem = value;

        this.widget._viewModel?.idSubtipoMensagem = null;

        this._cubit.changeState(formViewModel: this.widget._viewModel);

        this._cubit.getSubTypeComplaintList(value == 11 ? value : null);

        Get.focusScope?.requestFocus(FocusNode());
      },
      // coverage:ignore-end
    );
  }

  Widget _serviceProtocolDropdownButtonFormField(
    OmbudsmanPerformActionFormState state,
  ) {
    List<GeneralListDataModel> items = [];

    int? currentValueSelected = state.formViewModel?.canalAtendimento;

    String? helperText;
    Widget? icon;

    if (state.serviceProtocolContentState ==
        ServiceProtocolContentState.LOADING) {
      helperText = "Buscando canal de atendimento..";
      icon = Container(
        width: 24.r,
        child: CircularProgressIndicator(color: context.theme.primaryColor),
      );
    }

    if (state.serviceProtocolContentState ==
        ServiceProtocolContentState.ERROR) {
      helperText = "Falha ao buscar canal de atendimento";
      icon = Icon(Icons.error_outline,
          size: FontSizeManager.s16, color: Colors.red);
    }

    if (state.serviceProtocolContentState ==
        ServiceProtocolContentState.SUCCESS) {
      icon = Icon(Icons.arrow_drop_down, size: FontSizeManager.s24);

      if (state.serviceProtocol?.data != null) {
        items = state.serviceProtocol?.data ?? [];
      }
    }
    return CustomDropdownMenuItem<int>(
      helperText: helperText,
      icon: icon,
      labelText: "Canal de Atendimento*",
      items: items.map((GeneralListDataModel value) {
        // coverage:ignore-start
        return DropdownMenuItem<int>(
          value: value.codCanalAtendimento,
          child: FittedBox(
            fit: BoxFit.contain,
            child: Text(
              value.dscCanalAtendimento ?? "",
              overflow: TextOverflow.ellipsis,
            ),
          ),
        );
        // coverage:ignore-end
      }).toList(),
      value: currentValueSelected,
      // coverage:ignore-start
      validator: (int? value) {
        return ValidatorUtils.validateInt(
          value,
          fieldName: "Canal de Atendimento",
        );
      },
      onChanged: (int? value) {
        this.widget._viewModel?.canalAtendimento = value;

        this._cubit.changeState(formViewModel: this.widget._viewModel);

        Get.focusScope?.requestFocus(FocusNode());
        Get.forceAppUpdate();
      },
      // coverage:ignore-end
    );
  }

  Widget _subTypeComplaintDropdownButtonFormField(
    OmbudsmanPerformActionFormState state,
    int? currentValueSelected,
  ) {
    List<GeneralListDataModel> items = [];

    String? helperText;
    Widget? icon;

    if (state.subTypePersonContentState == SubTypePersonContentState.LOADING) {
      helperText = "Buscando subtipo da reclamação..";
      icon = Container(
        width: 24.r,
        child: CircularProgressIndicator(color: context.theme.primaryColor),
      );
    }

    if (state.subTypePersonContentState == SubTypePersonContentState.ERROR) {
      helperText = "Falha ao buscar subtipo da reclamação";
      icon = Icon(Icons.error_outline,
          size: FontSizeManager.s16, color: Colors.red);
    }

    if (state.subTypePersonContentState == SubTypePersonContentState.SUCCESS) {
      icon = Icon(Icons.arrow_drop_down, size: FontSizeManager.s24);

      if (state.subTypeComplaintListModel?.data != null) {
        items = state.subTypeComplaintListModel?.data ?? [];
      }
    }

    return CustomDropdownMenuItem<int>(
      labelText: "Subtipo da reclamação*",
      helperText: helperText,
      icon: icon,
      items: items.map((GeneralListDataModel value) {
        // coverage:ignore-start
        return DropdownMenuItem<int>(
          value: value.id,
          child: FittedBox(
            fit: BoxFit.contain,
            child: Text(
              value.descricao ?? "",
              overflow: TextOverflow.ellipsis,
            ),
          ),
        );
        // coverage:ignore-end
      }).toList(),
      value: currentValueSelected,
      validator: (int? value) {
        return ValidatorUtils.validateInt(
          value,
          fieldName: "subtipo da reclamação",
        );
      },
      // coverage:ignore-start
      onChanged: (int? value) {
        this.widget._viewModel?.idSubtipoMensagem = value;

        this._cubit.changeState(formViewModel: this.widget._viewModel);

        Get.focusScope?.requestFocus(FocusNode());
      },
      // coverage:ignore-end
    );
  }

  Widget _typePersonDropdownButtonFormField(
    OmbudsmanPerformActionFormState state,
  ) {
    List<GeneralListDataModel> items = [];

    if (state.triggerTypeListModel?.data != null) {
      items = state.triggerTypeListModel?.data ?? [];
    }

    return CustomDropdownMenuItem<int>(
      labelText: "Tipo de pessoa*",
      icon: Icon(Icons.arrow_drop_down, size: FontSizeManager.s24),
      items: items.map(
        (GeneralListDataModel item) {
          return DropdownMenuItem<int>(
            value: item.id,
            child: FittedBox(
              fit: BoxFit.contain,
              child: Text(item.descricao ?? "-"),
            ),
          );
        },
      ).toList(),
      value: this.widget._viewModel?.tipoAcionador,
      validator: (int? value) {
        return ValidatorUtils.validateInt(
          value,
          fieldName: "tipo de pessoa",
        );
      },
      // coverage:ignore-start
      onChanged: (int? value) async {
        if (value == null) return;

        this.widget._viewModel?.tipoAcionador = value;

        this._cubit.changeState(
              formViewModel: this.widget._viewModel,
            );

        this._cubit.clearInformationCNPJ();

        Get.focusScope?.requestFocus(FocusNode());
      },
      // coverage:ignore-end
    );
  }

  Widget _messageRoutingDropdownButtonFormField(
    List<GeneralListDataModel> subjectListModel,
    int? currentValueSelected,
    OmbudsmanPerformActionFormState state,
  ) {
    return CustomDropdownMenuItem<int>(
      labelText: "Direcionamento da mensagem*",
      items: subjectListModel.map((GeneralListDataModel? value) {
        // coverage:ignore-start
        return DropdownMenuItem<int>(
          value: value?.id,
          child: FittedBox(
            fit: BoxFit.contain,
            child: Text(
              value?.descricao ?? "",
              overflow: TextOverflow.ellipsis,
            ),
          ),
        );
        // coverage:ignore-end
      }).toList(),
      value: currentValueSelected,
      validator: (int? value) {
        return ValidatorUtils.validateInt(
          value,
          fieldName: "direcionamento de mensagem",
        );
      },
      // coverage:ignore-start
      onChanged: (int? value) {
        if (value == null) return;

        this.widget._viewModel?.assuntoId = value;

        this.widget._viewModel?.tipoMensagem = null;

        this.widget._viewModel?.localidadeOcorrencia = null;

        this.widget._viewModel?.canalAtendimento = null;

        this._cubit.changeState(formViewModel: this.widget._viewModel);

        this._cubit.getMessageTypeList(value);

        if (this.widget._viewModel?.assuntoId == 2) {
          this._cubit.getServiceChannel();
        }

        this._cubit.getOccurrenceLocationList();

        // this._cubit.changeOccurrenceLocationList(
        //     value: state.formViewModel?.assuntoId);

        Get.focusScope?.requestFocus(FocusNode());
      },
      // coverage:ignore-end
    );
  }

  Widget _occurrenceLocationFormField(
    OmbudsmanPerformActionFormState state,
  ) {
    List<GeneralListDataModel> items = [];

    if (state.occurrenceLocationList == null) return const SizedBox.shrink();

    if (state.occurrenceLocationList?.data != null) {
      items = state.occurrenceLocationList?.data ?? [];
    }

    return CustomDropDownSearch(
      labelText: "Localidade da ocorrência*",
      items: (filter, __) => items,
      filterFn: (GeneralListDataModel item, String filter) {
        return item.descricao!.toUpperCase().contains(filter.toUpperCase());
      },
      selectedItem: items
          .where((element) =>
              element.id == state.formViewModel?.localidadeOcorrencia)
          .firstOrNull,
      builder: (context, selectedItem) {
        return Text(
          selectedItem?.descricao ?? "",
          style: TextStyle(
            color: ColorManager.darkColor,
            fontWeight: FontWeightManager.regular,
            fontSize: FontSizeManager.s16,
            fontFamily: FontConstants.fontFamily,
          ),
        );
      },
      validator: (GeneralListDataModel? value) {
        return ValidatorUtils.validateInt(
          value?.id,
          fieldName: "Localidade da ocorrência",
        );
      },
      compareFn: (item1, item2) => item1.descricao == item2.descricao,
      itemBuilder: (_, item, __, isSelected) {
        return ListTile(
          selected: isSelected,
          title: Text(item.descricao ?? ""),
        );
      },
      onChanged: (GeneralListDataModel? value) async {
        this.widget._viewModel?.localidadeOcorrencia = value?.id;

        this._cubit.changeState(formViewModel: this.widget._viewModel);

        Get.focusScope?.requestFocus(FocusNode());
      },
    );
  }

  Widget _processFormField(
    ProcessContentState? processContentState,
    OmbudsmanPerformActionFormState state,
  ) {
    Widget? suffix;
    String helperText = 'Número do processo não é obrigatório';

    if (processContentState == ProcessContentState.LOADING) {
      helperText = "Buscando processo...";
      // coverage:ignore-start
      suffix = Container(
        padding: const EdgeInsets.all(16.0),
        child: const CircularProgressIndicator(),
      );
      // coverage:ignore-end
    }

    if (processContentState == ProcessContentState.ERROR) {
      helperText = "O numero de processo não é válido";
      // coverage:ignore-start
      suffix = Icon(Icons.error_outline, size: 24.sp, color: Colors.red);
      // coverage:ignore-end
    }

    if (processContentState == ProcessContentState.SUCCESS) {
      helperText = "Processo encontrado";
      suffix = Icon(Icons.check, size: 24.sp, color: Colors.green);
    }

    return CustomTextFormField(
      controller: this._processController,
      labelText: "Processo",
      keyboardType: TextInputType.numberWithOptions(
        signed: true,
      ),
      inputFormat: InputFormatEnum.PROCESS,
      suffix: suffix,
      helperText: helperText,
      validator: (String? value) {
        if (processContentState == ProcessContentState.SUCCESS) {
          return ValidatorUtils.validateProcess(
            value,
            isRequired: true,
          );
        }
        return null;
      },
      // coverage:ignore-start
      onChanged: (String? value) async {
        if (value == null || value.length == 0) {
          state.formViewModel?.numeroProcesso = value;
          TextEditingValue(
            text: state.formViewModel?.numeroProcesso ?? "",
          );
          this._cubit.changeState(formViewModel: state.formViewModel);

          return;
        }

        if (value.isNotEmpty && value.length == 25) {
          Get.focusScope?.unfocus();

          value = value.numericOnly();
          state.formViewModel?.numeroProcesso = value;
          this._cubit.changeState(formViewModel: state.formViewModel);

          await this._cubit.getProcessNumber(value);
        }
      },
      // coverage:ignore-end
    );
  }

  Widget _buildAddFileButton({required OmbudsmanPerformActionFormState state}) {
    if (state.formViewModel?.arquivosAnexo?.length == 3) {
      return const SizedBox.shrink();
    }

    return IconButton(
      onPressed: () async {
        await DocumentPickerComponent.show(
          context,
          fileType: FileType.custom,
          allowedExtensions: [
            'png',
            'jpg',
            'jpeg',
            'pdf',
          ],
          onImageSelected: (File? file) async {
            if (file != null) {
              if (file.lengthSync() >
                  state.configurationListModel!.data!.tamanhoMaxArquivoAnexo! *
                      1000) {
                SnackbarUtils.error(
                  "O arquivo selecionado é muito grande.",
                  "Não é possível adicionar arquivos maiores que ${state.configurationListModel!.data!.tamanhoMaxArquivoAnexo! / 1000}MB.",
                );

                return;
              }

              List<int> imageBytes = File(file.path).readAsBytesSync();
              String base64encoded = base64Encode(imageBytes);

              if (state.formViewModel?.arquivosAnexo == null) {
                state.formViewModel?.arquivosAnexo = [];
              }
              if (state.formViewModel?.arquivosAnexo != null) {
                state.formViewModel!.arquivosAnexo!.add(
                  ArquivosAnexoViewModel(
                    nome: file.path.split('/').last,
                    arquivo: base64encoded,
                  ),
                );
                state.formViewModel?.arquivosAnexo =
                    state.formViewModel?.arquivosAnexo;
                this._cubit.changeState(formViewModel: state.formViewModel);
              }
            }
          },
        );
      },
      icon: Icon(
        Icons.add_circle_rounded,
        color: ColorManager.primaryColor,
      ),
    );
  }

  Future<void> _populateForm(ActuationFormViewModel? formViewModel) async {
    if (formViewModel != null) {
      this._messageController.value =
          TextEditingValue(text: formViewModel.mensagem ?? "");

      this._processController.value =
          TextEditingValue(text: formViewModel.numeroProcesso ?? "");

      await this._cubit.getMessageTypeList(formViewModel.assuntoId);
      await this._cubit.getServiceChannel();
      await this._cubit.getSubTypeComplaintList(formViewModel.tipoMensagem);
      await this._cubit.getOccurrenceLocationList();
    }
  }
}
