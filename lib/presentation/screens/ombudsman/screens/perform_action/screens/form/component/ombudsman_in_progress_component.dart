import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:tjcemobile/domain/viewmodel/actuation_form_viewmodel.dart';
import 'package:tjcemobile/presentation/resources/color_manager.dart';
import 'package:tjcemobile/presentation/resources/padding_manager.dart';
import 'package:tjcemobile/presentation/resources/routes_manager.dart';
import 'package:tjcemobile/presentation/screens/ombudsman/cubit/ombudsman_cubit.dart';

class OmbudsmanInProgressComponent extends StatelessWidget {
  final ActuationFormViewModel _actuationFormViewModel;

  OmbudsmanInProgressComponent({
    required ActuationFormViewModel actuationFormViewModel,
    Key? key,
  })  : this._actuationFormViewModel = actuationFormViewModel,
        super(key: key);

  final _cubit = Get.find<OmbudsmanCubit>();

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: AlertDialog(
        title: Stack(
          children: [
            Container(
              padding: PaddingManager.large,
              child: Column(
                children: <Widget>[
                  Icon(
                    Icons.mobile_friendly_rounded,
                    size: 24.sp,
                    color: ColorManager.secondaryColor,
                  ),
                  Text(
                    "Solicitação em andamento",
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
            Positioned(
              right: 0.0,
              child: GestureDetector(
                onTap: () async => await Get.toNamed(Routes.homeRoute),
                child: Align(
                  alignment: Alignment.topRight,
                  child: Icon(
                    Icons.close,
                    color: ColorManager.primaryColor,
                  ),
                ),
              ),
            ),
          ],
        ),
        content: SizedBox(
          width: 360.r,
          child: Text.rich(
            TextSpan(
              children: <TextSpan>[
                TextSpan(text: "Você possui uma solicitação"),
                TextSpan(text: " "),
                TextSpan(text: "em andamento."),
                TextSpan(text: " "),
                TextSpan(
                  text:
                      "Deseja continuar o preenchimento ou cancelar e iniciar uma nova solicitação?",
                ),
              ],
            ),
            textAlign: TextAlign.justify,
          ),
        ),
        actions: <Widget>[
          TextButton(
            child: Text("Nova"),
            // coverage:ignore-start
            onPressed: () async {
              await this._cubit.removeOmbudsmanFormFromDataSource();
            },
            // coverage:ignore-end
          ),
          TextButton(
            child: Text("Continuar"),
            // coverage:ignore-start
            onPressed: () async {
              await Get.toNamed(
                Routes.ombudsmanPerformActionFormRoute,
                arguments: {
                  "formViewModel": this._actuationFormViewModel,
                },
              );

              await Get.offNamed(Routes.homeRoute);
            },
            // coverage:ignore-end
          ),
        ],
      ),
    );
  }
}
