import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';
import 'package:tjcemobile/data/model/general_list_model.dart';
import 'package:tjcemobile/domain/viewmodel/actuation_form_viewmodel.dart';
import 'package:tjcemobile/presentation/resources/color_manager.dart';
import 'package:tjcemobile/presentation/resources/font_manager.dart';
import 'package:tjcemobile/presentation/resources/padding_manager.dart';
import 'package:tjcemobile/presentation/screens/ombudsman/screens/perform_action/screens/form/cubit/ombudsman_perform_action_form_cubit.dart';
import 'package:tjcemobile/presentation/widgets/custom_base_card.dart';
import 'package:tjcemobile/presentation/widgets/primary_button.dart';
import 'package:tjcemobile/presentation/widgets/secondary_button.dart';

class ConfirmationStep extends StatefulWidget {
  final ActuationFormViewModel _formViewModel;

  const ConfirmationStep({
    required ActuationFormViewModel formViewModel,
    Key? key,
  })  : this._formViewModel = formViewModel,
        super(key: key);

  @override
  State<ConfirmationStep> createState() => _ConfirmationStepState();
}

class _ConfirmationStepState extends State<ConfirmationStep> {
  final _cubit = Get.find<OmbudsmanPerformActionFormCubit>();

  final _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      FocusScope.of(context).unfocus();
    });
  }

  @override
  Widget build(BuildContext context) {
    bool isLegalPerson = this.widget._formViewModel.tipoAcionador == 3;
    bool havePhone = this.widget._formViewModel.telefone != null;
    bool oldPerson = this.widget._formViewModel.pessoaIdosa;
    bool desabilityPerson = this.widget._formViewModel.pessoaComDeficiencia;
    bool isServiceChannel = this.widget._formViewModel.canalAtendimento != null;

    final cellPhoneMaskFormatter = MaskTextInputFormatter(
      mask: '(##) # ####-####',
      filter: {"#": RegExp(r'[0-9]')},
    );

    return BlocBuilder<OmbudsmanPerformActionFormCubit,
        OmbudsmanPerformActionFormState>(
      bloc: Get.find<OmbudsmanPerformActionFormCubit>(),
      builder: (BuildContext context, OmbudsmanPerformActionFormState state) {
        List<GeneralListDataModel> serviceProtocolDataModel =
            state.serviceProtocol?.data ?? [];

        List<GeneralListDataModel> typeMessage =
            state.messageTypeListModel?.data ?? [];

        List<GeneralListDataModel> ocorrenceLocation =
            state.occurrenceLocationList?.data ?? [];

        List<GeneralListDataModel> oldPersonList =
            state.ageGroupListModel?.data ?? [];

        List<GeneralListDataModel> desabilityType =
            state.disabilityList?.data ?? [];

        return Column(
          children: <Widget>[
            Expanded(
              child: CustomBaseCard(
                child: Container(
                  color: Colors.white,
                  padding: PaddingManager.large,
                  child: ListView(
                    controller: this._scrollController,
                    keyboardDismissBehavior:
                        ScrollViewKeyboardDismissBehavior.onDrag,
                    children: <Widget>[
                      Image.asset('assets/tjce_logo.png', height: 56.sp),
                      SizedBox(height: 8.r),
                      Text(
                        "CONFIRA OS DADOS INFORMADOS",
                        semanticsLabel: 'Confira os dados informados, caso estejam corretos você pode prosseguir tocando em concluir, caso deseje alterar, basta tocar em voltar.'
                                '\n ${isLegalPerson ? "Nome do contato" : "Nome"}:  ${isLegalPerson ? this.widget._formViewModel.nomeContatoAcionadorPessoaJuridica : this.widget._formViewModel.nomeAcionadorPessoaFisica}' +
                            '\n ${isLegalPerson ? "CNPJ" : "CPF"}:  ${isLegalPerson ? this.widget._formViewModel.cnpjAcionadorPessoaJuridica : this.widget._formViewModel.cpfAcionadorPessoaFisica}' +
                            '\n ${isLegalPerson ? "CPF: ${this.widget._formViewModel.cpfAcionadorPessoaJuridica}" : ""}' +
                            '\n "E-mail: ${this.widget._formViewModel.email}' +
                            '\n ${this.widget._formViewModel.telefone != null ? "Telefone: ${cellPhoneMaskFormatter.maskText(this.widget._formViewModel.telefone!)}" : ""}' +
                            '\n Pessoa idosa: ${oldPerson == true ? "Sim" : "Não"}' +
                            '\n ${oldPerson ? "Faixa etária: ${oldPersonList.firstWhereOrNull((value) => value.codFaixaEtaria == this.widget._formViewModel.faixaEtaria)?.dscIntervFaixaEtaria}" : ""}' +
                            '\n Pessoa com deficiência: ${desabilityPerson ? "Sim" : "Não"}' +
                            '\n ${desabilityPerson ? "Tipo de deficiência: ${desabilityType.firstWhereOrNull((value) => value.codTipoDeficiencia == this.widget._formViewModel.tipoDeficiencia)?.dscTipoDeficiencia}" : ""}' +
                            '\n\n Dados da mensagem' +
                            '\n ${isServiceChannel ? "Canal atendimento: ${serviceProtocolDataModel.firstWhereOrNull((value) => value.codCanalAtendimento == this.widget._formViewModel.canalAtendimento)?.dscCanalAtendimento}" : ""}' +
                            '\n ${"Tipo de mensagem: ${typeMessage.firstWhereOrNull((value) => value.id == this.widget._formViewModel.tipoMensagem)?.descricao}"}' +
                            '\n ${"Localidade: ${ocorrenceLocation.firstWhereOrNull((value) => value.id == this.widget._formViewModel.localidadeOcorrencia)?.descricao}"}' +
                            '\n Mensagem: ${this.widget._formViewModel.mensagem}',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: FontSizeManager.s14,
                          fontWeight: FontWeightManager.medium,
                        ),
                      ),
                      Text.rich(
                        TextSpan(
                          children: <TextSpan>[
                            TextSpan(
                              text: "Caso deseje prosseguir, clique em",
                            ),
                            TextSpan(text: " "),
                            TextSpan(
                              text: "Concluir",
                              style: TextStyle(
                                fontWeight: FontWeightManager.bold,
                              ),
                            ),
                          ],
                          style: TextStyle(fontSize: FontSizeManager.s12),
                        ),
                        textAlign: TextAlign.center,
                      ),
                      Text.rich(
                        TextSpan(
                          children: <TextSpan>[
                            TextSpan(
                              text: "Caso deseje modificar os dados, clique em",
                            ),
                            TextSpan(text: " "),
                            TextSpan(
                              text: "Voltar",
                              style: TextStyle(
                                fontWeight: FontWeightManager.bold,
                              ),
                            ),
                          ],
                          style: TextStyle(fontSize: FontSizeManager.s12),
                        ),
                        textAlign: TextAlign.center,
                      ),
                      SizedBox(height: 16.r),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.person_2_outlined,
                            color: ColorManager.outlineColor,
                          ),
                          SizedBox(width: 4.r),
                          Text(
                            "Dados pessoais",
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: ColorManager.outlineColor,
                              fontSize: FontSizeManager.s14,
                              fontWeight: FontWeightManager.bold,
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 15.r),
                      this._buildContentLine(
                        title: isLegalPerson ? "Nome do contato" : "Nome",
                        description: isLegalPerson
                            ? this
                                .widget
                                ._formViewModel
                                .nomeContatoAcionadorPessoaJuridica
                            : this
                                .widget
                                ._formViewModel
                                .nomeAcionadorPessoaFisica,
                      ),
                      this._buildContentLine(
                        title: isLegalPerson ? "CNPJ" : "CPF",
                        description: isLegalPerson
                            ? this
                                .widget
                                ._formViewModel
                                .cnpjAcionadorPessoaJuridica
                            : this
                                .widget
                                ._formViewModel
                                .cpfAcionadorPessoaFisica,
                      ),
                      if (isLegalPerson) ...[
                        this._buildContentLine(
                          title: "CPF",
                          description: this
                              .widget
                              ._formViewModel
                              .cpfAcionadorPessoaJuridica,
                        ),
                      ],
                      this._buildContentLine(
                        title: "E-mail",
                        description: this.widget._formViewModel.email ?? "",
                      ),
                      if (havePhone) ...[
                        this._buildContentLine(
                          title: "Telefone",
                          description: cellPhoneMaskFormatter
                              .maskText(this.widget._formViewModel.telefone!),
                        ),
                      ],
                      this._buildContentLine(
                        title: "Pessoa idosa",
                        description: oldPerson ? "Sim" : "Não",
                      ),
                      if (oldPerson) ...[
                        this._buildContentLine(
                          title: "Faixa etária",
                          description: oldPersonList
                                  .firstWhere(
                                    (GeneralListDataModel value) =>
                                        value.codFaixaEtaria ==
                                        this.widget._formViewModel.faixaEtaria,
                                    orElse: () => GeneralListDataModel(
                                      dscIntervFaixaEtaria: "-",
                                    ),
                                  )
                                  .dscIntervFaixaEtaria ??
                              "-",
                        ),
                      ],
                      this._buildContentLine(
                        title: "Pessoa com deficiência",
                        description: desabilityPerson ? "Sim" : "Não",
                      ),
                      if (desabilityPerson) ...[
                        this._buildContentLine(
                          title: "Tipo de deficiência",
                          description: desabilityType
                                  .firstWhere(
                                    (GeneralListDataModel value) =>
                                        value.codTipoDeficiencia ==
                                        this
                                            .widget
                                            ._formViewModel
                                            .tipoDeficiencia,
                                    orElse: () => GeneralListDataModel(
                                      dscTipoDeficiencia: "-",
                                    ),
                                  )
                                  .dscTipoDeficiencia ??
                              "-",
                        ),
                      ],
                      SizedBox(height: 15.r),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.book_outlined,
                            color: ColorManager.outlineColor,
                          ),
                          SizedBox(width: 4.r),
                          Text(
                            "Dados da mensagem",
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: ColorManager.outlineColor,
                              fontSize: FontSizeManager.s14,
                              fontWeight: FontWeightManager.bold,
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 15.r),
                      if (isServiceChannel) ...[
                        this._buildContentLine(
                          title: "Canal de atendimento",
                          description: serviceProtocolDataModel
                                  .firstWhere(
                                    (GeneralListDataModel value) =>
                                        value.codCanalAtendimento ==
                                        this
                                            .widget
                                            ._formViewModel
                                            .canalAtendimento,
                                    orElse: () => GeneralListDataModel(
                                      dscCanalAtendimento: "",
                                    ),
                                  )
                                  .dscCanalAtendimento ??
                              "",
                        ),
                      ],
                      this._buildContentLine(
                        title: "Tipo de mensagem",
                        description: typeMessage
                                .firstWhere(
                                  (GeneralListDataModel value) =>
                                      value.id ==
                                      this.widget._formViewModel.tipoMensagem,
                                  orElse: () => GeneralListDataModel(
                                    descricao: "",
                                  ),
                                )
                                .descricao ??
                            "",
                      ),
                      this._buildContentLine(
                        title: "Localidade",
                        description: ocorrenceLocation
                                .firstWhere(
                                  (GeneralListDataModel value) =>
                                      value.id ==
                                      this
                                          .widget
                                          ._formViewModel
                                          .localidadeOcorrencia,
                                  orElse: () => GeneralListDataModel(
                                    descricao: "",
                                  ),
                                )
                                .descricao ??
                            "",
                      ),
                      this._buildContentLine(
                        title: "Mensagem",
                        description: this.widget._formViewModel.mensagem ?? "",
                      ),
                    ],
                  ),
                ),
              ),
            ),
            SizedBox(height: 8.r),
            if (state.contentState == FormContentState.LOADING)
              CircularProgressIndicator(
                color: context.theme.primaryColor,
              ),
            SizedBox(height: 3.r),
            if (state.contentState != FormContentState.LOADING)
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: <Widget>[
                  Expanded(
                    child: SecondaryButton(
                      "Voltar",
                      onPressed: () async {
                        Get.focusScope?.unfocus();
                        this._cubit.decrementStep();

                        await this._cubit.saveContentInPrefs();
                      },
                    ),
                  ),
                  SizedBox(width: 16.r),
                  Expanded(
                    child: PrimaryButton(
                      "Confirmar",
                      onPressed: () async {
                        Get.focusScope?.unfocus();

                        await this._cubit.saveContentInPrefs();
                        await this._cubit.sendData();
                      },
                    ),
                  ),
                ],
              ),
          ],
        );
      },
    );
  }

  Widget _buildContentLine({
    required String title,
    required String description,
  }) {
    return Column(
      children: <Widget>[
        Row(
          children: <Widget>[
            Expanded(
              flex: 1,
              child: Text(
                title,
                textAlign: TextAlign.right,
                maxLines: 2,
                style: TextStyle(
                  fontSize: FontSizeManager.s10,
                  color: context.theme.hintColor,
                ),
              ),
            ),
            SizedBox(width: 12.r),
            Expanded(
              flex: 2,
              child: Text(
                description,
                maxLines: 2,
                softWrap: true,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                  fontSize: FontSizeManager.s12,
                  fontWeight: FontWeightManager.bold,
                ),
              ),
            ),
          ],
        ),
        SizedBox(height: 4.r),
        Divider(),
        SizedBox(height: 4.r),
      ],
    );
  }
}
