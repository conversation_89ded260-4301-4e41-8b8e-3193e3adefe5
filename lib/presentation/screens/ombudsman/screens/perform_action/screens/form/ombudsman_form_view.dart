import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:tjcemobile/domain/viewmodel/actuation_form_viewmodel.dart';
import 'package:tjcemobile/presentation/resources/routes_manager.dart';
import 'package:tjcemobile/presentation/screens/ombudsman/screens/perform_action/screens/form/cubit/ombudsman_perform_action_form_cubit.dart';
import 'package:tjcemobile/presentation/screens/ombudsman/screens/perform_action/screens/form/steps/confirmation/confirmation_step.dart';
import 'package:tjcemobile/presentation/screens/ombudsman/screens/perform_action/screens/form/steps/personal/personal_step.dart';
import 'package:tjcemobile/presentation/screens/ombudsman/screens/perform_action/screens/form/steps/type_message/type_message_step.dart';
import 'package:tjcemobile/presentation/widgets/arrow_back_component.dart';
import 'package:tjcemobile/presentation/widgets/custom_base_scaffold.dart';
import 'package:tjcemobile/presentation/widgets/error_component.dart';
import 'package:tjcemobile/presentation/widgets/step_progress_component.dart';

class OmbudsmanFormView extends StatefulWidget {
  final ActuationFormViewModel? _formViewModel;

  const OmbudsmanFormView({
    ActuationFormViewModel? formViewModel,
    Key? key,
  })  : this._formViewModel = formViewModel,
        super(key: key);

  @override
  State<OmbudsmanFormView> createState() => _OmbudsmanFormViewState();
}

class _OmbudsmanFormViewState extends State<OmbudsmanFormView> {
  final _cubit = Get.find<OmbudsmanPerformActionFormCubit>();

  final ValueNotifier<PageController> _pageControllerNotifier =
      ValueNotifier(PageController(initialPage: 0));

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await this._cubit.getListsTypes();

      if (this.widget._formViewModel != null) {
        this._cubit.continueFilForm(this.widget._formViewModel!);
      }
    });
  }

  @override
  void dispose() {
    Get.delete<OmbudsmanPerformActionFormCubit>(force: true);

    this._pageControllerNotifier.value.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OmbudsmanPerformActionFormCubit,
        OmbudsmanPerformActionFormState>(
      bloc: Get.find<OmbudsmanPerformActionFormCubit>(),
      builder: (BuildContext context, OmbudsmanPerformActionFormState state) {
        return CustomBaseScaffold(
          // coverage:ignore-start
          popScope: (bool value, dynamic result) async {
            await Get.offNamedUntil(
              Routes.ombudsmanRoute,
              (Route<dynamic> route) => value,
            );

            // coverage:ignore-end
          },
          appBar: this._buildAppBar(),
          body: this._buildBody(state),
        );
      },
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text("Ouvidoria"),
      leading: ArrowBackComponent(onPressed: () => Get.back()),
    );
  }

  Widget _buildBody(OmbudsmanPerformActionFormState state) {
    if (state.formContentState == FormContentState.LOADING) {
      return Center(
        child: CircularProgressIndicator(color: context.theme.primaryColor),
      );
    }

    if (state.formContentState == FormContentState.ERROR) {
      return ErrorComponent(failure: state.failure);
    }

    if (!this._pageControllerNotifier.value.hasClients) {
      this._pageControllerNotifier.value =
          PageController(initialPage: state.formViewModel?.currentStep ?? 0);
    }

    return ValueListenableBuilder(
      valueListenable: this._pageControllerNotifier,
      builder: (context, PageController pageController, _) {
        if (pageController.hasClients) {
          this.currentStep = state.formViewModel?.currentStep ?? 0;
        }

        return this._pageView(pageController, state);
      },
    );
  }

  Column _pageView(
    PageController pageController,
    OmbudsmanPerformActionFormState state,
  ) {
    return Column(
      children: <Widget>[
        if (pageController.hasClients) ...[
          // coverage:ignore-start
          Column(
            children: <Widget>[
              SizedBox(height: 8.r),
              Semantics(
                label: "",
                child: StepProgressComponent(
                  currentStep: state.formViewModel?.currentStep ?? 0,
                  titles: <String>[
                    "Mensagem",
                    "Dados",
                    "Confirmação",
                  ],
                ),
              ),
              SizedBox(height: 8.r),
            ],
          ),
        ],
        // coverage:ignore-end
        Expanded(
          child: PageView(
            allowImplicitScrolling: false,
            physics: NeverScrollableScrollPhysics(),
            controller: pageController,
            children: <Widget>[
              TypeMessageStep(
                viewModel: state.formViewModel,
              ),
              PersonalStep(
                viewModel: state.formViewModel,
              ),
              ConfirmationStep(
                formViewModel: state.formViewModel!,
              ),
            ],
          ),
        ),
        SizedBox(height: 8.r),
      ],
    );
  }

  set currentStep(int currentStep) {
    this._pageControllerNotifier.value.jumpToPage(currentStep);
  }
}
