import 'package:dio/dio.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:http_mock_adapter/http_mock_adapter.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tjcemobile/data/data_source/remote/pje_log_remote_data_source.dart';
import 'package:tjcemobile/data/model/pje_log_model.dart';
import 'package:tjcemobile/data/network/pje_log_service/pje_log_service.dart';

import '../../../../../mocks/app/flavor_config_mock.dart';
import '../../../../../mocks/viewmodel/pje_log_viewmodel_mock.dart';

void main() {
  final Dio dioMock = Dio();
  late DioAdapter dioAdapter;

  late PjeLogRemoteDataSource dataSource;

  setUpAll(() async {
    TestWidgetsFlutterBinding.ensureInitialized();

    registerFallbackValue(Uri());

    dioAdapter = DioAdapter(dio: dioMock, matcher: const UrlRequestMatcher());

    dioMock.httpClientAdapter = dioAdapter;

    Get.lazyPut<PjeLogService>(
      () => PjeLogService(
        dioMock,
        baseUrl: flavorConfigMock.values.mobileBaseUrl,
      ),
      fenix: true,
    );

    Get.lazyPut<PjeLogRemoteDataSource>(
      () => PjeLogRemoteDataSourceImpl(Get.find()),
      fenix: true,
    );
  });

  test("postPjeLog is called", () async {
    dataSource = Get.find<PjeLogRemoteDataSource>();

    dioAdapter.onPost(
      '/pje-logs',
      (request) => request.reply(200, {}),
    );

    final content = await dataSource.postPjeLog(pjeLogViewModelMock);

    expect(content, isA<PjeLogModel>());
  });
}
