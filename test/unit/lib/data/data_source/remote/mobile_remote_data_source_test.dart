import 'package:dio/dio.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:http_mock_adapter/http_mock_adapter.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tjcemobile/data/data_source/remote/mobile_remote_data_source.dart';
import 'package:tjcemobile/data/model/auth_token_model.dart';
import 'package:tjcemobile/data/model/response_interceptor_model.dart';
import 'package:tjcemobile/data/network/mobile_service/mobile_service.dart';

import '../../../../../mocks/app/flavor_config_mock.dart';
import '../../../../../mocks/viewmodel/auth_token_viewmodel_mock.dart';
import '../../../../../mocks/viewmodel/logger_client_viewmodel_mock.dart';

void main() {
  final Dio dioMock = Dio();
  late DioAdapter dioAdapter;

  late MobileRemoteDataSource dataSource;

  setUpAll(() async {
    TestWidgetsFlutterBinding.ensureInitialized();

    registerFallbackValue(Uri());

    dioAdapter = DioAdapter(dio: dioMock, matcher: const UrlRequestMatcher());

    dioMock.httpClientAdapter = dioAdapter;

    Get.lazyPut<MobileService>(
      () => MobileService(
        dioMock,
        baseUrl: flavorConfigMock.values.mobileBaseUrl,
      ),
      fenix: true,
    );

    Get.lazyPut<MobileRemoteDataSource>(
      () => MobileRemoteDataSourceImpl(Get.find()),
      fenix: true,
    );
  });

  test("postAuthToken is called", () async {
    dataSource = Get.find<MobileRemoteDataSource>();

    dioAdapter.onPost(
      '/auth/token',
      (request) => request.reply(200, {}),
    );

    final content = await dataSource.postAuthToken(authTokenViewModelMock);

    expect(content, isA<AuthTokenModel>());
  });

  test("postLoggerClientError is called", () async {
    dataSource = Get.find<MobileRemoteDataSource>();

    dioAdapter.onPost(
      '/mobile-log',
      (request) => request.reply(200, {}),
    );

    final content =
        await dataSource.postLoggerClientError(loggerClientViewModelMock);

    expect(content, isA<ResponseInterceptorModel>());
  });
}
