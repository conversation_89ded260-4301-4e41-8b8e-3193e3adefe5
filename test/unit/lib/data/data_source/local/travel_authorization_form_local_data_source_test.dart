import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:mocktail/mocktail.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:tjcemobile/app/app_preferences.dart';
import 'package:tjcemobile/data/data_source/local/audience_pdf_local_data_source.dart';
import 'package:tjcemobile/data/data_source/local/travel_authorization_form_local_data_source.dart';
import 'package:tjcemobile/data/network/dio_factory.dart';
import 'package:tjcemobile/domain/viewmodel/travel_authorization_form_viewmodel.dart';

import '../../../../../mocks/viewmodel/travel_authorization_form_viewmodel_mock.dart';

class AppPreferencesMock extends Mock implements AppPreferences {}

class DataSourceMock extends Mock
    implements TravelAuthorizationFormLocalDataSource {}

void main() {
  late SharedPreferences sharedPreferences;
  late AppPreferences appPreferences;
  late TravelAuthorizationFormLocalDataSource dataSource;

  setUpAll(() async {
    TestWidgetsFlutterBinding.ensureInitialized();

    registerFallbackValue(Uri());

    // sharedPrefs
    SharedPreferences.setMockInitialValues({});
    sharedPreferences = await SharedPreferences.getInstance();

    Get.put<SharedPreferences>(sharedPreferences, permanent: true);

    // app prefs getIt
    Get.put<AppPreferences>(AppPreferences(Get.find()), permanent: true);

    Get.put<DioFactory>(DioFactory(Get.find()), permanent: true);

    Get.lazyPut<TravelAuthorizationFormLocalDataSource>(
      () => TravelAuthorizationFormLocalDataSourceImpl(),
      fenix: true,
    );
  });

  test(
      "setTravelAuthorizationFormViewModel() should save the accident deal form model to the preferences",
      () async {
    dataSource = Get.find<TravelAuthorizationFormLocalDataSource>();

    await dataSource.setTravelAuthorizationFormViewModel(
        travelAuthorizationFormViewModelMock);

    expect(
      dataSource.setTravelAuthorizationFormViewModel(
          travelAuthorizationFormViewModelMock),
      isA<void>(),
    );
  });

  test(
      "setTravelAuthorizationFormViewModel() should return a list of accident deal form models",
      () async {
    dataSource = Get.find<TravelAuthorizationFormLocalDataSource>();

    List<TravelAuthorizationFormViewModel> matcher = [
      travelAuthorizationFormViewModelMock,
    ];

    await dataSource.setTravelAuthorizationFormViewModel(
        travelAuthorizationFormViewModelMock);

    travelAuthorizationFormViewModelMock.currentStep = 0;

    await dataSource.setTravelAuthorizationFormViewModel(
        travelAuthorizationFormViewModelMock);

    await dataSource.getTravelAuthorizationFormViewModelList();

    // Assert
    expect(
      matcher,
      [
        travelAuthorizationFormViewModelMock,
      ],
    );
  });

  test(
      "getTravelAuthorizationFormViewModelList() should return a list of accident deal form models",
      () async {
    dataSource = Get.find<TravelAuthorizationFormLocalDataSource>();

    List<TravelAuthorizationFormViewModel> matcher = [
      travelAuthorizationFormViewModelMock
    ];

    await dataSource.getTravelAuthorizationFormViewModelList();

    // Assert
    expect(
      matcher,
      [
        travelAuthorizationFormViewModelMock,
      ],
    );
  });

  test(
      "removeFromDataSource() should remove the accident deal form model from the preferences",
      () async {
    appPreferences = Get.find<AppPreferences>();
    dataSource = Get.find<TravelAuthorizationFormLocalDataSource>();

    await dataSource.setTravelAuthorizationFormViewModel(
        travelAuthorizationFormViewModelMock);

    await dataSource.removeFromDataSource();

    // Assert
    final preferencesData = await appPreferences.getLocalData();
    expect(preferencesData[AUDIENCE_PDF_KEY], null);
  });
}
