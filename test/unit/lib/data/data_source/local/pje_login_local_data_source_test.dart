import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:mocktail/mocktail.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:tjcemobile/app/app_preferences.dart';
import 'package:tjcemobile/data/data_source/local/audience_pdf_local_data_source.dart';
import 'package:tjcemobile/data/data_source/local/pje_login_local_data_source.dart';
import 'package:tjcemobile/data/network/dio_factory.dart';
import 'package:tjcemobile/domain/viewmodel/pje_process_mni_viewmodel.dart';

import '../../../../../mocks/viewmodel/pje_process_mni_viewmodel_mock.dart';

class AppPreferencesMock extends Mock implements AppPreferences {}

class DataSourceMock extends Mock implements PjeLoginLocalDataSource {}

void main() {
  late SharedPreferences sharedPreferences;
  late AppPreferences appPreferences;
  late PjeLoginLocalDataSource dataSource;

  setUpAll(() async {
    TestWidgetsFlutterBinding.ensureInitialized();

    registerFallbackValue(Uri());

    // sharedPrefs
    SharedPreferences.setMockInitialValues({});
    sharedPreferences = await SharedPreferences.getInstance();

    Get.put<SharedPreferences>(sharedPreferences, permanent: true);

    // app prefs getIt
    Get.put<AppPreferences>(AppPreferences(Get.find()), permanent: true);

    Get.put<DioFactory>(DioFactory(Get.find()), permanent: true);

    Get.lazyPut<PjeLoginLocalDataSource>(
      () => PjeLoginLocalDataSourceImpl(),
      fenix: true,
    );
  });

  test(
      "setPjeLoginViewModel() should save the accident deal form model to the preferences",
      () async {
    dataSource = Get.find<PjeLoginLocalDataSource>();

    await dataSource.setPjeLoginViewModel(pjeLoginViewModelMock);

    expect(
      dataSource.setPjeLoginViewModel(pjeLoginViewModelMock),
      isA<void>(),
    );
  });

  test(
      "setPjeLoginViewModel() should return a list of accident deal form models",
      () async {
    dataSource = Get.find<PjeLoginLocalDataSource>();

    List<PjeLoginViewModel> matcher = [
      pjeLoginViewModelMock,
    ];

    await dataSource.setPjeLoginViewModel(pjeLoginViewModelMock);

    await dataSource.getPjeLoginViewModelList();

    // Assert
    expect(
      matcher,
      [
        pjeLoginViewModelMock,
      ],
    );
  });

  test(
      "getPjeLoginViewModelList() should return a list of accident deal form models",
      () async {
    dataSource = Get.find<PjeLoginLocalDataSource>();

    List<PjeLoginViewModel> matcher = [pjeLoginViewModelMock];

    await dataSource.getPjeLoginViewModelList();

    // Assert
    expect(
      matcher,
      [
        pjeLoginViewModelMock,
      ],
    );
  });

  test(
      "removeFromDataSource() should remove the accident deal form model from the preferences",
      () async {
    appPreferences = Get.find<AppPreferences>();
    dataSource = Get.find<PjeLoginLocalDataSource>();

    await dataSource.setPjeLoginViewModel(pjeLoginViewModelMock);

    await dataSource.removeFromDataSource();

    // Assert
    final preferencesData = await appPreferences.getLocalData();
    expect(preferencesData[AUDIENCE_PDF_KEY], null);
  });
}
