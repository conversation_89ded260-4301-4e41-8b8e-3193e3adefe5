import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:mocktail/mocktail.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:tjcemobile/app/app_preferences.dart';
import 'package:tjcemobile/data/data_source/local/pje_login_local_data_source.dart';
import 'package:tjcemobile/data/network/dio_factory.dart';
import 'package:tjcemobile/data/network/failure.dart';
import 'package:tjcemobile/data/repository_impl/local/pje_login_local_repository_impl.dart';
import 'package:tjcemobile/domain/repository/local/pje_login_local_repository.dart';
import 'package:tjcemobile/domain/viewmodel/pje_process_mni_viewmodel.dart';

import '../../../../../mocks/viewmodel/pje_process_mni_viewmodel_mock.dart';

class RepositoryMock extends Mock implements PjeLoginLocalRepository {}

class DioAdapterMock extends Mock implements HttpClientAdapter {}

void main() {
  late SharedPreferences sharedPreferences;

  late RepositoryMock repositoryMock;
  late PjeLoginLocalRepository repository;

  setUpAll(() async {
    TestWidgetsFlutterBinding.ensureInitialized();

    registerFallbackValue(Uri());

    SharedPreferences.setMockInitialValues({});
    sharedPreferences = await SharedPreferences.getInstance();

    Get.put<SharedPreferences>(sharedPreferences, permanent: true);

    Get.put<AppPreferences>(AppPreferences(Get.find()), permanent: true);

    Get.put<DioFactory>(DioFactory(Get.find()), permanent: true);

    Get.lazyPut<PjeLoginLocalDataSource>(
      () => PjeLoginLocalDataSourceImpl(),
      fenix: true,
    );

    Get.lazyPut<PjeLoginLocalRepository>(
      () => PjeLoginLocalRepositoryImpl(Get.find()),
      fenix: true,
    );

    repositoryMock = RepositoryMock();
  });

  test('verify setPjeLoginViewModel() is executed', () async {
    repository = Get.find<PjeLoginLocalRepository>();

    final Either<Failure, void> matcher = Right(null);

    when(() => repositoryMock.setPjeLoginViewModel(pjeLoginViewModelMock))
        .thenAnswer((_) async => matcher);

    final actual = await repository.setPjeLoginViewModel(pjeLoginViewModelMock);

    actual.fold(
      (l) => expect(l, isA<Failure>()),
      (r) => expect(null, isNull),
    );
  });

  test('verify getPjeLoginViewModelList() is executed', () async {
    repository = Get.find<PjeLoginLocalRepository>();

    final Either<Failure, List<PjeLoginViewModel>> matcher = Right([]);

    when(() => repositoryMock.getPjeLoginViewModelList())
        .thenAnswer((_) async => matcher);

    final actual = await repository.getPjeLoginViewModelList();

    actual.fold(
      (l) => expect(l, isA<Failure>()),
      (r) => expect(r, isA<List<PjeLoginViewModel>>()),
    );
  });

  test('verify removeFromDataSource is executed', () async {
    repository = Get.find<PjeLoginLocalRepository>();

    final Either<Failure, void> matcher = Right(null);

    when(() => repositoryMock.removeFromDataSource())
        .thenAnswer((_) async => matcher);

    final actual = await repository.removeFromDataSource();

    actual.fold(
      (l) => expect(l, isA<Failure>()),
      (r) => expect(null, isNull),
    );
  });
}
