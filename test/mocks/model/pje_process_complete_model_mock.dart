import 'package:tjcemobile/data/model/pje_process_complete_model.dart';

final pjeProcessCompleteModelMock = PjeProcessCompleteModel(
  data: PjeProcessCompleteDataModel(
    idProcesso: '12345',
    grau: '1',
    processo: Processo(
      dadosBasicos: DadosBasicos(
        polo: [
          Polo(
            parte: [
              Parte(
                pessoa: Pessoa(
                  nome: 'Nome da Pessoa',
                  dataNascimento: Value(value: '01/01/2000'),
                  tipoPessoa: 'Física',
                ),
                advogado: [
                  Advogado(
                    nome: 'Nome do Advogado',
                    intimacao: true,
                    inscricao: Value(value: 'OAB12345'),
                    tipoRepresentante: 'Defensor',
                  ),
                ],
              ),
            ],
            polo: 'Ativo',
          ),
        ],
        magistradoAtuante: [Value(value: '<PERSON>iz <PERSON>')],
        valorCausa: 10000,
        orgaoJulgador: OrgaoJulgador(
          codigoOrgao: '001',
          nomeOrgao: 'Tribunal X',
          instancia: 'Primeira',
          codigoMunicipioIBGE: 123456,
        ),
        numero: Value(value: '1234567890'),
        competencia: 1,
        classeProcessual: 101,
        codigoLocalidade: 'Localidade X',
        nivelSigilo: 0,
        intervencaoMP: false,
        tamanhoProcesso: 200,
        dataAjuizamento: Value(value: '01/01/2021'),
      ),
      movimento: [
        Movimento(
          movimentoNacional: MovimentoNacional(
            complemento: [Value(value: 'Complemento')],
            codigoNacional: 1,
          ),
          dataHora: Value(value: '01/01/2021 10:00:00'),
          identificadorMovimento: 'Mov123',
        ),
      ],
      documento: [
        DocumentoDataModel(
          numeroProcesso: '12345',
          grau: '1',
          conteudo: Conteudo(
            include: '',
          ),
          idDocumento: 'Doc123',
          tipoDocumento: 'Tipo X',
          dataHora: Value(value: '01/01/2021 10:00:00'),
          mimetype: 'application/pdf',
          nivelSigilo: 0,
          movimento: "",
          hash: 'hash123',
          descricao: 'Descrição do Documento',
        ),
      ],
    ),
  ),
);
