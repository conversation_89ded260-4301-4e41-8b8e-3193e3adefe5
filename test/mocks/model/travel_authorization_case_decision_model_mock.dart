import 'package:tjcemobile/data/model/travel_authorization_case_decision_model.dart';

final travelAuthorizationCaseDecisionModelMock =
    TravelAuthorizationCaseDecisionModel(
  national: NationalModel(
    dismissal: [
      CaseDecisionModel(
        title: 'Dismissal Title 1',
        subTitle: 'Dismissal SubTitle 1',
        foundation: FoundationModel(
          title: 'Foundation Title 1',
          content: 'Foundation Content 1',
        ),
      ),
    ],
    registry: [
      CaseDecisionModel(
        title: 'Registry Title 1',
        subTitle: 'Registry SubTitle 1',
        foundation: FoundationModel(
          title: 'Foundation Title 2',
          content: 'Foundation Content 2',
        ),
      ),
    ],
  ),
  international: InternationalModel(
    dismissal: [
      CaseDecisionModel(
        title: 'Dismissal Title 2',
        subTitle: 'Dismissal SubTitle 2',
        foundation: FoundationModel(
          title: 'Foundation Title 3',
          content: 'Foundation Content 3',
        ),
      ),
    ],
    action: [
      CaseDecisionModel(
        title: 'Action Title 1',
        subTitle: 'Action SubTitle 1',
        foundation: FoundationModel(
          title: 'Foundation Title 4',
          content: 'Foundation Content 4',
        ),
      ),
    ],
    registry: [
      CaseDecisionModel(
        title: 'Registry Title 2',
        subTitle: 'Registry SubTitle 2',
        foundation: FoundationModel(
          title: 'Foundation Title 5',
          content: 'Foundation Content 5',
        ),
      ),
    ],
  ),
  mercosul: InternationalModel(
    dismissal: [
      CaseDecisionModel(
        title: 'Dismissal Title 3',
        subTitle: 'Dismissal SubTitle 3',
        foundation: FoundationModel(
          title: 'Foundation Title 6',
          content: 'Foundation Content 6',
        ),
      ),
    ],
    action: [
      CaseDecisionModel(
        title: 'Action Title 2',
        subTitle: 'Action SubTitle 2',
        foundation: FoundationModel(
          title: 'Foundation Title 7',
          content: 'Foundation Content 7',
        ),
      ),
    ],
    registry: [
      CaseDecisionModel(
        title: 'Registry Title 3',
        subTitle: 'Registry SubTitle 3',
        foundation: FoundationModel(
          title: 'Foundation Title 8',
          content: 'Foundation Content 8',
        ),
      ),
    ],
  ),
);
