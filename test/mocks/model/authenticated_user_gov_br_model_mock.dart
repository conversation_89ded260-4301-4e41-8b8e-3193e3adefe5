import 'package:tjcemobile/data/model/authenticated_user_gov_br_model.dart';

final AuthenticatedUserGovBrModel authenticatedUserGovBrModelMock =
    AuthenticatedUserGovBrModel(
  data: AuthenticatedUserGovBrDataModel(
    sub: "sub",
    socialName: "socialName",
    emailVerified: "emailVerified",
    amr: ["amr"],
    profile: "profile",
    kid: "kid",
    iss: "iss",
    phoneNumberVerified: "phoneNumberVerified",
    preferredUsername: "preferredUsername",
    nonce: "nonce",
    picture: "picture",
    aud: "aud",
    authTime: 1,
    scope: ["scope"],
    name: "name",
    phoneNumber: "phoneNumber",
    exp: 1,
    iat: 1,
    jti: "jti",
    email: "email",
  ),
);
