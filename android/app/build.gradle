plugins {
    id "com.android.application"
    id "kotlin-android"

    id "dev.flutter.flutter-gradle-plugin"

    id 'com.google.gms.google-services'
}

// Load keystore properties
def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

// Load local properties
def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode') ?: '1'
def flutterVersionName = localProperties.getProperty('flutter.versionName') ?: '1.0'

android {
    namespace "br.jus.tjce.tjcemobile"

    compileSdk 35
    ndkVersion "27.0.12077973"

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8

        coreLibraryDesugaringEnabled true
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        applicationId "br.jus.tjce.tjcemobile"

        minSdk = 23
        targetSdk = flutter.targetSdkVersion

        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName

        multiDexEnabled true
    }

    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile file(keystoreProperties['storeFile'])
            storePassword keystoreProperties['storePassword']
        }
    }

    buildTypes {
        release {
            minifyEnabled true

            signingConfig signingConfigs.release
        }
    }

    flavorDimensions "flavors"
    productFlavors {
        dev {
            dimension "flavors"
        }

        hml {
            dimension "flavors"
        }

        prod {
            dimension "flavors"
        }
    }

//    java {
//        toolchain {
//            languageVersion = JavaLanguageVersion.of(23)
//        }
//    }
}

flutter {
    source '../..'
}

dependencies {
    // Import the Firebase BoM
    implementation platform('com.google.firebase:firebase-bom:33.1.2')
    implementation 'com.google.firebase:firebase-analytics'

    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.1.4'
}
