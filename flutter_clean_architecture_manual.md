# Manual de Arquitetura Flutter - Clean Architecture

## 📋 Índice
1. [<PERSON><PERSON><PERSON> Geral](#visão-geral)
2. [Estrutura de Pastas](#estrutura-de-pastas)
3. [Camadas da Arquitetura](#camadas-da-arquitetura)
4. [Configuração Inicial](#configuração-inicial)
5. [Padrões e Convenções](#padrões-e-convenções)
6. [Gerenciamento de Estado](#gerenciamento-de-estado)
7. [Networking e APIs](#networking-e-apis)
8. [Testes](#testes)
9. [Configuração de Ambientes](#configuração-de-ambientes)
10. [Boas Práticas](#boas-práticas)

## 🎯 Visão Geral

Este manual apresenta uma arquitetura robusta baseada em **Clean Architecture** para aplicações Flutter, garantindo:
- **Separação clara de responsabilidades**
- **Testabilidade e manutenibilidade**
- **Escalabilidade e flexibilidade**
- **Independência de frameworks externos**

### Tecnologias Base
- **Flutter SDK**: >=3.1.0
- **Arquitetura**: Clean Architecture
- **Estado**: BLoC/Cubit + GetX
- **Networking**: Dio + Retrofit
- **Injeção de Dependência**: GetX
- **Testes**: Mocktail + BLoC Test

## 📁 Estrutura de Pastas

```
lib/
├── app/                          # Configuração da aplicação
│   ├── app.dart                  # Widget principal da aplicação
│   ├── di.dart                   # Injeção de dependências
│   ├── flavor.dart               # Configuração de ambientes
│   ├── app_preferences.dart      # Preferências da aplicação
│   ├── extensions.dart           # Extensões globais
│   ├── firebase/                 # Configurações Firebase
│   │   ├── firebase_analytics_handler.dart
│   │   ├── firebase_notifications_handler.dart
│   │   └── firebase_remote_config_preferences.dart
│   └── shared/                   # Utilitários compartilhados
│       ├── enum/                 # Enumerações globais
│       ├── utils/                # Utilitários diversos
│       └── input_formatter/      # Formatadores de entrada
├── data/                         # Camada de dados
│   ├── data_source/              # Fontes de dados
│   │   ├── local/                # Dados locais (SharedPreferences, DB)
│   │   └── remote/               # Dados remotos (APIs)
│   ├── interceptors/             # Interceptadores HTTP
│   ├── model/                    # Modelos de dados
│   ├── network/                  # Serviços de rede
│   │   ├── dio_factory.dart      # Configuração do Dio
│   │   ├── error_handler.dart    # Tratamento de erros
│   │   ├── failure.dart          # Classe de falhas
│   │   └── services/             # Serviços REST
│   ├── repository_impl/          # Implementações de repositórios
│   │   ├── local/                # Repositórios locais
│   │   └── remote/               # Repositórios remotos
│   └── response_parser/          # Parsers de resposta
├── domain/                       # Camada de domínio
│   ├── repository/               # Interfaces de repositórios
│   │   ├── local/                # Repositórios locais
│   │   └── remote/               # Repositórios remotos
│   ├── usecase/                  # Casos de uso
│   │   ├── base_usecase.dart     # Caso de uso base
│   │   └── feature_modules/      # Casos de uso por funcionalidade
│   └── viewmodel/                # ViewModels (DTOs)
├── presentation/                 # Camada de apresentação
│   ├── dialogs/                  # Diálogos customizados
│   ├── resources/                # Recursos de UI
│   │   ├── assets_manager.dart   # Gerenciador de assets
│   │   ├── color_manager.dart    # Gerenciador de cores
│   │   ├── font_manager.dart     # Gerenciador de fontes
│   │   ├── icons_manager.dart    # Gerenciador de ícones
│   │   ├── language_manager.dart # Gerenciador de idiomas
│   │   ├── routes_manager.dart   # Gerenciador de rotas
│   │   ├── strings_manager.dart  # Gerenciador de strings
│   │   ├── styles_manager.dart   # Gerenciador de estilos
│   │   ├── theme_manager.dart    # Gerenciador de temas
│   │   └── values_manager.dart   # Valores constantes
│   ├── screens/                  # Telas da aplicação
│   │   └── feature_name/         # Telas por funcionalidade
│   │       ├── feature_view.dart # Tela principal
│   │       ├── components/       # Componentes específicos
│   │       ├── cubit/           # Cubit da funcionalidade
│   │       └── screens/         # Sub-telas
│   └── widgets/                  # Widgets reutilizáveis
├── firebase_options.dart         # Configurações Firebase
├── main_dev.dart                # Entry point desenvolvimento
├── main_hml.dart                # Entry point homologação
└── main_prod.dart               # Entry point produção

assets/                          # Assets da aplicação
├── fonts/                       # Fontes customizadas
├── icons/                       # Ícones
├── images/                      # Imagens
├── translations/                # Arquivos de tradução
└── data/                        # Dados estáticos

test/                           # Testes
├── mocks/                      # Mocks para testes
├── unit/                       # Testes unitários
└── integration_test/           # Testes de integração

android/                        # Configurações Android
ios/                           # Configurações iOS
```

## 🏗️ Camadas da Arquitetura

### 1. **App Layer** (`lib/app/`)
**Responsabilidade**: Configuração geral da aplicação

#### Arquivos Principais:
- **`app.dart`**: Widget raiz da aplicação
- **`di.dart`**: Configuração de injeção de dependências
- **`flavor.dart`**: Configuração de ambientes
- **`app_preferences.dart`**: Gerenciamento de preferências

### 2. **Presentation Layer** (`lib/presentation/`)
**Responsabilidade**: Interface do usuário e interação

#### Estrutura:
- **Screens**: Organizadas por funcionalidade
- **Widgets**: Componentes reutilizáveis
- **Cubits**: Gerenciamento de estado
- **Resources**: Temas, cores, strings, rotas

### 3. **Domain Layer** (`lib/domain/`)
**Responsabilidade**: Lógica de negócio pura

#### Componentes:
- **Use Cases**: Casos de uso específicos
- **Repository Interfaces**: Contratos para acesso a dados
- **ViewModels**: Modelos de transferência de dados

### 4. **Data Layer** (`lib/data/`)
**Responsabilidade**: Acesso e manipulação de dados

#### Componentes:
- **Data Sources**: Local e Remote
- **Repository Implementations**: Implementações concretas
- **Network Services**: Serviços REST
- **Models**: Modelos de dados com serialização

## ⚙️ Configuração Inicial

### 1. **Dependências Base (pubspec.yaml)**

```yaml
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # Estado e Navegação
  flutter_bloc: ^9.1.0
  get: ^4.6.1
  equatable: ^2.0.5

  # Networking
  dio: ^5.7.0
  retrofit: ^4.4.2
  json_annotation: ^4.9.0
  pretty_dio_logger: ^1.3.1

  # Utilitários
  dartz: ^0.10.1
  shared_preferences: ^2.2.2
  package_info_plus: ^8.0.1

  # UI
  flutter_screenutil: ^5.9.0
  auto_size_text: ^3.0.0

  # Internacionalização
  easy_localization: ^3.0.0
  intl: ^0.19.0

dev_dependencies:
  # Testes
  test: ^1.24.9
  bloc_test: ^10.0.0
  mocktail: ^1.0.1
  http_mock_adapter: ^0.6.0

  # Code Generation
  build_runner: ^2.4.6
  json_serializable: ^6.7.1
  retrofit_generator: '>=9.0.0 <10.0.0'
```

### 2. **Configuração de Flavors**

#### `lib/app/flavor.dart`
```dart
enum Flavor { DEV, HML, PROD }

class FlavorValues {
  final String baseUrl;
  final String apiKey;
  final bool enableLogging;

  FlavorValues({
    required this.baseUrl,
    required this.apiKey,
    required this.enableLogging,
  });
}

class FlavorConfig {
  static FlavorConfig? _instance;

  final Flavor flavor;
  final String name;
  final Color color;
  final FlavorValues values;

  factory FlavorConfig({
    required Flavor flavor,
    Color color = Colors.blue,
    required FlavorValues values,
  }) {
    _instance ??= FlavorConfig._internal(
      flavor,
      flavor.toString().split('.').last,
      color,
      values,
    );
    return _instance!;
  }

  FlavorConfig._internal(this.flavor, this.name, this.color, this.values);

  static FlavorConfig get instance => _instance!;
  static bool get isDevelopment => _instance!.flavor == Flavor.DEV;
  static bool get isProduction => _instance!.flavor == Flavor.PROD;
}
```

### 3. **Entry Points por Ambiente**

#### `lib/main_dev.dart`
```dart
import 'package:flutter/material.dart';
import 'app/app.dart';
import 'app/di.dart';
import 'app/flavor.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  FlavorConfig(
    flavor: Flavor.DEV,
    color: Colors.red,
    values: FlavorValues(
      baseUrl: 'https://api-dev.example.com',
      apiKey: 'dev-api-key',
      enableLogging: true,
    ),
  );

  await initAppModule();
  runApp(App());
}
```

## 🔄 Gerenciamento de Estado

### 1. **Base Use Case**

#### `lib/domain/usecase/base_usecase.dart`
```dart
import 'package:dartz/dartz.dart';
import '../../../data/network/failure.dart';

abstract class BaseUseCase<In, Out> {
  Future<Either<Failure, Out>> execute(In input);
}
```

### 2. **Estrutura de Cubit**

#### `lib/presentation/screens/feature/cubit/feature_cubit.dart`
```dart
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

part 'feature_state.dart';

class FeatureCubit extends Cubit<FeatureState> {
  final FeatureUseCase _useCase;

  FeatureCubit(this._useCase) : super(FeatureState());

  Future<void> loadData() async {
    emit(state.copyWith(status: FeatureStatus.loading));

    final result = await _useCase.execute(NoParams());

    result.fold(
      (failure) => emit(state.copyWith(
        status: FeatureStatus.error,
        failure: failure,
      )),
      (data) => emit(state.copyWith(
        status: FeatureStatus.success,
        data: data,
      )),
    );
  }
}
```

#### `lib/presentation/screens/feature/cubit/feature_state.dart`
```dart
part of 'feature_cubit.dart';

enum FeatureStatus { initial, loading, success, error }

class FeatureState extends Equatable {
  final FeatureStatus status;
  final List<DataModel>? data;
  final Failure? failure;

  const FeatureState({
    this.status = FeatureStatus.initial,
    this.data,
    this.failure,
  });

  FeatureState copyWith({
    FeatureStatus? status,
    List<DataModel>? data,
    Failure? failure,
  }) {
    return FeatureState(
      status: status ?? this.status,
      data: data ?? this.data,
      failure: failure ?? this.failure,
    );
  }

  @override
  List<Object?> get props => [status, data, failure];
}
```

## 🌐 Networking e APIs

### 1. **Dio Factory**

#### `lib/data/network/dio_factory.dart`
```dart
import 'package:dio/dio.dart';
import 'package:pretty_dio_logger/pretty_dio_logger.dart';
import '../../app/flavor.dart';

class DioFactory {
  static Dio? _dio;

  static Dio getDio() {
    Duration timeOut = const Duration(milliseconds: 60000);

    if (_dio == null) {
      _dio = Dio();

      _dio!.options = BaseOptions(
        baseUrl: FlavorConfig.instance.values.baseUrl,
        receiveTimeout: timeOut,
        sendTimeout: timeOut,
        connectTimeout: timeOut,
      );

      if (FlavorConfig.instance.values.enableLogging) {
        _dio!.interceptors.add(PrettyDioLogger(
          requestHeader: true,
          requestBody: true,
          responseHeader: true,
        ));
      }

      return _dio!;
    } else {
      return _dio!;
    }
  }
}
```

### 2. **Service Template**

#### `lib/data/network/services/api_service.dart`
```dart
import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import '../../model/data_model.dart';

part 'api_service.g.dart';

@RestApi()
abstract class ApiService {
  factory ApiService(Dio dio, {String baseUrl}) = _ApiService;

  @GET("/endpoint")
  Future<HttpResponse<List<DataModel>>> getData();

  @POST("/endpoint")
  Future<HttpResponse<DataModel>> postData(@Body() DataModel data);

  @PUT("/endpoint/{id}")
  Future<HttpResponse<DataModel>> updateData(
    @Path("id") int id,
    @Body() DataModel data,
  );

  @DELETE("/endpoint/{id}")
  Future<HttpResponse<void>> deleteData(@Path("id") int id);
}
```

### 3. **Error Handler**

#### `lib/data/network/error_handler.dart`
```dart
import 'package:dio/dio.dart';
import 'failure.dart';

class ErrorHandler implements Exception {
  late Failure failure;

  ErrorHandler.handle(dynamic error) {
    if (error is DioException) {
      failure = _handleError(error);
    } else {
      failure = DataSource.DEFAULT.getFailure();
    }
  }
}

Failure _handleError(DioException error) {
  switch (error.type) {
    case DioExceptionType.connectionTimeout:
      return DataSource.CONNECT_TIMEOUT.getFailure();
    case DioExceptionType.sendTimeout:
      return DataSource.SEND_TIMEOUT.getFailure();
    case DioExceptionType.receiveTimeout:
      return DataSource.RECIEVE_TIMEOUT.getFailure();
    case DioExceptionType.badResponse:
      return _handleResponseError(error.response?.statusCode);
    case DioExceptionType.cancel:
      return DataSource.CANCEL.getFailure();
    default:
      return DataSource.DEFAULT.getFailure();
  }
}

Failure _handleResponseError(int? statusCode) {
  switch (statusCode) {
    case ResponseCode.BAD_REQUEST:
      return DataSource.BAD_REQUEST.getFailure();
    case ResponseCode.UNAUTORISED:
      return DataSource.UNAUTORISED.getFailure();
    case ResponseCode.FORBIDDEN:
      return DataSource.FORBIDDEN.getFailure();
    case ResponseCode.NOT_FOUND:
      return DataSource.NOT_FOUND.getFailure();
    case ResponseCode.INTERNAL_SERVER_ERROR:
      return DataSource.INTERNAL_SERVER_ERROR.getFailure();
    default:
      return DataSource.DEFAULT.getFailure();
  }
}
```

### 4. **Failure Classes**

#### `lib/data/network/failure.dart`
```dart
import 'package:equatable/equatable.dart';

class Failure extends Equatable {
  final int code;
  final String message;

  const Failure(this.code, this.message);

  @override
  List<Object> get props => [code, message];
}

class ResponseCode {
  static const int SUCCESS = 200;
  static const int NO_CONTENT = 201;
  static const int BAD_REQUEST = 400;
  static const int UNAUTORISED = 401;
  static const int FORBIDDEN = 403;
  static const int NOT_FOUND = 404;
  static const int INTERNAL_SERVER_ERROR = 500;

  // Local status codes
  static const int CONNECT_TIMEOUT = -1;
  static const int CANCEL = -2;
  static const int RECIEVE_TIMEOUT = -3;
  static const int SEND_TIMEOUT = -4;
  static const int CACHE_ERROR = -5;
  static const int NO_INTERNET_CONNECTION = -6;
  static const int DEFAULT = -7;
}

enum DataSource {
  SUCCESS,
  NO_CONTENT,
  BAD_REQUEST,
  FORBIDDEN,
  UNAUTORISED,
  NOT_FOUND,
  INTERNAL_SERVER_ERROR,
  CONNECT_TIMEOUT,
  CANCEL,
  RECIEVE_TIMEOUT,
  SEND_TIMEOUT,
  CACHE_ERROR,
  NO_INTERNET_CONNECTION,
  DEFAULT
}

extension DataSourceExtension on DataSource {
  Failure getFailure() {
    switch (this) {
      case DataSource.SUCCESS:
        return const Failure(ResponseCode.SUCCESS, "Sucesso");
      case DataSource.NO_CONTENT:
        return const Failure(ResponseCode.NO_CONTENT, "Sem conteúdo");
      case DataSource.BAD_REQUEST:
        return const Failure(ResponseCode.BAD_REQUEST, "Requisição inválida");
      case DataSource.FORBIDDEN:
        return const Failure(ResponseCode.FORBIDDEN, "Acesso negado");
      case DataSource.UNAUTORISED:
        return const Failure(ResponseCode.UNAUTORISED, "Não autorizado");
      case DataSource.NOT_FOUND:
        return const Failure(ResponseCode.NOT_FOUND, "Não encontrado");
      case DataSource.INTERNAL_SERVER_ERROR:
        return const Failure(ResponseCode.INTERNAL_SERVER_ERROR, "Erro interno do servidor");
      case DataSource.CONNECT_TIMEOUT:
        return const Failure(ResponseCode.CONNECT_TIMEOUT, "Timeout de conexão");
      case DataSource.CANCEL:
        return const Failure(ResponseCode.CANCEL, "Requisição cancelada");
      case DataSource.RECIEVE_TIMEOUT:
        return const Failure(ResponseCode.RECIEVE_TIMEOUT, "Timeout de recebimento");
      case DataSource.SEND_TIMEOUT:
        return const Failure(ResponseCode.SEND_TIMEOUT, "Timeout de envio");
      case DataSource.CACHE_ERROR:
        return const Failure(ResponseCode.CACHE_ERROR, "Erro de cache");
      case DataSource.NO_INTERNET_CONNECTION:
        return const Failure(ResponseCode.NO_INTERNET_CONNECTION, "Sem conexão com a internet");
      case DataSource.DEFAULT:
        return const Failure(ResponseCode.DEFAULT, "Erro desconhecido");
    }
  }
}
```

## 🧪 Testes

### 1. **Teste de Cubit**

#### `test/unit/presentation/cubit/feature_cubit_test.dart`
```dart
import 'package:bloc_test/bloc_test.dart';
import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockFeatureUseCase extends Mock implements FeatureUseCase {}

void main() {
  late FeatureCubit cubit;
  late MockFeatureUseCase mockUseCase;

  setUp(() {
    mockUseCase = MockFeatureUseCase();
    cubit = FeatureCubit(mockUseCase);
  });

  tearDown(() {
    cubit.close();
  });

  group('FeatureCubit', () {
    test('initial state is correct', () {
      expect(cubit.state, equals(FeatureState()));
    });

    blocTest<FeatureCubit, FeatureState>(
      'emits [loading, success] when loadData succeeds',
      build: () {
        when(() => mockUseCase.execute(any()))
            .thenAnswer((_) async => const Right(mockData));
        return cubit;
      },
      act: (cubit) => cubit.loadData(),
      expect: () => [
        FeatureState(status: FeatureStatus.loading),
        FeatureState(status: FeatureStatus.success, data: mockData),
      ],
    );

    blocTest<FeatureCubit, FeatureState>(
      'emits [loading, error] when loadData fails',
      build: () {
        when(() => mockUseCase.execute(any()))
            .thenAnswer((_) async => const Left(mockFailure));
        return cubit;
      },
      act: (cubit) => cubit.loadData(),
      expect: () => [
        FeatureState(status: FeatureStatus.loading),
        FeatureState(status: FeatureStatus.error, failure: mockFailure),
      ],
    );
  });
}
```

### 2. **Teste de Use Case**

#### `test/unit/domain/usecase/feature_usecase_test.dart`
```dart
import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockFeatureRepository extends Mock implements FeatureRepository {}

void main() {
  late FeatureUseCase useCase;
  late MockFeatureRepository mockRepository;

  setUp(() {
    mockRepository = MockFeatureRepository();
    useCase = FeatureUseCase(mockRepository);
  });

  group('FeatureUseCase', () {
    test('should return data when repository call is successful', () async {
      // Arrange
      when(() => mockRepository.getData())
          .thenAnswer((_) async => const Right(mockData));

      // Act
      final result = await useCase.execute(NoParams());

      // Assert
      expect(result, equals(const Right(mockData)));
      verify(() => mockRepository.getData()).called(1);
    });

    test('should return failure when repository call fails', () async {
      // Arrange
      when(() => mockRepository.getData())
          .thenAnswer((_) async => const Left(mockFailure));

      // Act
      final result = await useCase.execute(NoParams());

      // Assert
      expect(result, equals(const Left(mockFailure)));
      verify(() => mockRepository.getData()).called(1);
    });
  });
}
```

## 📱 Padrões e Convenções

### 1. **Estrutura de Tela**

#### `lib/presentation/screens/feature/feature_view.dart`
```dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';

class FeatureView extends StatelessWidget {
  const FeatureView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Feature Title'),
      ),
      body: BlocProvider(
        create: (context) => Get.find<FeatureCubit>()..loadData(),
        child: BlocBuilder<FeatureCubit, FeatureState>(
          builder: (context, state) {
            switch (state.status) {
              case FeatureStatus.loading:
                return const Center(child: CircularProgressIndicator());
              case FeatureStatus.success:
                return _buildSuccessWidget(state.data!);
              case FeatureStatus.error:
                return _buildErrorWidget(state.failure!);
              default:
                return const SizedBox.shrink();
            }
          },
        ),
      ),
    );
  }

  Widget _buildSuccessWidget(List<DataModel> data) {
    return ListView.builder(
      itemCount: data.length,
      itemBuilder: (context, index) {
        return ListTile(
          title: Text(data[index].title),
          subtitle: Text(data[index].description),
        );
      },
    );
  }

  Widget _buildErrorWidget(Failure failure) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error, size: 64, color: Colors.red),
          SizedBox(height: 16),
          Text(failure.message),
          SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => context.read<FeatureCubit>().loadData(),
            child: Text('Tentar Novamente'),
          ),
        ],
      ),
    );
  }
}
```

### 2. **Widget Customizado Base**

#### `lib/presentation/widgets/custom_base_scaffold.dart`
```dart
import 'package:flutter/material.dart';

class CustomBaseScaffold extends StatelessWidget {
  final String title;
  final Widget body;
  final List<Widget>? actions;
  final Widget? floatingActionButton;
  final bool showBackButton;

  const CustomBaseScaffold({
    Key? key,
    required this.title,
    required this.body,
    this.actions,
    this.floatingActionButton,
    this.showBackButton = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(title),
        automaticallyImplyLeading: showBackButton,
        actions: actions,
      ),
      body: SafeArea(child: body),
      floatingActionButton: floatingActionButton,
    );
  }
}
```

### 3. **Repository Pattern**

#### `lib/domain/repository/remote/feature_repository.dart`
```dart
import 'package:dartz/dartz.dart';
import '../../../data/network/failure.dart';
import '../../../data/model/data_model.dart';

abstract class FeatureRepository {
  Future<Either<Failure, List<DataModel>>> getData();
  Future<Either<Failure, DataModel>> getDataById(int id);
  Future<Either<Failure, DataModel>> createData(DataModel data);
  Future<Either<Failure, DataModel>> updateData(DataModel data);
  Future<Either<Failure, void>> deleteData(int id);
}
```

#### `lib/data/repository_impl/remote/feature_repository_impl.dart`
```dart
import 'package:dartz/dartz.dart';
import '../../../domain/repository/remote/feature_repository.dart';
import '../../network/error_handler.dart';
import '../../network/failure.dart';
import '../../network/services/api_service.dart';
import '../../model/data_model.dart';

class FeatureRepositoryImpl implements FeatureRepository {
  final ApiService _apiService;

  FeatureRepositoryImpl(this._apiService);

  @override
  Future<Either<Failure, List<DataModel>>> getData() async {
    try {
      final response = await _apiService.getData();
      if (response.response.statusCode == ResponseCode.SUCCESS) {
        return Right(response.data);
      } else {
        return Left(Failure(response.response.statusCode!,
                           response.response.statusMessage!));
      }
    } catch (error) {
      return Left(ErrorHandler.handle(error).failure);
    }
  }

  @override
  Future<Either<Failure, DataModel>> createData(DataModel data) async {
    try {
      final response = await _apiService.postData(data);
      if (response.response.statusCode == ResponseCode.SUCCESS) {
        return Right(response.data);
      } else {
        return Left(Failure(response.response.statusCode!,
                           response.response.statusMessage!));
      }
    } catch (error) {
      return Left(ErrorHandler.handle(error).failure);
    }
  }

  // Implementar outros métodos...
}
```

### 4. **Data Model Template**

#### `lib/data/model/data_model.dart`
```dart
import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'data_model.g.dart';

@JsonSerializable()
class DataModel extends Equatable {
  final int id;
  final String title;
  final String description;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;

  const DataModel({
    required this.id,
    required this.title,
    required this.description,
    required this.createdAt,
    required this.updatedAt,
  });

  factory DataModel.fromJson(Map<String, dynamic> json) =>
      _$DataModelFromJson(json);

  Map<String, dynamic> toJson() => _$DataModelToJson(this);

  @override
  List<Object?> get props => [id, title, description, createdAt, updatedAt];
}
```

## 🎨 Resources Manager

### 1. **Color Manager**

#### `lib/presentation/resources/color_manager.dart`
```dart
import 'package:flutter/material.dart';

class ColorManager {
  static const Color primary = Color(0xFF2196F3);
  static const Color primaryDark = Color(0xFF1976D2);
  static const Color primaryLight = Color(0xFFBBDEFB);

  static const Color secondary = Color(0xFFFF9800);
  static const Color secondaryDark = Color(0xFFF57C00);
  static const Color secondaryLight = Color(0xFFFFE0B2);

  static const Color success = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFF9800);
  static const Color error = Color(0xFFF44336);
  static const Color info = Color(0xFF2196F3);

  static const Color background = Color(0xFFFAFAFA);
  static const Color surface = Color(0xFFFFFFFF);
  static const Color onPrimary = Color(0xFFFFFFFF);
  static const Color onSecondary = Color(0xFF000000);
  static const Color onBackground = Color(0xFF000000);
  static const Color onSurface = Color(0xFF000000);
  static const Color onError = Color(0xFFFFFFFF);

  static const Color grey = Color(0xFF9E9E9E);
  static const Color darkGrey = Color(0xFF424242);
  static const Color lightGrey = Color(0xFFF5F5F5);
}
```

### 2. **Theme Manager**

#### `lib/presentation/resources/theme_manager.dart`
```dart
import 'package:flutter/material.dart';
import 'color_manager.dart';
import 'font_manager.dart';
import 'styles_manager.dart';
import 'values_manager.dart';

class ThemeManager {
  static ThemeData getLightTheme() {
    return ThemeData(
      // Main colors
      primarySwatch: Colors.blue,
      primaryColor: ColorManager.primary,
      primaryColorLight: ColorManager.primaryLight,
      primaryColorDark: ColorManager.primaryDark,
      disabledColor: ColorManager.grey,
      splashColor: ColorManager.primaryLight,

      // Card theme
      cardTheme: CardTheme(
        color: ColorManager.surface,
        shadowColor: ColorManager.grey,
        elevation: AppSize.s4,
      ),

      // App bar theme
      appBarTheme: AppBarTheme(
        centerTitle: true,
        color: ColorManager.primary,
        elevation: AppSize.s4,
        shadowColor: ColorManager.primaryLight,
        titleTextStyle: getRegularStyle(
          fontSize: FontSize.s16,
          color: ColorManager.onPrimary,
        ),
      ),

      // Button theme
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          textStyle: getRegularStyle(
            color: ColorManager.onPrimary,
            fontSize: FontSize.s17,
          ),
          backgroundColor: ColorManager.primary,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppSize.s12),
          ),
        ),
      ),

      // Text theme
      textTheme: TextTheme(
        displayLarge: getSemiBoldStyle(
          color: ColorManager.onBackground,
          fontSize: FontSize.s16,
        ),
        headlineLarge: getSemiBoldStyle(
          color: ColorManager.onBackground,
          fontSize: FontSize.s16,
        ),
        titleMedium: getMediumStyle(
          color: ColorManager.onBackground,
          fontSize: FontSize.s16,
        ),
        bodyLarge: getRegularStyle(
          color: ColorManager.onBackground,
        ),
        bodySmall: getRegularStyle(
          color: ColorManager.grey,
        ),
      ),

      // Input decoration theme
      inputDecorationTheme: InputDecorationTheme(
        contentPadding: const EdgeInsets.all(AppPadding.p8),
        hintStyle: getRegularStyle(
          color: ColorManager.grey,
          fontSize: FontSize.s14,
        ),
        labelStyle: getMediumStyle(
          color: ColorManager.grey,
          fontSize: FontSize.s14,
        ),
        errorStyle: getRegularStyle(
          color: ColorManager.error,
        ),
        enabledBorder: OutlineInputBorder(
          borderSide: BorderSide(
            color: ColorManager.grey,
            width: AppSize.s1_5,
          ),
          borderRadius: BorderRadius.circular(AppSize.s8),
        ),
        focusedBorder: OutlineInputBorder(
          borderSide: BorderSide(
            color: ColorManager.primary,
            width: AppSize.s1_5,
          ),
          borderRadius: BorderRadius.circular(AppSize.s8),
        ),
        errorBorder: OutlineInputBorder(
          borderSide: BorderSide(
            color: ColorManager.error,
            width: AppSize.s1_5,
          ),
          borderRadius: BorderRadius.circular(AppSize.s8),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderSide: BorderSide(
            color: ColorManager.primary,
            width: AppSize.s1_5,
          ),
          borderRadius: BorderRadius.circular(AppSize.s8),
        ),
      ),
    );
  }

  static ThemeData getDarkTheme() {
    // Implementar tema escuro
    return ThemeData.dark();
  }
}
```

### 3. **Routes Manager**

#### `lib/presentation/resources/routes_manager.dart`
```dart
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../screens/home/<USER>';
import '../screens/feature/feature_view.dart';
import '../../app/di.dart';

class Routes {
  static const String splashRoute = "/";
  static const String homeRoute = "/home";
  static const String featureRoute = "/feature";
  static const String featureDetailsRoute = "/feature/details";
}

class RouteGenerator {
  static Route<dynamic> getPageRoute(RouteSettings routeSettings) {
    final String name = routeSettings.name ?? "";
    final Map<String, dynamic>? args =
        routeSettings.arguments as Map<String, dynamic>?;

    switch (name) {
      case Routes.splashRoute:
        return GetPageRoute(
          settings: routeSettings,
          page: () => SplashView(),
        );

      case Routes.homeRoute:
        initHomeModule();
        return GetPageRoute(
          settings: routeSettings,
          page: () => HomeView(),
        );

      case Routes.featureRoute:
        initFeatureModule();
        return GetPageRoute(
          settings: routeSettings,
          page: () => FeatureView(),
        );

      case Routes.featureDetailsRoute:
        initFeatureModule();
        return GetPageRoute(
          settings: routeSettings,
          page: () => FeatureDetailsView(
            id: args?['id'] ?? 0,
          ),
        );

      default:
        return initialRoute();
    }
  }

  static Route<dynamic> initialRoute() {
    return GetPageRoute(page: () => HomeView());
  }
}
```

## 🔧 Injeção de Dependências

### `lib/app/di.dart`
```dart
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../data/data_source/remote/feature_remote_data_source.dart';
import '../data/network/dio_factory.dart';
import '../data/network/services/api_service.dart';
import '../data/repository_impl/remote/feature_repository_impl.dart';
import '../domain/repository/remote/feature_repository.dart';
import '../domain/usecase/feature/feature_usecase.dart';
import '../presentation/screens/feature/cubit/feature_cubit.dart';

Future<void> initAppModule() async {
  // Shared Preferences
  final sharedPrefs = await SharedPreferences.getInstance();
  Get.put<SharedPreferences>(sharedPrefs);

  // App Preferences
  Get.put<AppPreferences>(AppPreferences(Get.find()));

  // Network
  Get.put<DioFactory>(DioFactory());
  Get.put<ApiService>(ApiService(Get.find<DioFactory>().getDio()));
}

void initFeatureModule() {
  if (!Get.isRegistered<FeatureRemoteDataSource>()) {
    // Data Sources
    Get.put<FeatureRemoteDataSource>(
      FeatureRemoteDataSourceImpl(Get.find()),
    );

    // Repositories
    Get.put<FeatureRepository>(
      FeatureRepositoryImpl(Get.find()),
    );

    // Use Cases
    Get.put<FeatureUseCase>(
      FeatureUseCase(Get.find()),
    );

    // Cubits
    Get.put<FeatureCubit>(
      FeatureCubit(Get.find()),
    );
  }
}

void initHomeModule() {
  if (!Get.isRegistered<HomeCubit>()) {
    // Implementar módulo home
  }
}
```

## 🌍 Internacionalização

### 1. **Language Manager**

#### `lib/presentation/resources/language_manager.dart`
```dart
import 'package:flutter/material.dart';

enum LanguageType { PORTUGUESE, ENGLISH }

const String PORTUGUESE = "pt";
const String ENGLISH = "en";
const String ASSETS_PATH_LOCALISATIONS = "assets/translations";

const Locale PORTUGUESE_LOCAL = Locale("pt", "BR");
const Locale ENGLISH_LOCAL = Locale("en", "US");

extension LanguageTypeExtension on LanguageType {
  String getValue() {
    switch (this) {
      case LanguageType.PORTUGUESE:
        return PORTUGUESE;
      case LanguageType.ENGLISH:
        return ENGLISH;
    }
  }
}
```

### 2. **Strings Manager**

#### `lib/presentation/resources/strings_manager.dart`
```dart
class AppStrings {
  static const String appName = "app_name";
  static const String loading = "loading";
  static const String retry = "retry";
  static const String ok = "ok";
  static const String cancel = "cancel";
  static const String error = "error";
  static const String success = "success";
  static const String noDataFound = "no_data_found";
  static const String connectionError = "connection_error";
  static const String serverError = "server_error";
  static const String unknownError = "unknown_error";

  // Feature specific strings
  static const String featureTitle = "feature_title";
  static const String featureDescription = "feature_description";
}
```

### 3. **Translation Files**

#### `assets/translations/pt-BR.json`
```json
{
  "app_name": "Meu App",
  "loading": "Carregando...",
  "retry": "Tentar Novamente",
  "ok": "OK",
  "cancel": "Cancelar",
  "error": "Erro",
  "success": "Sucesso",
  "no_data_found": "Nenhum dado encontrado",
  "connection_error": "Erro de conexão",
  "server_error": "Erro do servidor",
  "unknown_error": "Erro desconhecido",
  "feature_title": "Título da Funcionalidade",
  "feature_description": "Descrição da funcionalidade"
}
```

#### `assets/translations/en-US.json`
```json
{
  "app_name": "My App",
  "loading": "Loading...",
  "retry": "Try Again",
  "ok": "OK",
  "cancel": "Cancel",
  "error": "Error",
  "success": "Success",
  "no_data_found": "No data found",
  "connection_error": "Connection error",
  "server_error": "Server error",
  "unknown_error": "Unknown error",
  "feature_title": "Feature Title",
  "feature_description": "Feature description"
}
```

## 📋 Boas Práticas

### 1. **Nomenclatura**
- **Classes**: PascalCase (ex: `FeatureRepository`)
- **Métodos e Variáveis**: camelCase (ex: `loadData()`)
- **Constantes**: UPPER_SNAKE_CASE (ex: `API_BASE_URL`)
- **Arquivos**: snake_case (ex: `feature_repository.dart`)

### 2. **Organização de Código**
- Sempre separar responsabilidades por camadas
- Usar interfaces para desacoplar implementações
- Implementar tratamento de erros consistente
- Manter widgets pequenos e focados
- Usar const constructors quando possível

### 3. **Performance**
- Implementar lazy loading para listas grandes
- Usar `const` widgets sempre que possível
- Otimizar rebuilds com `BlocBuilder` específicos
- Implementar cache local quando apropriado
- Usar `ListView.builder` para listas dinâmicas

### 4. **Segurança**
- Nunca commitar chaves de API no código
- Usar variáveis de ambiente para configurações sensíveis
- Implementar validação de entrada adequada
- Usar HTTPS para todas as comunicações
- Implementar timeout adequado para requisições

### 5. **Testes**
- Manter cobertura de testes acima de 80%
- Testar todos os casos de uso críticos
- Usar mocks para dependências externas
- Implementar testes de integração para fluxos principais
- Automatizar execução de testes no CI/CD

---

## 🚀 Conclusão

Este manual fornece uma base sólida para desenvolvimento de aplicações Flutter seguindo Clean Architecture. A estrutura apresentada garante:

- **Manutenibilidade**: Código organizado e fácil de modificar
- **Testabilidade**: Estrutura preparada para testes automatizados
- **Escalabilidade**: Fácil adição de novas funcionalidades
- **Reutilização**: Componentes e padrões reutilizáveis
- **Qualidade**: Seguindo as melhores práticas da comunidade

Adapte este template às necessidades específicas do seu projeto, mantendo sempre os princípios fundamentais da Clean Architecture.
```
```
```
